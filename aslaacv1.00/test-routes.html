<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .route-link {
            display: block;
            padding: 10px;
            margin: 10px 0;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            text-align: center;
        }
        .route-link:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DeviceConfig Route Testing</h1>
        
        <div class="info">
            <strong>Note:</strong> The development server is running on port 3035. 
            Test the DeviceConfig page with different device IDs.
        </div>
        
        <h2>Test Routes:</h2>
        
        <a href="http://localhost:3035/device-config/123456" class="route-link" target="_blank">
            DeviceConfig with ID: 123456
        </a>
        
        <a href="http://localhost:3035/device-config/test123" class="route-link" target="_blank">
            DeviceConfig with ID: test123
        </a>
        
        <a href="http://localhost:3035/device-config/device001" class="route-link" target="_blank">
            DeviceConfig with ID: device001
        </a>
        
        <a href="http://localhost:3035/mqtt-test" class="route-link" target="_blank">
            MQTT Test Page
        </a>
        
        <a href="http://localhost:3035/paho-mqtt" class="route-link" target="_blank">
            Paho MQTT Config Page
        </a>
        
        <h2>Expected Features:</h2>
        <ul>
            <li>✅ WSS/WS protocol toggle</li>
            <li>✅ Broker selection dropdown</li>
            <li>✅ Connection status indicator</li>
            <li>✅ Retry connection button</li>
            <li>✅ Fallback broker mechanism</li>
            <li>✅ Device configuration controls</li>
        </ul>
        
        <h2>Testing Instructions:</h2>
        <ol>
            <li>Click on any DeviceConfig link above</li>
            <li>Check if the page loads without errors</li>
            <li>Try different brokers from the dropdown</li>
            <li>Toggle between WSS and WS protocols</li>
            <li>Click "Connect" to test MQTT connections</li>
            <li>Check browser console for connection logs</li>
        </ol>
    </div>
</body>
</html>
