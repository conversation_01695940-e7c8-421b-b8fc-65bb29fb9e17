# Enhanced Power Button with Visible Schedule Indicator

## Overview

The Home page power button has been enhanced with a clear, visible schedule indicator that transforms it into a scheduler interface. This feature provides an intuitive and discoverable way to access command scheduling without interfering with the existing 3-second countdown system.

## Features

### 🎯 Visible Schedule Indicator
- **Clear Visual Cue**: Animated upward arrow above the power button
- **Glow Effect**: Subtle golden glow with pulsing animation
- **Text Label**: "Schedule" text below the arrow for clarity
- **Tooltip**: Hover tooltip explaining the functionality
- The button transforms from a power icon (⚡) to an alarm icon (⏰) when activated

### 📅 Command Scheduler
- Schedule device commands for future execution
- Support for all device commands: on1, on2, off1, off2
- Date and time picker with validation
- Duration support for SMS devices
- Visual command preview and confirmation

### 🎨 Visual Feedback
- Smooth icon transformation animations
- Color changes to indicate mode (red → yellow)
- Subtle scroll indicator hint
- Hover effects and visual states

## How to Use

### Simple and Intuitive Process
1. **Locate the Schedule Indicator**: Look for the animated golden arrow above the power button with "Schedule" text
2. **Access Scheduler Mode**: Click the golden arrow indicator
3. **Button Transforms**: The power button transforms into an alarm icon with smooth animation
4. **Open Scheduler**: Click the alarm icon to open the scheduling dialog
5. **Schedule Commands**:
   - Select the desired command (on1, on2, off1, off2)
   - Choose date and time for execution
   - Click "Schedule Command" to confirm
6. **Return to Power Mode**: Wait 8 seconds for auto-reset or the button returns to normal mode

### Visual Indicators
- **Golden Arrow**: Animated upward-pointing arrow with glow effect
- **Pulsing Animation**: Gentle pulsing to draw attention without being distracting
- **Text Label**: Clear "Schedule" text for immediate understanding
- **Hover Effects**: Enhanced glow on hover for better interactivity
- **Smooth Transitions**: Framer-motion animations for professional feel

## Technical Implementation

### Components Created

1. **`useScrollTransform` Hook** (`src/hooks/useScrollTransform.js`)
   - Handles multiple interaction methods (double-tap, long-press, corner-tap)
   - Supports both desktop and mobile devices
   - Configurable interaction method and reset timeout

2. **`ScrollablePowerButton` Component** (`src/components/ScrollablePowerButton/`)
   - Replaces the standard power button
   - Integrates gesture detection with visual feedback
   - Uses framer-motion for smooth animations
   - Supports three interaction methods

3. **`SchedulerDialog` Component** (`src/components/SchedulerDialog/`)
   - Modal interface for command scheduling
   - Integrates with existing API endpoints
   - Supports all device types (MQTT/SMS)

4. **`InteractionMethodDemo` Component** (`src/components/InteractionMethodDemo/`)
   - Demonstration of all three interaction methods
   - Helpful for testing and user education

### Integration Points

- **Home.js**: Modified to use the new scrollable power button
- **API Integration**: Uses existing `/api/device/control/` endpoints
- **Styling**: Consistent with existing MUI theme
- **Animations**: Built with framer-motion library

## Configuration Options

### ScrollablePowerButton Props
```javascript
<ScrollablePowerButton
  onPowerClick={handlePowerClick}        // Original power functionality
  onScheduleClick={handleScheduleClick}  // Opens scheduler dialog
  powerLabel="Power Off"                 // Label for power mode
  scheduleLabel="Schedule"               // Label for scheduler mode
  powerIcon="carbon:flash-off"           // Icon for power mode
  scheduleIcon="material-symbols:alarm"  // Icon for scheduler mode
  powerColor="red"                       // Color for power mode
  scheduleColor="warning.main"           // Color for scheduler mode
  disabled={loading}                     // Disable during operations
  showScheduleIndicator={true}           // Show/hide the schedule indicator arrow
/>
```

### Design Benefits

| Aspect | Benefit | Description |
|--------|---------|-------------|
| **Discoverability** | High | Visible indicator makes feature obvious to users |
| **Reliability** | Excellent | No gesture detection issues or conflicts |
| **Accessibility** | Great | Clear visual cue works for all users |
| **Consistency** | Perfect | Matches existing UI design language |
| **Performance** | Optimal | Simple click handler, no complex gesture logic |

## Browser Compatibility

- **Desktop**: All modern browsers with wheel event support
- **Mobile**: iOS Safari, Chrome Mobile, Firefox Mobile
- **Touch Events**: Full support for touch gestures
- **Fallback**: Standard click functionality always available

## Accessibility

- **Keyboard Navigation**: Standard tab navigation supported
- **Screen Readers**: Proper ARIA labels and descriptions
- **Visual Indicators**: Clear visual feedback for all states
- **Alternative Access**: Click functionality preserved for all users

## Future Enhancements

- [ ] Add haptic feedback for mobile devices
- [ ] Implement scheduled command history view
- [ ] Add recurring schedule options
- [ ] Support for custom command sequences
- [ ] Integration with device status for smart scheduling

## Troubleshooting

### Common Issues

1. **Scroll not detected**: Ensure the cursor is directly over the button
2. **Mobile gestures not working**: Try a quicker swipe motion
3. **Button stuck in alarm mode**: Wait for auto-reset or refresh page
4. **Scheduler not opening**: Check device permissions and network connection

### Debug Mode

To enable debug logging, add this to your browser console:
```javascript
localStorage.setItem('debug-scroll-button', 'true');
```

## Dependencies

- `framer-motion`: Animation library
- `@mui/x-date-pickers`: Date/time picker components
- `dayjs`: Date manipulation library
- Existing project dependencies (React, MUI, etc.)
