import { useState, useEffect } from "react";
// @mui
import {
  Card,
  Table,
  Button,
  Checkbox,
  TableRow,
  TableBody,
  TableCell,
  Container,
  TableContainer,
  TablePagination,
  Divider,
  Skeleton,
} from "@mui/material";
// components
import Page from "../../components/Page";
import Scrollbar from "../../components/Scrollbar";
import SearchNotFound from "../../components/SearchNotFound";
import React, { useRef } from 'react';
import { DownloadTableExcel } from 'react-export-table-to-excel';

// sections
import {
  DeviceListToolbar,
} from "../../sections/admin/device";
import axios from "../../utils/axios";
import Layout from "../../layout";
import TableListHead from "../../sections/admin/user/TableListHead";
import { Icon } from "@iconify/react";

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: "paid", label: "Paid" },
  { id: "phoneNumber", label: "Phone Number" },
  { id: "CarModel", label: "CarModel" },
  { id: "location", label: "Location" },
  { id: "AvialableTime", label: "Avialable time" },
  { id: "Spare key", label: "Spare key" },
  { id: "Installed", label: "Installed" },
  { id: "" },
];

// ----------------------------------------------------------------------

export default function OrderList() {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState([]);
  const [page, setPage] = useState(0);
  const [order, setOrder] = useState("asc");
  const [selected, setSelected] = useState([]);
  const [orderBy, setOrderBy] = useState("name");
  const [filterName, setFilterName] = useState("");
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = orders.map((n) => n._id); //  n.name);
      setSelected(newSelecteds);
      return;
    }
    setSelected([]);
  };

  const handleClick = (_id) => {
    const selectedIndex = selected.indexOf(_id);
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, _id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterByName = (filterName) => {
    setFilterName(filterName);
    setPage(0);
  };


  const handleDisableMultiDevice = async (status) => {
    const list = orders.slice();
    // console.log(selected);
    list.forEach((order, index) => {
      if (selected.includes(order._id)) {
        order.status = status;
      }
    });
    const res = await axios.post("/api/admin/user/change-active", {
      ids: selected,
      status,
    });
    if (res.status === 200 && res.data.success) {
      setOrders(list);
    }
  };

  useEffect(() => {
    setLoading(true)
    axios
      .get("/api/admin/order/list")
      .then((res) => {
        setLoading(false);
        setOrders(res.data.orders);

        // console.log(res);
      })
      .catch((err) => { setLoading(false) }).finally(() => setLoading(false));
  }, []);
  const emptyRows =
    page > 0 ? Math.max(0, (1 + page) * rowsPerPage - orders.length) : 0;

  const filteredDevices = applySortFilter(
    orders,
    getComparator(order, orderBy),
    filterName
  );

  const isNotFound = !filteredDevices?.length && Boolean(filterName);
  const tableRef = useRef(null);

  return (
    <Page title="Device Manage">
      <Container sx={{ py: { xs: 12 } }}>
        <Layout />
        <Card>
          <DeviceListToolbar
            numSelected={selected.length}
            filterName={filterName}
            onFilterName={handleFilterByName}
            onDisableDevice={() => handleDisableMultiDevice("inactive")}
            onEnableDevice={() => handleDisableMultiDevice("active")}
          />
          <Divider />
          <Scrollbar>
            {
              loading &&
              [1, 2, 3, 4, 5].map((index) => (
                <Skeleton height={30} key={index} animation='pulse' />
              ))
            }
            {!loading &&
              <TableContainer sx={{ minWidth: 650, maxHeight: "70vh" }}>
                <Table size="small" stickyHeader ref={tableRef}>
                  <TableListHead
                    order={order}
                    orderBy={orderBy}
                    headLabel={TABLE_HEAD}
                    rowCount={orders?.length}
                    numSelected={selected.length}
                    onRequestSort={handleRequestSort}
                    onSelectAllClick={handleSelectAllClick}
                  />
                  <TableBody>
                    {filteredDevices
                      ?.slice(
                        page * rowsPerPage,
                        page * rowsPerPage + rowsPerPage
                      )
                      ?.map((row) => {
                        const {
                          phoneNumber,
                          AvialableTime,
                          CarModel,
                          address,
                          _id,
                          isSpareKey,
                          isInstalled,
                          paid,
                        } = row;

                        const isItemSelected = selected.indexOf(_id) !== -1;

                        return (
                          <TableRow
                            hover
                            key={_id}
                            tabIndex={-1}
                            role="checkbox"
                            selected={isItemSelected}
                            aria-checked={isItemSelected}
                          >
                            <TableCell padding="checkbox">
                              <Checkbox
                                checked={isItemSelected}
                                onClick={() => handleClick(_id)}
                              />
                            </TableCell>
                            <TableCell align="left">{paid ? <Icon icon="flat-color-icons:paid"></Icon> : <Icon icon='mdi:question-mark-circle-outline'></Icon>}</TableCell>
                            <TableCell align="left">{phoneNumber}</TableCell>
                            <TableCell align="left">{CarModel}</TableCell>
                            <TableCell align="left">{address}</TableCell>
                            <TableCell align="left">{AvialableTime}</TableCell>
                            <TableCell align="left">{isSpareKey ? 'байгаа' : 'байхгүй'}</TableCell>
                            <TableCell align="left">{isInstalled ? <Icon icon="entypo:install" color='green'></Icon> : <Icon icon='entypo:uninstall'></Icon>}</TableCell>
                          </TableRow>
                        );
                      })}
                    {emptyRows > 0 && (
                      <TableRow style={{ height: 53 * emptyRows }}>
                        <TableCell colSpan={6} />
                      </TableRow>
                    )}
                  </TableBody>
                  {isNotFound && (
                    <TableBody>
                      <TableRow>
                        <TableCell align="center" colSpan={6} sx={{ py: 3 }}>
                          <SearchNotFound searchQuery={filterName} />
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  )}
                </Table>
              </TableContainer>
            }
          </Scrollbar>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50, 100]}
            component="div"
            count={filteredDevices?.length || 0}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(e, page) => setPage(page)}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Card>
      </Container>
      <div>
        <DownloadTableExcel
          filename="orders table"
          sheet="orders"
          currentTableRef={tableRef.current}
        >

          <Button> Export excel </Button>

        </DownloadTableExcel>
      </div>
    </Page>
  );
}

// ----------------------------------------------------------------------

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === "desc"
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
  // console.log(array);
  const stabilizedThis = array?.map((el, index) => [el, index]);
  stabilizedThis?.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });
  if (query) {

    return array.filter(
      (_order) => (_order.phoneNumber.indexOf(query.toLowerCase()) !== -1)
    );
  }
  return stabilizedThis?.map((el) => el[0]);
}
