import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import Generator from 'license-key-generator';
// form
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import AdapterDateFns from '@mui/lab/AdapterDateFns';
import { LoadingButton, MobileDatePicker, LocalizationProvider } from '@mui/lab';
import { Stack,  Grid, Container, Typography, Divider, TextField, Button } from '@mui/material';

import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// hooks
import useAuth from '../../hooks/useAuth';
// components
import { FormProvider, RHFSelect, RHFTextField } from '../../components/hook-form';
import CarFront from '../../components/CarFront';
import Page from '../../components/Page';
import axios from '../../utils/axios';
import useResponsive from '../../hooks/useResponsive';
import Layout from '../../layout';

// ----------------------------------------------------------------------

export default function DeviceEdit() {
  const {user} =useAuth();
  const isMobile = useResponsive('down', 'sm');
  const navigate = useNavigate();
  const { id } = useParams();
  const [device, setDevice] = useState(null);
  const { enqueueSnackbar } = useSnackbar();
  const EditSchema = Yup.object().shape({

  });

  const defaultValues = {
    deviceNumber: device?.deviceNumber || '',
    type: device?.type || '4g', 
    licenseKey: user?.licenseKey || '',
    expired: user?.expired || 0,
  };

  const methods = useForm({
    resolver: yupResolver(EditSchema),
    defaultValues,
  });

  const {
    watch,
    setValue,
    control,
    handleSubmit,
    formState: {  isSubmitting },
  } = methods;
  const values = watch();
  const onSubmit = async (data) => {
     
    
    const res = await axios.post(`/api/device/set-by-admin/${id}`, {...data,phoneNumber:device.phoneNumber });
    try {
      if (res.data.success) {
        enqueueSnackbar('Device is changed', { variant: 'success' });
        navigate('/admin/device-manage');
      }
    }
    catch (err) {
      // console.log(err);
    }

  };
  const handleLicenseKey = async () => {
    const options = {
      type: "random",
      length: 12,
      group: 3,
      split: '-',
      splitStatus: true
    }
    const code = new Generator(options);

    code.get((err, key) => {
      setValue('licenseKey', key);
      setValue('expired',new Date((Date.now()+ 3600 * 24 *30 * 1000)));
    });




  }
  useEffect(() => {

    axios.post(`/api/device/edit/${id}`).then(res => {
  
      if (res.data.device) {
        const d = res.data.device;
        const device = { ...d, expired: d.user[0].expired, licenseKey: d.user[0].licenseKey };
        setValue('deviceNumber', device.deviceNumber);
        setValue('type', device.type);
        setValue('licenseKey', device.licenseKey);
        setValue('expired', device.expired);
        setDevice(device);
      }

    }).catch(err => {

    })
  }, [id,setValue]);
  return (
    <Page title="Device Edit">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'md'}>
        <Grid container spacing={3}  >
          {!isMobile &&
            <Grid item xs={12} sm={6} textAlign={"center"}>
              <CarFront />
              <Typography variant='h4' sx={{ pt: 4 }}>
                user phone number:<br />{device?.phoneNumber || ' not available'}
              </Typography>
            </Grid>
          }
          <Grid item xs={12} sm={6} >
            <Typography variant='h4'>
              Device Information
            </Typography>
            <Divider sx={{ mb: 4, mt: 1 }} />
            <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
              <Stack spacing={3}>

                <RHFTextField name="deviceNumber" label="Device Number" value={values.deviceNumber} />
                <RHFSelect name="type" label="Device Type">
                  <option value="4g">4G Net</option>
                  <option value="sms">SMS</option>
                </RHFSelect>
                <Stack direction={"row"}>
                  <RHFTextField name="licenseKey" label="License Key" />
                  <Button variant='outlined' sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048', color: "white" }} onClick={handleLicenseKey}>Get</Button>
                </Stack>

                <Controller
                  name="expired"
                  control={control}
                  render={({ field }) => (
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <MobileDatePicker

                        {...field}
                        minDate={new Date()}
                        inputFormat="dd MMM yyyy"
                        label="Expire Date"
                        renderInput={(params) => <TextField  {...params} fullWidth />}
                      />
                    </LocalizationProvider>
                  )}
                /> 
                <LoadingButton fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                  type="submit" variant="contained" loading={isSubmitting}>
                  Save Changes.
                </LoadingButton>
              </Stack>

            </FormProvider>
          </Grid>
        </Grid>
      </Container>
    </Page>
  );
}
