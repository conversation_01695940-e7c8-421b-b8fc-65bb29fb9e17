import { sentenceCase } from 'change-case';
import { useState, useEffect, useMemo } from 'react';
// @mui
import { useTheme } from '@mui/material/styles';
import {
  Card,
  Table,
  Button,
  Checkbox,
  TableRow,
  TableBody,
  TableCell,
  Container,
  TableContainer,
  TablePagination,
  Divider,
} from '@mui/material';
// components
import Page from '../../components/Page';
import Label from '../../components/Label';
import Scrollbar from '../../components/Scrollbar';
import SearchNotFound from '../../components/SearchNotFound';

import axios from "../../utils/axios";
import Layout from '../../layout';
import TableListHead from '../../sections/admin/user/TableListHead';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'username', label: 'Username' },
  { id: 'phoneNumber', label: 'Phone Number' },
  { id: 'count', label: 'Devices' },
  { id: 'licenseKey', label: 'License Key' },
  { id: 'expired', label: 'Expired' },
  { id: 'type', label: 'Type' },
  { id: 'status', label: 'Status' },
  { id: '' },
];

// ----------------------------------------------------------------------

export default function UserList() {
  const theme = useTheme();
  const [users, setUsers] = useState([]);
  const [page, setPage] = useState(0);
  const [order, setOrder] = useState('asc');
  const [selected, setSelected] = useState([]);
  const [orderBy, setOrderBy] = useState('name');
  const [filterName, setFilterName] = useState('');
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleRequestSort = async (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  
    const res = await axios.get('/api/admin/user/list');
    setUsers(res.data.users);
  };
  

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = users.map((n) => n._id);
      setSelected(newSelecteds);
      return;
    }
    setSelected([]);
  };

  const handleClick = (_id) => {
    const isItemSelected = selected.includes(_id);
    const newSelected = isItemSelected
      ? selected.filter((id) => id !== _id)
      : [...selected, _id];
    setSelected(newSelected);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterByName = (filterName) => {
    setFilterName(filterName);
    setPage(0);
  };

  const handleDisableUser = async (_id, status) => {
    const list = [...users];
    const index = list.findIndex((user) => user._id === _id);
    if (index !== -1) {
      list[index].status = status;
      const res = await axios.post('/api/admin/user/change-active', {
        ids: [_id],
        status,
      });
      if (res.status === 200 && res.data.success) {
        setUsers(list);
      }
    }
  };

    const handleDisableMultiDevice = async (status) => {
  const list = users.slice();
  // console.log(selected);
  list.forEach((user, index) => {
    if (selected.includes(user._id)) {
      user.status = status;
    }
  });

  const promises = selected.map((id) =>
    axios.post('/api/admin/user/change-active', { ids: [id], status })
  );
  await Promise.all(promises);

  setUsers(list);
};

    useEffect(() => {
        const fetchUsers = async () => {
          try {
            const res = await axios.get('/api/admin/user/list');
            setUsers(res.data.users);
          } catch (err) {
            console.log(err);
          }
        };
        fetchUsers();
      }, []);
    const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - users.length) : 0;

const filteredDevices = useMemo(() => {
  return applySortFilter(users, getComparator(order, orderBy), filterName);
}, [users, order, orderBy, filterName]);

    const isNotFound = !filteredDevices.length && Boolean(filterName);

    return (
        <Page title="Device Manage">
            <Container sx={{ py: { xs: 12 } }}>
                <Layout />
                <Card >
                    <DeviceListToolbar
                        numSelected={selected.length}
                        filterName={filterName}
                        onFilterName={handleFilterByName}
                        onDisableDevice={() => handleDisableMultiDevice("inactive")}
                        onEnableDevice={() => handleDisableMultiDevice("active")}
                    />
                    <Divider />
                    <Scrollbar>
                        <TableContainer sx={{ minWidth: 650, maxHeight: '70vh' }}>
                            <Table size="small" stickyHeader>
                                <TableListHead
                                    order={order}
                                    orderBy={orderBy}
                                    headLabel={TABLE_HEAD}
                                    rowCount={users.length}
                                    numSelected={selected.length}
                                    onRequestSort={handleRequestSort}
                                    onSelectAllClick={handleSelectAllClick}
                                />
                                <TableBody>
                                {filteredDevices.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(({ phoneNumber, licenseKey, expired, status, _id, username, devices }) => {
  const isItemSelected = selected.indexOf(_id) !== -1;

  return (
    <TableRow
      hover
      key={_id}
      tabIndex={-1}
      role="checkbox"
      selected={isItemSelected}
      aria-checked={isItemSelected}
    >
      <TableCell padding="checkbox">
        <Checkbox checked={isItemSelected} onClick={() => handleClick(_id)} />
      </TableCell>
      <TableCell align="left">{username}</TableCell>
      <TableCell align="left">{phoneNumber}</TableCell>
      <TableCell align="left">{devices?.length}</TableCell>
      <TableCell align="left">{licenseKey}</TableCell>
      <TableCell align="left">{expired && expired.indexOf("T") !== -1 ? expired.split("T")[0] : ""}</TableCell>
      <TableCell align="left">
        <Label
          color={(status === 'active' && 'success') || (status === 'trial' && 'warning') || 'error'}
        >
          {sentenceCase(status)}
        </Label>
      </TableCell>
      <TableCell align="right">
        {/* <DeviceMoreMenu deviceId={deviceID} onDisable={() => handleDisableUser(_id, "inactive")} onEnable={() => handleDisableUser(_id, "active")} /> */}
      </TableCell>
    </TableRow>
  );
})}

                                    {emptyRows > 0 && (
                                        <TableRow style={{ height: 53 * emptyRows }}>
                                            <TableCell colSpan={6} />
                                        </TableRow>
                                    )}
                                </TableBody>
                                {isNotFound && (
                                    <TableBody>
                                        <TableRow>
                                            <TableCell align="center" colSpan={6} sx={{ py: 3 }}>
                                                <SearchNotFound searchQuery={filterName} />
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                )}
                            </Table>
                        </TableContainer>
                    </Scrollbar>
                    <TablePagination
                        rowsPerPageOptions={[5, 10, 25, 50]}
                        component="div"
                        count={filteredDevices.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={(e, page) => setPage(page)}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                </Card>
            </Container>
        </Page>
    );
}

// ----------------------------------------------------------------------

function descendingComparator(a, b, orderBy) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}

function getComparator(order, orderBy) {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  if (query) {
    const statusQuery = query.toLowerCase();
    return array.filter((user) => {
      const status = user.status.toLowerCase();
      return (
        user.username.toLowerCase().indexOf(query) !== -1 ||
        user.phoneNumber.toLowerCase().indexOf(query) !== -1 ||
        user.devices?.length.toString().indexOf(query) !== -1 ||
        user.licenseKey.toLowerCase().indexOf(query) !== -1 ||
        user.expired?.indexOf(query) !== -1 ||
        (statusQuery === "verified" && status === "active") ||
        (statusQuery === "pending" && status !== "active")
      );
    });
  }

  return stabilizedThis.map((el) => el[0]);
}
