import { useState, useEffect } from "react";
// @mui
import {
  Card,
  Table,
  Button,
  TableRow,
  TableBody,
  TableCell,
  Container,
  TableContainer,
  TablePagination,
  Divider,
  Skeleton,
  Tooltip,
  IconButton,
} from "@mui/material";
// components
import Page from "../../components/Page";
import Label from "../../components/Label";
import Scrollbar from "../../components/Scrollbar";
import SearchNotFound from "../../components/SearchNotFound";
import React, { useRef } from 'react';
import { DownloadTableExcel } from 'react-export-table-to-excel';

// sections
import {
  
  DeviceListToolbar,
  
} from "../../sections/admin/device";
import axios from "../../utils/axios";
import Layout from "../../layout";

import { fDate } from "../../utils/formatUtils";
import { fShortenNumber } from "../../utils/formatUtils";
import NormalTableListHead from "../../sections/admin/wallet/NormalTableListHead";
import { Icon } from "@iconify/react";

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: "user", label: "Client" },
  { id: "phoneNumber", label: "Mobile" },
  { id: "date", label: "Date" },
  { id: "mode", label: "Mode" },
  { id: "before", label: "Bf Balance" },
  { id: "amount", label: "Amount" },
  { id: "" },
];

// ----------------------------------------------------------------------

export default function TransactionLogs() {
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [page, setPage] = useState(0);
  const [order, setOrder] = useState("asc");
  const [selected, setSelected] = useState([]);
  const [orderBy, setOrderBy] = useState("user");
  const [filterName, setFilterName] = useState("");
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = transactions.map((n) => n._id); //  n.name);
      setSelected(newSelecteds);
      return;
    }
    setSelected([]);
  };


  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterByName = (filterName) => {
    setFilterName(filterName);
    setPage(0);
  };


  const handleDisableMultiDevice = async (status) => {

  };

  useEffect(() => {
    setLoading(true)
    axios
      .get("/api/admin/wallet-transactions")
      .then((res) => {
        setLoading(false);
        setTransactions(res.data.transactions);

        // console.log(res);
      })
      .catch((err) => { setLoading(false) }).finally(() => setLoading(false));
  }, []);
  const emptyRows =
    page > 0 ? Math.max(0, (1 + page) * rowsPerPage - transactions.length) : 0;

  const filteredTransactions = applySortFilter(
    transactions,
    getComparator(order, orderBy),
    filterName
  );

  const isNotFound = !filteredTransactions?.length && Boolean(filterName);
  const tableRef = useRef(null);

  return (
    <Page title="Transactions">
      <Container sx={{ py: { xs: 12 } }}>
        <Layout />
        <Card>
          <DeviceListToolbar
            numSelected={selected.length}
            filterName={filterName}
            onFilterName={handleFilterByName}
            onDisableDevice={() => handleDisableMultiDevice("inactive")}
            onEnableDevice={() => handleDisableMultiDevice("active")}
          />
          <Divider />
          <Scrollbar>
            {
              loading &&
              [1, 2, 3, 4, 5].map((index) => (
                <Skeleton height={30} key={index} animation='pulse' />
              ))
            }
            {!loading &&
              <TableContainer sx={{ minWidth: 650, maxHeight: "70vh" }}>
                <Table size="small" stickyHeader ref={tableRef}>
                  <NormalTableListHead
                    order={order}
                    orderBy={orderBy}
                    headLabel={TABLE_HEAD}
                    rowCount={transactions?.length}
                    numSelected={selected.length}
                    onRequestSort={handleRequestSort}
                    onSelectAllClick={handleSelectAllClick}
                  />
                  <TableBody>
                    {filteredTransactions
                      ?.slice(
                        page * rowsPerPage,
                        page * rowsPerPage + rowsPerPage
                      )
                      ?.map((row) => {
                        const {
                          username,
                          phoneNumber,
                          ts,
                          amount,
                          _id,
                          before,
                          mode,
                          description
                        } = row;



                        return (
                          <TableRow
                            hover
                            key={_id}
                            tabIndex={-1}
                          >
                            <TableCell align="left">{username}</TableCell>
                            <TableCell align="left">{phoneNumber}</TableCell>
                            <TableCell align="left">{fDate(ts)}</TableCell>
                            <TableCell align="left">
                              <Label
                                color={mode === 'withdraw' ? 'success' : 'error'} >
                                {mode}
                              </Label>
                            </TableCell>
                            <TableCell align="left">{fShortenNumber(before)}</TableCell>
                            <TableCell align="left">{fShortenNumber(amount)}</TableCell>
                            <TableCell align="left"><Tooltip title = {description}><IconButton><Icon icon="ic:outline-remove-red-eye" width={20}></Icon></IconButton></Tooltip></TableCell>

                          </TableRow>
                        );
                      })}
                    {emptyRows > 0 && (
                      <TableRow style={{ height: 53 * emptyRows }}>
                        <TableCell colSpan={6} />
                      </TableRow>
                    )}
                  </TableBody>
                  {isNotFound && (
                    <TableBody>
                      <TableRow>
                        <TableCell align="center" colSpan={6} sx={{ py: 3 }}>
                          <SearchNotFound searchQuery={filterName} />
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  )}
                </Table>
              </TableContainer>
            }
          </Scrollbar>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50, 100]}
            component="div"
            count={filteredTransactions?.length || 0}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(e, page) => setPage(page)}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Card>
      </Container>
      <div>
        <DownloadTableExcel
          filename="transaction table"
          sheet="transactions"
          currentTableRef={tableRef.current}
        >

          <Button> Export excel </Button>

        </DownloadTableExcel>
      </div>
    </Page >
  );
}

// ----------------------------------------------------------------------

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === "desc"
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
  // console.log(array);
  const stabilizedThis = array?.map((el, index) => [el, index]);
  stabilizedThis?.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });
  if (query) {

    return array.filter(
      (_transaction) => (_transaction.ts.toLowerCase().includes(query.toLowerCase()) || _transaction.mode.toLowerCase().includes(query.toLowerCase()) ||_transaction.username.toLowerCase().includes(query.toLowerCase()) || _transaction.phoneNumber.indexOf(query.toLowerCase()) !== -1)
    );
  }
  return stabilizedThis?.map((el) => el[0]);
}
