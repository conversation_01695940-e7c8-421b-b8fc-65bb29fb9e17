
import { useSnackbar } from 'notistack';

import { <PERSON><PERSON>,  <PERSON>rid,  Container, Typo<PERSON>, Divider, TextField, Box, Button, Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// hooks
import useAuth from '../hooks/useAuth';
// components

import Page from '../components/Page';
import axios from '../utils/axios';
import Layout from '../layout';
import { maskPhoneNumber } from '../utils/phoneUtils';


// ----------------------------------------------------------------------

export default function ConfigureDrivers() {
    const {  user } = useAuth();
    const [currentDeviceNumber, setCurrentDeviceNumber] = useState(user?.device?.deviceNumber);
    const [currentDeviceType,setCurrentDeviceType] = useState(user?.device?.type);
    const [index,setIndex] = useState("1st");
    const [phoneNumber,setPhoneNumber] = useState('');
    const { t } = useTranslation();
    const [configuredDrivers, setConfigureDrivers] = useState({ drivers: [] });

    const { enqueueSnackbar } = useSnackbar();
    const [updated, setUpdated] = useState(0);

    const onSubmit = async () => {
        const data = {
            phoneNumber,
            deviceNumber: currentDeviceNumber,
            type: currentDeviceType,
            index,
        }

        const res = await axios.post(`/api/device/configure`, { ...data });
        try {
            if (res?.data?.success) {
                enqueueSnackbar(res?.data?.message, { variant: 'success' });
                setUpdated(updated + 1);
            }
            else {
                enqueueSnackbar(res?.data?.message, { variant: 'error' });
            }
        }
        catch (err) {
            // console.log(err);
        }

    };
    const onDelete = async (id) => {

        const response = await axios.post('api/device/configured-delete', { id: id });
        if (response.status === 200) {

            setConfigureDrivers({
                drivers: response.data.drivers
            })
        }
    }
    useEffect(() => {
        // console.log(user.devices)
        axios.post(`/api/device/configured-drivers`,{deviceNumber:currentDeviceNumber}).then(res => {
            if (res.status === 200 && res.data.success && res.data.drivers) {
                setConfigureDrivers({
                    drivers: res.data.drivers
                })
            }
            // console.log(user)
        }).catch(err => {

        })
    }, [currentDeviceNumber]);
    const onChangeDevice = (e)=>{
        try{
            const number = e.target.value;
            const type = user?.devices?.filter((d)=>d.deviceNumber === number)[0]?.type;
            setCurrentDeviceNumber(number);
            setCurrentDeviceType(type);


        }
        catch(err){
            console.log(err)
        }

    }
    return (
        <Page title="Device registration">
            <Layout />
            <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
                <Grid container spacing={3}  >

                    <Grid item xs={12}  >
                        <Typography variant='h4'>
                            {t("device_profile.device_information")}
                        </Typography>
                        <Divider sx={{ mb: 4, mt: 1 }} />
                        <Stack spacing={3}>
                            <FormControl>
                                <InputLabel id="type-select-label">{t("words.device")}</InputLabel>
                                <Select value={currentDeviceNumber} label={t("words.device")} onChange={(onChangeDevice)} labelId="type-select-label">
                                    {user?.devices?.map((d,index)=>(
                                        <MenuItem value={d.deviceNumber} key = {index}>{d.deviceNumber}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                            <FormControl>
                                <InputLabel id="type-select-label">{t("words.device_type")}</InputLabel>
                                <Select value={currentDeviceType} label={t("words.device_type")} onChange=
                                {(e) => (setCurrentDeviceType(e.target.value))}
                                disabled
                                labelId="type-select-label">
                                    <MenuItem value="4g">4G</MenuItem>
                                    <MenuItem value="sms">SMS</MenuItem>
                                </Select>
                            </FormControl>
                            <FormControl>
                                <InputLabel id="type-select-label">{t("words.driver_index")}</InputLabel>
                                <Select value={index} label={t("words.driver_index")} onChange={(e) => (setIndex(e.target.value))} labelId="type-select-label">
                                    <MenuItem value="1st">1st Number</MenuItem>
                                    <MenuItem value="2nd">2nd Number</MenuItem>
                                    <MenuItem value="3rd">3rd Number</MenuItem>
                                </Select>
                            </FormControl>
                            <TextField value={phoneNumber} label={`${currentDeviceType === '4g'?'Driver':'Phone'} Number`} onChange={(e) => (setPhoneNumber(e.target.value))} />
                            {/* {
                                newDevice.type === "sms" ?
                                    <TextField value={user?.device?.deviceNumber} label={t("words.4g_device_number")} disabled onChange={(e) => (setNewDevice({ ...newDevice, deviceNumber: user?.device?.deviceNumber[0] }))} />
                                    :
                                    <TextField value={newDevice.deviceNumber} label={t("words.4g_device_number")} onChange={(e) => (setNewDevice({ ...newDevice, deviceNumber: e.target.value }))} />
                            } */}

                            <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                onClick={onSubmit} variant="contained" >
                                {t("words.proceed")}
                            </Button>
                        </Stack>


                        <Typography variant='h4' sx={{ mt: 4 }}>
                            {t("configure_drivers.numbers_of_drivers")}
                        </Typography>
                        <Divider sx={{ mb: 4, mt: 1 }} />
                        {
                            configuredDrivers?.drivers?.map((driver, index) => {
                                return (

                                    <Box display="flex" justifyContent="space-between" key={index}>
                                        <Typography variant='h4'>
                                            {maskPhoneNumber(driver.phoneNumber)}
                                        </Typography>
                                        <Box display="flex" alignItems="center" onClick={() => onDelete(driver._id)} sx={{ cursor: "pointer" }}>
                                            <DeleteIcon sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048', color: "#38b1ff", mr: "10px" }} />
                                            <Typography variant='h4'>
                                                {t("words.delete")}
                                            </Typography>
                                        </Box>
                                    </Box>

                                )
                            })
                        }

                    </Grid>
                </Grid>
            </Container>

        </Page>
    );
}
