import { Accordion, AccordionDetails, AccordionSummary, Box,  Container, Grid, IconButton, Stack, TextField, Tooltip, Typography } from "@mui/material";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { useTranslation } from 'react-i18next';
import {  useState } from "react";
import { LoadingButton } from "@mui/lab";
import { useSnackbar } from "notistack";

import axios from '../utils/axios';

import useAuth from "../hooks/useAuth";
import Page from "../components/Page";
import Layout from "../layout";
import { HOST_API } from "../config";
import { PaymentDialog } from './PaymentDialog';
import { fDateTime } from "../utils/formatUtils";
import Label from "../components/Label";
import { fShortenNumber } from "../utils/formatUtils";
import { Icon } from "@iconify/react";


const verificationStatus = ['no-license', 'pending', 'verified'];

export default function DriverProfile() {
    const { user, initialize } = useAuth();
    const verified = user?.driverLicenseVerification || 0;
    const { t } = useTranslation();
    const { enqueueSnackbar } = useSnackbar();
    const [paymentRequest, setPaymentRequest] = useState(false);
    const [bankList, setBankList] = useState([]);
    const [qrImage, setQrImage] = useState();
    const [loading, setLoading] = useState(false);
    const [bankName, setBankName] = useState(user?.wallet?.bankName || '');
    const [bankAccount, setBankAccount] = useState(user?.wallet?.bankAccount || '');

    const [profile, setProfile] = useState({
        username: user?.username,
        address: user?.address,
        description: user?.description,

        // driverLicenseFile: user?.driverLicenseFile || '',
    })
    const [image, setImage] = useState(user?.driverLicenseFile || '');
    const [payAmount, setPayAmount] = useState(10000);

    const handleQPay = async () => {
        if (payAmount < 1000) {
            enqueueSnackbar('Deposit amount can not less than 1000', { variant: 'error' })
            return;
        }
        const response = await axios.post(`/api/device/extend-balance`, { totalCost: payAmount, page: 'balance' });
        if (response.status === 200) {
            if (response.data.data && response.data.data.bankList) {
                //  console.log(response.data.data);
                setQrImage(response.data.data.bankList.qr_image)
                setBankList(response.data.data.bankList.urls);
                setPaymentRequest(true);
            }
        }
    }
    const imageChange = (event) => {
        if (event.target.files && event.target.files.length > 0) {
            setImage(event.target.files[0]);
        }
    }
    const handleSubmit = () => {
        if (typeof image === 'object') {
            const formData = new FormData();
            formData.append("username", profile.username);
            formData.append("address", profile.address);
            formData.append("description", profile.description);
            formData.append("driverLicenseFile", image);
            axios.post('/api/device/set-driver-profile', formData).then(res => {
                if (res.status === 200 && res.data?.success){
                    enqueueSnackbar('Submitted successful', { variant: 'success' })
                    initialize();
                }
                    
                else{
                    enqueueSnackbar('Whoops! please try again', { variant: 'error' })
                    console.log(res.data.err);
                }
                    
            })
        }
        else {
            enqueueSnackbar('Select Driver License File with image type', { variant: 'error' })
        }

    }

    const handleBank = () => {

        axios.post('/api/auth/set-bank', { bankName, bankAccount }).then(res => {
            if (res.status === 200 && res.data?.success) {
                enqueueSnackbar('Saved successful', { variant: 'success' })
            }
            else {
                enqueueSnackbar('Whoops! please try again', { variant: 'error' })
            }
        })
    }
    const handleWithdraw = () => {

        if (payAmount < 1000 || payAmount > user?.balance) {
            enqueueSnackbar('Withdraw amount can not less than 1000 or greater than user balance', { variant: 'error' })
            return;
        }
        setLoading(true)
        axios.post('/api/auth/request-withdraw', { payAmount }).then(res => {
            if (res.status === 200 && res.data?.success) {
                enqueueSnackbar('Submitted your withdraw request', { variant: 'success' });
                initialize();
            }
            else {
                enqueueSnackbar('Whoops! please try again', { variant: 'error' })
            }
        }).finally(() => setLoading(false))
    }

    return (
        <Page title="Driver Profile">
            <Layout />
            <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
                <form>
                    <Stack justifyContent={'center'} alignItems={'center'} sx={{ width: '100%' }}>
                        <Accordion sx={{ width: '100%' }}  >
                            <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}

                            >
                                <Stack direction="row" justifyContent={'space-between'} sx={{ width: '100%' }} alignItems={'center'}>
                                    <Typography variant={'h5'}>Driver License</Typography>
                                    <Label
                                        color={verified === 2 ? 'success' : (verified === 1 ? 'warning' : 'error')} >
                                        {verificationStatus[user?.driverLicenseVerification]}
                                    </Label>

                                </Stack>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Stack direction="column" sx={{ width: '100%' }} gap={2} paddingY={2}>
                                    <TextField label={t('driver.name')} onChange={(e) => { setProfile({ ...profile, username: e.target.value }) }} value={profile.username} />
                                    <TextField label={t('driver.address')} onChange={(e) => { setProfile({ ...profile, address: e.target.value }) }} value={profile.address} />
                                    <Box sx={{ width: '100%', }}>
                                        <input accept="image/*" type='file' hidden id='image' onChange={imageChange} />
                                        <Typography sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }} component="label" htmlFor='image' >
{image !== '' ?
    <img src={typeof image === 'object' ? URL.createObjectURL(image) : `${HOST_API}${image}`} alt="Driver license" style={{ width: '100%' }} />
    :
    <img src={`/images/driver-license.png`} alt="Default driver license" style={{ width: '100%', }} />
}
                                        </Typography>
                                    </Box>
                                    <TextField label={t('driver.description')} onChange={(e) => { setProfile({ ...profile, description: e.target.value }) }} value={profile.description} />
                                    <LoadingButton onClick={handleSubmit}  size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                        variant="contained" >{t('words.save_change')}
                                    </LoadingButton>
                                </Stack>

                            </AccordionDetails>
                        </Accordion>

                        <Accordion sx={{ width: '100%' }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Typography variant={'h5'}>{t('driver.bank_name')}</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Stack gap={2} sx={{ width: '100%' }} padding={2}>

                                    <TextField label={t('driver.bank_name')} value={bankName} onChange={(e) => setBankName(e.target.value)} />

                                    <TextField label={t('driver.bank_account')} value={bankAccount} onChange={(e) => setBankAccount(e.target.value)} />
                                    <LoadingButton onClick={handleBank} size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                        variant="contained" >{t('words.save_change')}
                                    </LoadingButton>
                                </Stack>
                            </AccordionDetails>
                        </Accordion>

                        <Accordion sx={{ width: '100%' }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Stack direction="row" justifyContent={'space-between'} sx={{ width: '100%' }} alignItems={'center'} >
                                    <Typography variant={'h5'}>Balance</Typography>
                                    <Label color="warning"
                                    >{fShortenNumber(user?.balance)}</Label>

                                </Stack>
                            </AccordionSummary>
                            <AccordionDetails>

                                <Stack gap={1} direction={{ xs: 'column', sm: 'row' }} justifyContent={'space-between'} sx={{ width: '100%', mb: 2 }} alignItems={{ sm: 'center' }}  >
                                    <TextField label="" onChange={(e) => { setPayAmount(e.target.value) }} value={payAmount} type="number" />
                                    <LoadingButton onClick={handleQPay} size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                        variant="contained" >{t('words.deposit')}
                                    </LoadingButton>
                                    <LoadingButton loading={loading} onClick={handleWithdraw} size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                        variant="contained" >{t('words.withdraw')}
                                    </LoadingButton>

                                </Stack>
                            </AccordionDetails>
                        </Accordion>
                        <Accordion sx={{ width: '100%' }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Stack direction="row" justifyContent={'space-between'} sx={{ width: '100%' }} alignItems={'center'} >
                                    <Typography variant="h5">{t('driver.withdraw_request')}</Typography>
                                    <Label color="warning"
                                    >{(user?.wallet?.requests?.length || 'Not yet')}
                                    </Label>

                                </Stack>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Stack sx={{ width: '100%', maxHeight: '400px', overflowY: 'auto', maxWidth: '600px', paddingBottom: 2 }} gap={1}>
                                    <Grid container>
                                        <Grid item xs={4}>
                                            Date
                                        </Grid>
                                        <Grid item xs={3}>
                                            Balance
                                        </Grid>
                                        <Grid item xs={3}>
                                            Request
                                        </Grid>
                                        <Grid item xs={2}>
                                            Status
                                        </Grid>
                                    </Grid>
                                    {user?.wallet?.requests?.map((request, index) => (
                                        <Grid container key={index}>
                                            <Grid item xs={4}>
                                                <Typography variant='caption'>{fDateTime(request.ts)}</Typography>

                                            </Grid>
                                            <Grid item xs={3}>
                                                {fShortenNumber(request.currentBalance)}
                                            </Grid>
                                            <Grid item xs={3}>
                                                {fShortenNumber(request.amount)}
                                            </Grid>
                                            <Grid item xs={2}>
                                                <Label
                                                    color={request.status === 'withdraw' ? 'success' : (request.status === 'pending' ? 'warning' : 'error')} >
                                                    {request.status}
                                                </Label>
                                            </Grid>
                                        </Grid>
                                    ))}

                                </Stack>
                            </AccordionDetails>
                        </Accordion>
                        <Accordion sx={{ width: '100%' }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Stack direction="row" justifyContent={'space-between'} sx={{ width: '100%' }} alignItems={'center'} >
                                    <Typography variant="h5">{t('driver.transactions')}</Typography>
                                    <Label color="warning"
                                    >{(user?.wallet?.transactions?.length || 'Not yet')}
                                    </Label>

                                </Stack>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Stack sx={{ width: '100%', maxHeight: '400px', overflowY: 'auto', maxWidth: '600px', paddingBottom: 2 }} gap={1}>
                                    <Grid container>
                                        <Grid item xs={4}>
                                            Date
                                        </Grid>
                                        <Grid item xs={4}>
                                            Amount
                                        </Grid>
                                        <Grid item xs={4}>
                                            Mode
                                        </Grid>

                                    </Grid>
                                    {user?.wallet?.transactions?.map((transaction, index) => (


                                        <Grid container key={index} sx={{ width: '100%' }}>
                                            <Grid item xs={4}>
                                                <Typography variant='caption'>{fDateTime(transaction?.ts)}</Typography>

                                            </Grid>
                                            <Grid item xs={4}>
                                                <Typography variant='caption'>{fShortenNumber(transaction?.amount)}</Typography>
                                            </Grid>
                                            <Grid item xs={4} sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>

                                                <Label
                                                    color={transaction.mode === 'withdraw' ? 'success' : 'error'} >
                                                    {transaction.mode}
                                                </Label>
                                                <Tooltip title={`${transaction.description}`} arrow>
                                                    <IconButton sx={{ padding: 0 }}>
                                                        <Icon icon="ic:outline-remove-red-eye" width={15}></Icon>
                                                    </IconButton>
                                                </Tooltip>
                                            </Grid>

                                        </Grid>

                                    ))}

                                </Stack>

                            </AccordionDetails>
                        </Accordion>
                    </Stack>
                </form>

            </Container>
            {paymentRequest && <PaymentDialog qrImage={qrImage} open={paymentRequest} onClose={() => { initialize(); setPaymentRequest(false); }} bankList={bankList} />}

        </Page>
    )
}
