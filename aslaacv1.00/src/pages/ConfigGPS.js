import React, { useState, useEffect } from 'react';
import { useSnackbar } from 'notistack';
import axios from '../utils/axios';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';
import { Container, Grid, Stack, Typography, Divider, Button, FormControl, InputLabel, Select, MenuItem, TextField, IconButton, Box } from '@mui/material';
import { Icon } from '@iconify/react';
import Iconify from '../components/Iconify';
import Layout from '../layout';
import Page from '../components/Page';
import { IconButtonAnimate } from '../components/animate';

// ----------------------------------------------------------------------

export default function ConfigGps() {
  const { id } = useParams();
  const { user, initialize } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const [currentDeviceNumber] = useState(id);
  const [ble, setBle] = useState('');
  const [subscribed, setSubscribed] = useState(false);
  const [gpsTime, setGpsTime] = useState(10 * 60);
  const [bleIndex, setBleIndex] = useState(0);
  const { t } = useTranslation();
  const [vaildMac, setValidMac] = useState(false);

  useEffect(() => {
    const device = user?.devices?.find((d) => d.deviceNumber === currentDeviceNumber);
    const interval = device?.interval;
    const subscribed = device?.subscribed;
    setGpsTime(interval);
    setSubscribed(subscribed);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDeviceNumber]);

  const addBleDevice = () => {
    axios
      .post(`/api/device/add-ble-device`, { deviceNumber: currentDeviceNumber, ble, bleIndex })
      .then((res) => {
        if (res.status === 200 && res.data.success) {
          enqueueSnackbar(res.data.message);
          initialize();
        } else {
          enqueueSnackbar(res.data.message, { variant: 'error' });
        }
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const removeBleDevice = (ble) => {
    axios
      .post(`/api/device/remove-ble-device`, { deviceNumber: currentDeviceNumber, ble })
      .then((res) => {
        if (res.status === 200 && res.data.success) {
          enqueueSnackbar(res.data.message);
          initialize();
        } else {
          enqueueSnackbar(res.data.message, { variant: 'error' });
        }
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const setGpsInterval = () => {
    axios
      .post(`/api/device/set-gps-time`, { deviceNumber: currentDeviceNumber, gpsTime })
      .then((res) => {
        if (res.status === 200 && res.data.success) {
          enqueueSnackbar(res.data.message);
          initialize();
        } else {
          enqueueSnackbar(res.data.message, { variant: 'error' });
        }
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const switchSubscribe = () => {
    axios
      .post(`/api/device/switch-subscribe`, { deviceNumber: currentDeviceNumber })
      .then((res) => {
        if (res.status === 200 && res.data.success) {
          enqueueSnackbar(res.data.message);
          setSubscribed(!subscribed);
        } else {
          enqueueSnackbar(res.data.message, { variant: 'error' });
        }
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const handleMacAddress = (e) => {
    const value = e.target.value;
    if (value.length === 17) {
      const regexp = new RegExp(/^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i);
      if (regexp.test(value)) {
        setBle(e.target.value);
        setValidMac(true);
      } else {
        setBle('Input type with MAC addr');
        setValidMac(false);
      }
    } else {
      setValidMac(false);
      setBle(e.target.value);
    }
  };

  return (
    <Page title="Device registration">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Stack justifyContent={'space-between'} direction="row" alignItems={'center'}>
              <Typography variant="h4">{t('device_profile.gps_information')}</Typography>
              <IconButtonAnimate onClick={() => window.history.back()}>
                <Iconify icon={'ep:back'} />
              </IconButtonAnimate>
            </Stack>

            <Divider sx={{ mb: 4, mt: 1 }} />
            <Stack spacing={3} sx={{ mb: 4 }}>
              <Stack direction="row" gap={1}>
                <Typography variant="h4">IMEI: {id}</Typography>
                <Button
                  fullWidth
                  size="large"
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                  disabled={!currentDeviceNumber}
                  onClick={() => switchSubscribe()}
                  variant="contained"
                >
                  {subscribed ? t('words.unsubscribe') : t('words.subscribe')}
                </Button>
              </Stack>
              <Box display={'flex'} gap={1}>
                <FormControl sx={{ flexGrow: 1 }}>
                  <InputLabel id="time-select-label">{t('words.gps_time_select')}</InputLabel>
                  <Select
                    value={gpsTime}
                    label={t('words.gps_time_select')}
                    labelId="type-select-label"
                    onChange={(e) => setGpsTime(e.target.value)}
                  >
                    <MenuItem value={10 * 60}>10Min</MenuItem>
                    <MenuItem value={30 * 60}>30Min</MenuItem>
                    <MenuItem value={60 * 60}>1Hr</MenuItem>
                    <MenuItem value={2 * 60 * 60}>2Hrs</MenuItem>
                    <MenuItem value={24 * 60 * 60}>24Hrs</MenuItem>
                  </Select>
                </FormControl>
                <Button
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                  variant="contained"
                  disabled={!currentDeviceNumber}
                  onClick={() => setGpsInterval()}
                >
                  {t('words.set_button')}
                </Button>
              </Box>

              <Box display={'flex'} gap={1}>
                <FormControl>
                  <TextField
                    label={'MAC Addr'}
                    value={ble}
                    onChange={handleMacAddress}
                    placeholder={'00:00:00:00:00:00'}
                    name="textmask"
                    id="formatted-text-mask-input"
                  />
                </FormControl>

                <FormControl sx={{ flexGrow: 1 }}>
                  <InputLabel id="type-select-label">{t('words.ble_index')}</InputLabel>
                  <Select
                    value={bleIndex}
                    onChange={(e) => setBleIndex(e.target.value)}
                    label={t('words.ble_index')}
                  >
                    <MenuItem value={0}>1st</MenuItem>
                    <MenuItem value={1}>2nd</MenuItem>
                    <MenuItem value={2}>3rd</MenuItem>
                    <MenuItem value={3}>4th</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Button
                onClick={() => addBleDevice()}
                sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                variant="contained"
                disabled={!currentDeviceNumber || ble === '' || !vaildMac}
              >
                {t('words.add_btn')}
              </Button>
            </Stack>
            <Typography variant="h4">{t('device_profile.registered_bles')}</Typography>
            <Divider sx={{ mb: 4, mt: 1 }} />
            <Stack gap={1}>
              {user?.devices
                ?.filter((d) => d.deviceNumber === currentDeviceNumber)
                .map((d) =>
                  d?.bles?.map((ble, index) => (
                    <Stack
                      key={index}
                      direction={'row'}
                      justifyContent={'space-between'}
                      alignItems={'center'}
                    >
                      <Typography variant="subtitle1">{ble.id}</Typography>
                      <Typography variant="subtitle1">{ble.index}</Typography>
                      <IconButton onClick={() => removeBleDevice(ble.id)}>
                        <Icon icon={'akar-icons:trash-can'} />
                      </IconButton>
                    </Stack>
                  ))
                )}
            </Stack>
          </Grid>
        </Grid>
      </Container>
    </Page>
  );
}
