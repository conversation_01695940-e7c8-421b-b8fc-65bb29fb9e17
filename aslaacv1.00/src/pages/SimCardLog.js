import React, { useState, useEffect } from 'react';
import axios from '../utils/axios'; // Adjust according to your project structure
import {
  Box,
  Card,
  CardContent,
  Container,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import useAuth from '../hooks/useAuth';
import Page from '../components/Page';
import Layout from '../layout';

function FetchSimLogs() {
  const { user } = useAuth();
  const [deviceNumber, setDeviceNumber] = useState('');
  const [logs, setLogs] = useState([]);

  useEffect(() => {
    // Fetch logs only if a device number is selected
    if (deviceNumber) {
      fetchSimLogs(deviceNumber);
    } else {
      // Clear logs if no device number is selected
      setLogs([]);
    }
  }, [deviceNumber]);

  const fetchSimLogs = async (selectedDeviceNumber) => {
    try {
      const response = await axios.get(`/api/log/sim-logs?deviceNumber=${selectedDeviceNumber}`);
       // console.log('API Response:', response.data); // For debugging

      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setLogs(response.data.data);
      } else {
        // console.error('Invalid or unexpected logs data structure:', response.data);
        setLogs([]);
      }
    } catch {
      // console.error('Error fetching SIM logs:', error);
      setLogs([]);
    }
  };

  return (
    <Page title="SIM Logs">
      <Container>
        <Layout />
        <Box sx={{ mt: 8 }}> {/* Try a larger value */}
  <Typography variant="h4" gutterBottom>
    Latest 10 SIM Logs
  </Typography>
  <FormControl fullWidth margin="normal" variant="outlined" size="small">
    <InputLabel id="device-number-select-label">Device Number</InputLabel>
    <Select
      labelId="device-number-select-label"
      id="device-number-select"
      value={deviceNumber}
      onChange={(e) => setDeviceNumber(e.target.value)}
      label="Device Number"
    >
      {user?.devices?.map((device, index) => (
        <MenuItem value={device.deviceNumber} key={index}>
          {device.deviceNumber}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
  {/* Use a separate Grid container for the "No logs available" message to ensure it doesn't share a row with the selector */}
  {logs.length > 0 ? (
    <Grid container spacing={3}>
      {logs.map((log, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card>
            <CardContent>
              <Typography variant="h6">{log.content || "No Content"}</Typography>
              <Typography color="textSecondary">{log.received || "No Date"}</Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  ) : (
    // Ensuring "No logs available" is clearly separated in the layout
    <Box mt={2}>
      <Typography variant="subtitle1" color="textSecondary">
        No logs available.
      </Typography>
    </Box>
  )}
</Box>

      </Container>
    </Page>
  );
}

export default FetchSimLogs;
