import {
  Con<PERSON>er,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import * as L from "leaflet";
import "leaflet/dist/leaflet.css";
import { useState, useEffect } from "react";
import axios from "../utils/axios";
import useAuth from "../hooks/useAuth";
import "leaflet-routing-machine";

export default function CarLocationsWithRouter() {
  const [from, setFrom] = useState(
    new Date(new Date().getTime() - 24 * 60 * 60 * 1000).toISOString()
  );
  const [to, setTo] = useState(new Date().toISOString());
  const { user } = useAuth();
  const [deviceNumber, setDeviceNumber] = useState(user?.device?.deviceNumber || "");
  const [routines, setRoutines] = useState([]);

  useEffect(() => {
    if (deviceNumber !== "") load();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviceNumber]);

  const load = () => {
    axios
      .post("/api/log/get-by-date", { from, to, deviceNumber })
      .then((res) => {
        if (res.status === 200 && res.data.logs.length > 0) {
          const enrichedPoints = res.data.logs
            .map((log) => ({
              lat: parseFloat(log.Lat.replace(" N", "")),
              lng: parseFloat(log.Lon.replace(" E", "")),
              sta: log.sta,
              speed: log.Speed,
              createdAt: log.createdAt,
            }))
            .filter((point) => point.lat !== 0 && point.lng !== 0);

          const interpolatedPoints = loadDataWithInterpolation(enrichedPoints);
          const filteredPoints = filterClosePoints(interpolatedPoints, 10);

          if (filteredPoints.length > 0) {
            setRoutines(filteredPoints);
          }
        }
      })
      .catch((err) => {
        console.error("Error loading GPS data:", err);
      });
  };

  const MapRoutes = ({ routines }) => {
    const map = useMap();

    if (routines && routines.length > 0) {
      L.Routing.control({
        waypoints: routines,
        lineOptions: {
          styles: [
            {
              color: "#0053A4",
              opacity: 0.8,
              weight: 4,
            },
          ],
        },
        show: false,
        addWaypoints: false,
        routeWhileDragging: true,
        draggableWaypoints: true,
        fitSelectedRoutes: true,
        showAlternatives: false,
        createMarker: function () {
          return null;
        },
      })
        .on("routesfound", (data) => {})
        .addTo(map);
    }

    return null;
  };

  function filterClosePoints(points, minDistance) {
    return points.filter((point, index, array) => {
      if (index === 0) return true;
      return arePointsFarEnough(point, array[index - 1], minDistance);
    });
  }

  function arePointsFarEnough(point1, point2, minDistance) {
    const distance = L.latLng(point1).distanceTo(L.latLng(point2));
    return distance > minDistance;
  }

  function loadDataWithInterpolation(gpsData) {
    let enrichedData = [];
    for (let i = 0; i < gpsData.length - 1; i++) {
      enrichedData.push(gpsData[i]);
      let fakePoints = generateIntermediatePoints(gpsData[i], gpsData[i + 1], 2);
      enrichedData = enrichedData.concat(fakePoints);
    }
    enrichedData.push(gpsData[gpsData.length - 1]);
    return enrichedData;
  }

  function generateIntermediatePoints(startPoint, endPoint, numPoints) {
    let intermediatePoints = [];
    for (let i = 1; i <= numPoints; i++) {
      let t = i / (numPoints + 1);
      const interpolatedLat = startPoint.lat * (1 - t) + endPoint.lat * t;
      const interpolatedLng = startPoint.lng * (1 - t) + endPoint.lng * t;
      const interpolatedSpeed = startPoint.speed * (1 - t) + endPoint.speed * t;
      const interpolatedCreatedAt = t <= 0.5 ? startPoint.createdAt : endPoint.createdAt;

      intermediatePoints.push({
        lat: interpolatedLat,
        lng: interpolatedLng,
        sta: startPoint.sta,
        speed: interpolatedSpeed,
        createdAt: interpolatedCreatedAt,
      });
    }
    return intermediatePoints;
  }

  return (
    <Container sx={{ py: { xs: 10 } }}>
      <Stack>
        <Stack direction="row" justifyContent={"center"} alignItems={"center"} gap={1}>
          <FormControl>
            <InputLabel id="type-select-label">Device</InputLabel>
            <Select
              label="Device"
              value={deviceNumber}
              onChange={(e) => setDeviceNumber(e.target.value)}
              labelId="type-select-label"
            >
              {user?.devices
                ?.filter((d) => d.type === "4g")
                ?.map((dn, index) => (
                  <MenuItem value={dn.deviceNumber} key={index}>
                    {dn.deviceNumber}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </Stack>
        <Stack
          direction="row"
          justifyContent={"center"}
          alignItems={"center"}
          gap={1}
          py={1}
        >
          <TextField
            size="small"
            defaultValue={from}
            onChange={(e) => {
              setFrom(e.target.value);
            }}
          />
          <Typography>-</Typography>
          <TextField
            size="small"
            defaultValue={to}
            onChange={(e) => {
              setTo(e.target.value);
            }}
          />
          <button onClick={() => load()}>Refresh</button>
        </Stack>
        <Stack sx={{ mb: 2 }}>
          <MapContainer center={[47.89994, 106.8994]} zoom={13} style={{ height: "120vh" }}>
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution="&copy; OpenStreetMap contributors"
            />
            {routines?.map((l, index) => (
              <Marker
                key={index}
                position={{ lat: l.lat, lng: l.lng }}
                icon={createRotatedIcon("/images/red-point.png", 0, 5)}
              />
            ))}
            <MapRoutes routines={routines} />
          </MapContainer>
        </Stack>
      </Stack>
    </Container>
  );
}

function createRotatedIcon(url, rotationAngle, size = 30) {
  return L.divIcon({
    html: `<img src="${url}" style="transform: rotate(${rotationAngle}deg); width: ${size}px; height: ${size}px;">`,
    iconSize: [size, size],
    className: "",
  });
}
