import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import DialogTitle from '@mui/material/DialogTitle';
import Dialog from '@mui/material/Dialog';
import { Box, Stack } from '@mui/material';
export function PaymentDialog(props) {
    const { onClose, bankList, open, qrImage } = props;

    const handleClose = () => {
        onClose();
    };

    return (
        <Dialog onClose={handleClose} open={open} fullWidth={true} maxWidth={'md'} sx={{ '& .MuiDialog-paper': { position: "fixed", bottom: 0, width: '100%', margin: 0 } }}>
            <DialogTitle>Choose your bank account</DialogTitle>
            <Stack sx={{ width: '100%', alignItems: 'center', justifyContent: 'center' }}>
                {qrImage && qrImage !== null &&
                    <Box sx={{ width: 164, height: 164 }}>
                        <img src={`data:image/jpeg;base64,${qrImage}`} style={{ width: '100%', height: '100%' }} alt="QR code for payment" />
                    </Box>
                }
            </Stack>
            <List sx={{ pt: 0, maxHeight: 450, overflowY: 'scroll' }}>
                {(bankList || []).map((bank, index) => (
                    <ListItem button onClick={() => window.location.href = bank.link} key={index}>
                        <ListItemAvatar>
                            <img src={`${bank.logo}`} width={50} height={50} alt={`Logo of ${bank.name}`} />
                        </ListItemAvatar>
                        <ListItemText primary={bank.name} secondary={bank.description} />
                    </ListItem>
                ))}
            </List>
        </Dialog>
    );
}