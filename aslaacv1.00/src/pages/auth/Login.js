// @mui

import { styled } from '@mui/material/styles';
import { Box, Typography,  } from '@mui/material';

// components
import Page from '../../components/Page';

// sections 
import LoginForm from '../../sections/auth/LoginForm';
import CarFront from '../../components/CarFront';

const ContentStyle = styled('div')(({ theme }) => ({
  maxWidth: 480,
  margin: 'auto',
  display: 'flex',
  minHeight: '100vh',
  flexDirection: 'column',
  justifyContent: 'center',
  // padding: theme.spacing(8, 0),
  alignContent: 'space-between',
  gap: 3
}));

// ----------------------------------------------------------------------

export default function Login() {

  return (
    <Page title="Login">


      <ContentStyle>
        <Typography variant="h3" gutterBottom textAlign={'center'}>
          Машины хяналтын систем
        </Typography>

        <Box width={"50%"} sx={{ mx: 'auto', mb: 3, mt: -3 }}>
          <CarFront />
        </Box>
        <Typography paragraph textAlign={'center'}>
          Утасны дугаараа оруулна уу
        </Typography>
        <Box width={'80%'} mx={'auto'} my={3}>
          <LoginForm />
        </Box>
        
      </ContentStyle>

    </Page>
  );
}
