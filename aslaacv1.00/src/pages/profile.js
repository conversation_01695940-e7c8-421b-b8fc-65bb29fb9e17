import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
// form
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui 
import { LoadingButton, MobileDatePicker } from '@mui/lab';
import { Stack, Alert, Grid, Switch, Container, Typography, Divider, TextField, Button, Chip, Select, MenuItem, FormControl, InputLabel } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs from 'dayjs';
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { useState, useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
// hooks
import useAuth from '../hooks/useAuth';
import useIsMountedRef from '../hooks/useIsMountedRef';
// components
import { FormProvider, RHFSelect, RHFTextField } from '../components/hook-form';
import CarFront from '../components/CarFront';
import Page from '../components/Page';
import axios from '../utils/axios';
import useResponsive from '../hooks/useResponsive';
import Layout from '../layout';
import { fDate } from '../utils/formatUtils';
import { OrderPrice } from '../config';
import { fShortenNumber } from '../utils/formatUtils';
import { PaymentDialog } from './PaymentDialog';
import { deleteUser } from 'firebase/auth';
import { Icon } from '@iconify/react';

// ----------------------------------------------------------------------

const verificationStatus = ['no-license','pending','verified'];


// ----------------------------------------------------------------------

export default function DeviceProfile() {
  const { initialize, user } = useAuth();
  const navigate = useNavigate();
  const [paymentRequest, setPaymentRequest] = useState(false);
  const { t } = useTranslation();
  const isMountedRef = useIsMountedRef();
  const { enqueueSnackbar } = useSnackbar();
  const [bankList, setBankList] = useState([]);
  const [qrImage, setQrImage] = useState();
  const [userPhoneNumber, setUserPhoneNumber] = useState(user?.phoneNumber);
  const [DriverName, setDriverName] = useState("");
  const [address, setAddress] = useState("");
  const [availableTime, setAvailableTime] = useState(dayjs(new Date()));
  const [isSpareKey, setIsSpareKey] = useState(true);
  const [currentOrder, setCurrentOrder] = useState();
  const [orderPrice, setTotalPrice] = useState(OrderPrice);
  const [image, setImage] = useState(null);


  const handleFileChange = (e) => {
    setImage(e.target.files[0]);
  }
 
  const onSubmitOrder = async () => {


    const data = {
      phoneNumber: userPhoneNumber,
      drivername: DriverName,
      address: address,
      image: image
    }
    const res = await axios.post(`/api/device/driver-confirm`, { ...data });
    console.log("is order", data)
    try {
      const qpay = res?.data?.qpay;
      if (res?.data?.success) {
        if (qpay && qpay.success) {
          enqueueSnackbar(`${res?.data?.message}, but not paid yet`, { variant: 'success' });
          setTimeout(() => {
            setQrImage(qpay.bankList?.qr_image);
            setBankList(qpay.bankList?.urls);
            setPaymentRequest(true);
          }, 1000);
        }
        else {
          enqueueSnackbar(qpay?.message, { variant: 'error' });
        }
        // enqueueSnackbar(res?.data?.message, { variant: 'success' });

      }
      else {
        if (res?.data?.order) {
          if (!res.data.order?.paid) {

            if (qpay && qpay.success) {
              enqueueSnackbar(`${res?.data?.message}, but not paid yet`, { variant: 'error' });
              setTimeout(() => {
                setQrImage(qpay.bankList?.qr_image);

                setBankList(qpay.bankList?.urls);
                setPaymentRequest(true);
              }, 1000);

            }

            else {
              enqueueSnackbar(qpay?.message, { variant: 'error' });
            }
          }

          // order is already and already paid.
          else {
            enqueueSnackbar(res?.data?.message, { variant: 'error' });
          }
        }

      }
    }
    catch (err) {
      // console.log(err);
    }
  }


  useEffect( () => {
    async function fetchdata(){
      const res = await axios.post(`/api/device/driver-info`);
      if (res && res.status === 200 && res.data.success) {
        console.log("data",res.data)
        setCurrentOrder(res.data.driverProfile);
        setDriverName(res.data.driverProfile.drivername);
        setAddress(res.data.driverProfile.address);
      }
    }
    fetchdata();
  }, []);
  console.log('is visible', paymentRequest);
  return (
    <Page title="Order Profile">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'md'}>
        <Grid container spacing={3}  >

          <Grid item xs={12} >
            <Typography variant='h4' sx={{ mt: 2 }}>
              {t("order.driver_profile")}
              <Chip sx={{ ml: 2 }} label={verificationStatus[user?.driverLicenseVerification]}
                size="small" />
            </Typography>
            <Divider sx={{ mb: 4, mt: 1 }} />
            {currentOrder &&
              <Stack spacing={2} direction='row' sx = {{mb:4, justifyContent:'center', alignItems:'center'}} >
                {currentOrder?.paid?<Icon width={24}  icon = "flat-color-icons:paid"></Icon>:<Icon width = {24} icon = 'mdi:question-mark-circle-outline'></Icon>}
                <Typography variant='subtitle2'>{currentOrder.paid?currentOrder.invoiceId:'Not Paid yet'}, </Typography>
                <Typography variant='subtitle2'>Confirmed  Status:</Typography>
                {currentOrder?.isInstalled?<Icon icon="entypo:install" color ='green'></Icon> : <Icon icon='entypo:uninstall'></Icon>}
              </Stack>
            }
            <Stack spacing={3}>

              <TextField label={`${t("order.drivername")}`} onChange={(e) => { setDriverName(e.target.value) }} value={DriverName} />
              <TextField label={`${t("order.address")}`} onChange={(e) => { setAddress(e.target.value) }} value={address} />
              <input type="file" name="image" id="image" accept="image/*" onChange={handleFileChange} />
              <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                onClick={() => { onSubmitOrder() }} variant="contained"  >
                {t("order.submit_order")}
              </Button>
            </Stack>


          </Grid>
        </Grid>
      </Container>
      {paymentRequest && <PaymentDialog qrImage={qrImage} open={paymentRequest} onClose={() => { initialize(); setPaymentRequest(false); }} bankList={bankList} />}
    </Page>
  );
}
