import PropTypes from 'prop-types';
import {useTranslation} from 'react-i18next'
import { useState } from 'react';

// @mui
import { MenuItem, IconButton } from '@mui/material';
// components
import Iconify from '../../components/Iconify';
import MenuPopover from '../../components/MenuPopover';


// ----------------------------------------------------------------------

InvoiceLogMoreMenu.propTypes = {
  onDelete: PropTypes.func,
  id:PropTypes.string,
  onLocation:PropTypes.func,
};
export default function InvoiceLogMoreMenu({ onDelete,id,onDetail }) {
  const [open, setOpen] = useState(null);
  const {t} = useTranslation();
  const handleOpen = (event) => {
    setOpen(event.currentTarget);
  };

  const handleClose = () => {
    setOpen(null);
  };

  const ICON = {
    mr: 2,
    width: 20,
    height: 20,
  };

  return (
    <>
      <IconButton onClick={handleOpen}>
        <Iconify icon={'eva:more-vertical-fill'} width={20} height={20} />
      </IconButton>

      <MenuPopover
        open={Boolean(open)}
        anchorEl={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        arrow="right-top"
        sx={{
          mt: -1,
          width: 170,
          '& .MuiMenuItem-root': { px: 1, typography: 'body2', borderRadius: 0.75 },
        }}
      >
        <MenuItem onClick={onDelete} sx={{ color: 'error.main' }}>
          <Iconify icon={'eva:trash-2-outline'} sx={{ ...ICON }} />
          {t('words.delete')}
        </MenuItem>
        <MenuItem onClick={onDetail} sx={{ color: 'info.main' }}>
          <Iconify icon={'fa:dollar'} sx={{ ...ICON }} />
          {t('words.detail')}
        </MenuItem>
      </MenuPopover> 
    </>
  );
}
