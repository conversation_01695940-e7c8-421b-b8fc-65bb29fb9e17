import PropTypes from 'prop-types';
// @mui
import {  styled } from '@mui/material/styles';
import { Toolbar,  IconButton, Typography, InputAdornment} from '@mui/material';

// import AdapterDateFns from '@mui/lab/AdapterDateFns';
// import {LocalizationProvider, MobileDateTimePicker} from '@mui/lab';
// components
import Iconify from '../../components/Iconify';
import InputStyle from '../../components/InputStyle';


// ----------------------------------------------------------------------

const RootStyle = styled(Toolbar)(({ theme }) => ({
  height: 70,
  display: 'flex',
  justifyContent: 'space-between',
  padding: theme.spacing(0, 1, 0, 3),
}));

// ----------------------------------------------------------------------

LogListToolbar.propTypes = {
  numSelected: PropTypes.number,
  filterName: PropTypes.string,
  onFilterName: PropTypes.func,
  onDeleteDevice: PropTypes.func,
  onChangeTime:PropTypes.func,
};

export default function LogListToolbar({ numSelected, filterName, onFilterName, onDeleteDevice,onChangeTime }) {

  return (
    <RootStyle
      sx={{
        ...(numSelected > 0 && {
          color: 'text.primary',
          bgcolor:  'primary.dark',
        }),
      }}
    >
      {numSelected > 0 ? (
        <Typography component="div" variant="subtitle1">
          {numSelected} selected
        </Typography>
      ) : ( 
        <InputStyle
          size='small'
          stretchStart={240}
          value={filterName}
          onChange={(event) => onFilterName(event.target.value)}
          placeholder="Search ..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon={'eva:search-fill'} sx={{ color: 'text.disabled', width: 20, height: 20 }} />
              </InputAdornment>
            ),
          }}
        />
        
      )}

      {numSelected > 0 &&
        
          <IconButton onClick={onDeleteDevice}>
            <Iconify icon={'eva:trash-2-outline'} />
          </IconButton>
        
      }

    </RootStyle>
  );
}
