import { Divider, Skeleton, Stack, Table, TableBody, TableCell, TableContainer, TablePagination, TableRow } from "@mui/material";
import { sentenceCase } from "change-case";
import { useEffect, useState } from "react";
import SearchNotFound from "../../../components/SearchNotFound";
import Label from "../../../components/Label";
import Scrollbar from "../../../components/Scrollbar";
import TableListHead from "../user/TableListHead";
import DeviceListToolbar from "./DeviceListToolbar";
import axios from "../../../utils/axios";
import DeviceSmsListToolbar from "./DeviceSmsListToolbar";
import { formatSQLDate } from "../../../utils/formatUtils";


const TABLE_HEAD = [
    { id: 'user', label: 'Nickname' },
    { id: 'device', label: 'PhoneNumber' },
    { id: 'send', label: 'Sent' },
    { id: 'failed', label: 'Failed' },
    { id: 'receive', label: 'Received' },
 
];

export default function DeviceSmsLog({ onClients }) {
    const [clients, setClients] = useState([]);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(0);
    const [order, setOrder] = useState('asc');
    const [selected, setSelected] = useState([]);
    const [orderBy, setOrderBy] = useState('name');
    const [filterName, setFilterName] = useState('');
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [from, setFrom] = useState(formatSQLDate(new Date(Date.now() - 24 * 60 * 60 * 1000)));
    const [to, setTo] = useState(formatSQLDate(new Date(Date.now())));
    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };
    const handleFilterByName = (filterName) => {
        setFilterName(filterName);
        setPage(0);
    };
    const handleRequestSort = (property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };
    const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - clients.length) : 0;
    const filteredClients = applySortFilter(clients, getComparator(order, orderBy), filterName);
    const isNotFound = !filteredClients.length && Boolean(filterName);
    const load = ()=>{
        setLoading(true)
        axios.get(`/api/log/sms-logs/${from}/${to}`).then(res => {
            if (res.status === 200 && res.data.clients) {
                setClients(res.data.clients);
                onClients(res.data.clients);
            }
        }).catch(err => {
        }).finally(() => {
            setLoading(false)
        });
    }
    useEffect(() => {
        load();
    }, []);
    const handleChangeDate = ()=>{
        
    }
    return (
        <Stack sx={{ width: '100%' }}>
            <Scrollbar sx={{ padding: 2 }} >
                {
                    loading &&
                    [1, 2, 3, 4, 5].map((index) => (
                        <Skeleton height={30} key={index} animation='pulse' />
                    ))
                }
                {!loading &&
                    <>
                        <DeviceSmsListToolbar
                            filterName={filterName}
                            onFilterName={handleFilterByName}
                            onFilterFrom={(e) => { setFrom(e)}}
                            onFilterTo={(e) => { setTo(e)}}
                            to={to}
                            from={from}
                            onChangeDate = {load}
                        />
                        <Divider />
                        <TableContainer  >
                            <Table size="small" stickyHeader>
                                <TableListHead
                                    checkbox={false}
                                    order={order}
                                    orderBy={orderBy}
                                    headLabel={TABLE_HEAD}
                                    rowCount={clients.length}
                                    onRequestSort={handleRequestSort}
                                />
                                <TableBody>
                                    {filteredClients.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row) => {

                                        const { user, device, sent, received, failed } = row;

                                        const isItemSelected = selected.indexOf(device) !== -1;

                                        return (
                                            <TableRow
                                                hover
                                                key={device}
                                                tabIndex={-1}
                                                selected={isItemSelected}
                                                aria-checked={isItemSelected}
                                            >

                                                <TableCell align="left">{user}</TableCell>
                                                <TableCell align="left">{device}</TableCell>
                                                <TableCell align="left">{sent}</TableCell>
                                                <TableCell align="left">{failed}</TableCell>
                                                <TableCell align="left">{received}</TableCell>

                                            </TableRow>
                                        );
                                    })}
                                    {emptyRows > 0 && (
                                        <TableRow style={{ height: 53 * emptyRows }}>
                                            <TableCell colSpan={6} />
                                        </TableRow>
                                    )}
                                </TableBody>
                                {isNotFound && (
                                    <TableBody>
                                        <TableRow>
                                            <TableCell align="center" colSpan={10} sx={{ py: 3 }}>
                                                <SearchNotFound searchQuery={filterName} />
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                )}
                            </Table>
                        </TableContainer>
                    </>

                }
            </Scrollbar>
            <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={filteredClients.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={(e, page) => setPage(page)}
                onRowsPerPageChange={handleChangeRowsPerPage}
            />
        </Stack>
    )
}

function descendingComparator(a, b, orderBy) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}
function getComparator(order, orderBy) {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
    // console.log(array);
    const stabilizedThis = array.map((el, index) => [el, index]);
    stabilizedThis.sort((a, b) => {
        const order = comparator(a[0], b[0]);
        if (order !== 0) return order;
        return a[1] - b[1];
    });
    if (query) {
        return array.filter((device) => (device?.user?.toLowerCase().includes(query.toLowerCase()) || device?.device?.toLowerCase().includes(query.toLowerCase())));
    }
    return stabilizedThis.map((el) => el[0]);
}
