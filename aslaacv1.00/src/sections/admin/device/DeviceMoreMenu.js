import PropTypes from 'prop-types';
import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
// @mui
import { MenuItem, IconButton } from '@mui/material';
// components
import Iconify from '../../../components/Iconify';
import MenuPopover from '../../../components/MenuPopover';

// ----------------------------------------------------------------------

DeviceMoreMenu.propTypes = {
  onDisable: PropTypes.func,
  onEnable:PropTypes.func,
  deviceId: PropTypes.string,
};

export default function DeviceMoreMenu({ onDisable, onEnable,deviceId }) {
  const [open, setOpen] = useState(null);
  const handleOpen = (event) => {
    setOpen(event.currentTarget);
  };

  const handleClose = () => {
    setOpen(null);
  };

  const ICON = {
    mr: 2,
    width: 20,
    height: 20,
  };

  return (
    <>
      <IconButton onClick={handleOpen}>
        <Iconify icon={'eva:more-vertical-fill'} width={20} height={20} />{' '}
      </IconButton>
      <MenuPopover
        open={Boolean(open)}
        anchorEl={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        arrow="right-top"
        sx={{
          mt: -1,
          width: 170,
          '& .MuiMenuItem-root': { px: 1, typography: 'body2', borderRadius: 0.75 },
        }}
      >
        <MenuItem onClick={onDisable} sx={{ color: 'error.main' }}>
          <Iconify icon={'eva:trash-2-outline'} sx={{ ...ICON }} />
          Disable{' '}
        </MenuItem>
        <MenuItem onClick={onEnable} sx={{ color: 'info.main' }}>
          <Iconify icon={'akar-icons:eye'} sx={{ ...ICON }} />
          Enable{' '}
        </MenuItem>{' '}
        <MenuItem component={RouterLink} to={`/admin/device/${deviceId}`}>
          <Iconify icon={'eva:edit-fill'} sx={{ ...ICON }} />
          Edit{' '}
        </MenuItem>{' '}
      </MenuPopover>{' '}
    </>
  );
}
