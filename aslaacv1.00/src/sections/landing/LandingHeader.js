import { Container, Stack, styled, Toolbar, Typography } from "@mui/material";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import useAuth from "../../hooks/useAuth";

// import Car<PERSON>ogo from "../../components/CarLogo";
import Logo from "../../components/Logo";

const ToolbarStyle = styled(Toolbar)(({ theme }) => ({
    transition: theme.transitions.create(['height', 'background-color'], {
        easing: theme.transitions.easing.easeInOut,
        duration: theme.transitions.duration.shorter,
    }),

    backdropFilter: 'blur(60px)',
    width: '100%',
    position: 'fixed',
    top: 0,
    zIndex: 999,
    left: 0
}));

export default function LandingHeader() {
    const { t } = useTranslation();
    const { user } = useAuth();
    const navigate = useNavigate();

    return (
        <ToolbarStyle>
            <Container>
                <Stack direction="row" justifyContent="space-between" padding={2}>
                    {/* <CarLogo /> */}
                    <Logo sx={{ height: { xs: 36, sm: 48, md: 64 }, width: { xs: 64, sm: 84, md: 124 } }} />

                    {/* Contact Info Stack with no MotionInView */}
                    <Stack
                        alignContent="center"
                        justifyContent="center"
                        sx={{ display: { xs: 'none', sm: 'none', md: 'flex' } }}
                        flexDirection="row"
                        gap={3}
                    >
                        <Stack direction="row" alignItems="center" gap={1}>
                            <Icon icon="arcticons:callforwardingstatus" color="orange" width={36} />
                            <Stack>
                                <Typography variant="subtitle2">
                                    {t('Утас')}
                                </Typography>
                                <Typography variant="h6">
                                    77372929
                                </Typography>
                            </Stack>
                        </Stack>

                        <Stack direction="row" alignItems="center" gap={1}>
                            <Icon icon="mdi:email-open-outline" color="orange" width={36} />
                            <Stack>
                                <Typography variant="subtitle2">
                                    {t('Емайл')}
                                </Typography>
                                <Typography variant="h6">
                                    <EMAIL>
                                </Typography>
                            </Stack>
                        </Stack>

                        <Stack direction="row" alignItems="center" gap={1}>
                            <Icon icon="ph:map-pin-line" color="orange" width={36} />
                            <Stack>
                                <Typography variant="subtitle2">
                                    {t('Хаяг')}
                                </Typography>
                                <Typography variant="h6">
                                    Компьютер Ланд баруун хойно 26 байр Electronic Parts
                                </Typography>
                            </Stack>
                        </Stack>
                    </Stack>

                    {/* User Icon */}
                    <Stack alignItems="center" justifyContent="center">
                        {user == null
                            ? (
                                <Icon
                                    onClick={() => navigate('/auth/login')}
                                    icon="iconoir:user-circle"
                                    width={36}
                                    color="orange"
                                    cursor="pointer"
                                />
                            )
                            : (
                                <Icon
                                    onClick={() => navigate('/auth/login')}
                                    icon="ri:dashboard-3-line"
                                    width={36}
                                    color="orange"
                                    cursor="pointer"
                                />
                            )
                        }
                    </Stack>
                </Stack>
            </Container>
        </ToolbarStyle>
    );
}
