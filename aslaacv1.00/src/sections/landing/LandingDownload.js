import { Box, Link, Stack, styled, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";

import Image from "../../components/Image";
import { HOST_API } from "../../config";

const RootStyle = styled(Stack)(({ theme }) => ({
    position: 'relative',
    display: 'flex',
    // height: '80vh',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'contain',
    backgroundPosition: 'right',
    width: '100%',
}));

export default function LandingDownload() {
    const { t } = useTranslation();
    return (
        <RootStyle>
            <Stack
                height="100%"
                width="100%"
                direction={{ xs: 'column', sm: 'column', md: 'row' }}
                justifyContent="center"
                sx={{ backdropFilter: 'blur(2px)' }}
                paddingX={{ xs: 2, sm: 4, md: 6 }}
            >
                <Stack padding={4} sx={{ backdropFilter: 'blur(2px)' }}>
                    <Typography variant="h3" color="orange" sx={{ mb: 2 }}>
                        {t('Гар утасны апп татах')}
                    </Typography>
                    <Box sx={{ maxWidth: 600, overflow: 'hidden', borderRadius: '8px' }}>
                        <Image src="/images/download-app.gif" alt="" />
                    </Box>
                </Stack>

                <Stack padding={4} mb={4} sx={{ backdropFilter: 'blur(2px)' }} justifyContent="flex-start">
                    <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        justifyContent="center"
                        gap={2}
                        sx={{ mx: 'auto', mb: 3, mt: 3 }}
                        height={{ xs: 60, sm: 70 }}
                    >
                        <Link href={`${HOST_API}direct-app-download/android-app.apk`} sx={{ textDecoration: 'none', color: 'white' }}>
                            <Box
                                sx={{ cursor: 'pointer' }}
                                flexDirection="row"
                                display="flex"
                                paddingY={2}
                                paddingX={2}
                                alignItems="center"
                                border={1}
                                borderColor="#38e8ff"
                                borderRadius={2}
                                gap={1}
                            >
                                <Icon icon="tabler:brand-android" width={48} color="#38e8ff" />
                                <Stack px={2}>
                                    <Typography variant="caption">
                                        Download APK
                                    </Typography>
                                    <Typography variant="h6">
                                        Android
                                    </Typography>
                                </Stack>
                            </Box>
                        </Link>

                        <Link
                            href="https://apps.apple.com/mn/app/aslaa/id1671998041"
                            sx={{ textDecoration: 'none', color: 'white' }}
                        >
                            <Box
                                flexDirection="row"
                                sx={{ cursor: 'pointer' }}
                                display="flex"
                                paddingY={2}
                                paddingX={2}
                                alignItems="center"
                                border={1}
                                borderColor="#38e8ff"
                                borderRadius={2}
                                gap={1}
                            >
                                <Icon icon="ph:apple-logo-bold" width={48} color="#38e8ff" />
                                <Stack px={2}>
                                    <Typography variant="caption">
                                        Download from
                                    </Typography>
                                    <Typography variant="h6">
                                        App Store
                                    </Typography>
                                </Stack>
                            </Box>
                        </Link>
                    </Stack>
                </Stack>
            </Stack>
        </RootStyle>
    );
}
