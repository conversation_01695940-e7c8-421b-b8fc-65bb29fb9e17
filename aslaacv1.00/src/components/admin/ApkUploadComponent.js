import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Typography, 
  Card, 
  CardContent, 
  CircularProgress,
  Alert,
  LinearProgress
} from '@mui/material';
import { useSnackbar } from 'notistack';
import UploadIcon from '@mui/icons-material/Upload';
import axios from '../../utils/axios';

export default function ApkUploadComponent() {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { enqueueSnackbar } = useSnackbar();

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.name.toLowerCase().endsWith('.apk')) {
      setFile(selectedFile);
      setUploadStatus(null);
      setUploadProgress(0);
    } else {
      setFile(null);
      setUploadStatus({
        success: false,
        message: 'Please select a valid APK file'
      });
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setUploadStatus({
        success: false,
        message: 'Please select an APK file first'
      });
      return;
    }

    setIsUploading(true);
    setUploadStatus(null);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('apkFile', file);

    try {
      const response = await axios.post('/api/download/upload-android-apk', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data.success) {
        setUploadStatus({
          success: true,
          message: 'APK file uploaded successfully'
        });
        enqueueSnackbar('APK file uploaded successfully', { variant: 'success' });
        setFile(null);
      } else {
        throw new Error(response.data.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Error uploading APK:', error);
      setUploadStatus({
        success: false,
        message: error.response?.data?.message || error.message || 'Failed to upload APK file'
      });
      enqueueSnackbar('Failed to upload APK file', { variant: 'error' });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Upload Android APK
        </Typography>
        
        <Box sx={{ mt: 2, mb: 2 }}>
          <input
            accept=".apk"
            style={{ display: 'none' }}
            id="apk-file-upload"
            type="file"
            onChange={handleFileChange}
            disabled={isUploading}
          />
          <label htmlFor="apk-file-upload">
            <Button
              variant="outlined"
              component="span"
              disabled={isUploading}
            >
              Select APK File
            </Button>
          </label>
          
          {file && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Selected file: {file.name}
            </Typography>
          )}
        </Box>

        {isUploading && (
          <Box sx={{ width: '100%', mb: 2 }}>
            <LinearProgress variant="determinate" value={uploadProgress} />
            <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
              {uploadProgress}% Uploaded
            </Typography>
          </Box>
        )}

        {uploadStatus && (
          <Alert severity={uploadStatus.success ? 'success' : 'error'} sx={{ mb: 2 }}>
            {uploadStatus.message}
          </Alert>
        )}

        <Button
          variant="contained"
          color="primary"
          startIcon={isUploading ? <CircularProgress size={20} color="inherit" /> : <UploadIcon />}
          onClick={handleUpload}
          disabled={!file || isUploading}
        >
          {isUploading ? 'Uploading...' : 'Upload APK'}
        </Button>
      </CardContent>
    </Card>
  );
}
