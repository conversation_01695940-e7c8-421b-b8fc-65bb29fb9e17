import React, { useEffect, useState } from 'react';
import { Box, Button, Typography, Paper, CircularProgress, TextField } from '@mui/material';
import mqtt from 'mqtt';

const DirectMqttTest = () => {
  const [status, setStatus] = useState('Disconnected');
  const [client, setClient] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState([]);
  const [brokerUrl, setBrokerUrl] = useState('wss://broker.emqx.io:8084/mqtt');
  const [message, setMessage] = useState('Hello from DirectMqttTest');
  const [topic, setTopic] = useState('aslaa/test');

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const connectMqtt = () => {
    setIsLoading(true);
    setStatus('Connecting...');
    setError(null);
    
    try {
      addLog(`Connecting to MQTT broker: ${brokerUrl}`);
      
      // Direct connection with URL
      const mqttClient = mqtt.connect(brokerUrl);
      
      mqttClient.on('connect', () => {
        setStatus('Connected');
        setClient(mqttClient);
        setIsLoading(false);
        addLog('Connected to MQTT broker successfully!');
      });
      
      mqttClient.on('message', (topic, message) => {
        const msg = message.toString();
        addLog(`Received message on ${topic}: ${msg}`);
      });
      
      mqttClient.on('error', (err) => {
        setStatus('Error');
        setError(err.message);
        setIsLoading(false);
        addLog(`Connection error: ${err.message}`);
        console.error('MQTT Error:', err);
      });
      
      mqttClient.on('close', () => {
        setStatus('Disconnected');
        setIsLoading(false);
        addLog('Connection closed');
      });
      
      mqttClient.on('offline', () => {
        setStatus('Offline');
        setIsLoading(false);
        addLog('Client is offline');
      });
      
      mqttClient.on('reconnect', () => {
        setStatus('Reconnecting');
        addLog('Attempting to reconnect...');
      });
    } catch (err) {
      setStatus('Error');
      setError(err.message);
      setIsLoading(false);
      addLog(`Exception: ${err.message}`);
      console.error('MQTT Connection Exception:', err);
    }
  };
  
  const disconnectMqtt = () => {
    if (client) {
      client.end();
      setClient(null);
      setStatus('Disconnected');
      addLog('Disconnected from MQTT broker');
    }
  };
  
  const handleSubscribe = () => {
    if (client && client.connected && topic) {
      client.subscribe(topic, (err) => {
        if (!err) {
          addLog(`Subscribed to ${topic}`);
        } else {
          addLog(`Error subscribing to ${topic}: ${err.message}`);
        }
      });
    } else {
      addLog('Cannot subscribe: client not connected or topic empty');
    }
  };
  
  const handlePublish = () => {
    if (client && client.connected && topic && message) {
      client.publish(topic, message, { qos: 0 }, (error) => {
        if (!error) {
          addLog(`Published to ${topic}: ${message}`);
        } else {
          addLog(`Error publishing: ${error.message}`);
        }
      });
    } else {
      addLog('Cannot publish: client not connected or topic/message empty');
    }
  };

  useEffect(() => {
    return () => {
      if (client) {
        client.end();
      }
    };
  }, [client]);

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Direct MQTT Connection Test
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ mr: 1 }}>
            Status:
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              color: status === 'Connected' ? 'green' : 
                     status === 'Connecting...' || status === 'Reconnecting' ? 'orange' : 
                     'error.main',
              fontWeight: 'bold'
            }}
          >
            {status}
          </Typography>
          {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
        </Box>
        
        {error && (
          <Typography color="error" sx={{ mb: 2 }}>
            Error: {error}
          </Typography>
        )}
        
        <Box sx={{ mb: 3 }}>
          <TextField
            label="Broker URL"
            variant="outlined"
            size="small"
            fullWidth
            value={brokerUrl}
            onChange={(e) => setBrokerUrl(e.target.value)}
            sx={{ mb: 2 }}
            disabled={status === 'Connected' || status === 'Connecting...'}
          />
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button 
              variant="contained" 
              onClick={connectMqtt}
              disabled={status === 'Connected' || status === 'Connecting...' || isLoading || !brokerUrl}
            >
              Connect
            </Button>
            
            <Button 
              variant="outlined" 
              onClick={disconnectMqtt}
              disabled={!client || status === 'Disconnected'}
            >
              Disconnect
            </Button>
          </Box>
        </Box>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Publish & Subscribe
          </Typography>
          
          <TextField
            label="Topic"
            variant="outlined"
            size="small"
            fullWidth
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <TextField
            label="Message"
            variant="outlined"
            size="small"
            fullWidth
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button 
              variant="contained" 
              onClick={handleSubscribe}
              disabled={!client || status !== 'Connected' || !topic}
            >
              Subscribe
            </Button>
            
            <Button 
              variant="contained" 
              color="secondary"
              onClick={handlePublish}
              disabled={!client || status !== 'Connected' || !topic || !message}
            >
              Publish
            </Button>
          </Box>
        </Box>
        
        <Typography variant="h6" gutterBottom>
          Logs
        </Typography>
        
        <Paper 
          variant="outlined" 
          sx={{ 
            p: 2, 
            maxHeight: 300, 
            overflow: 'auto',
            bgcolor: 'grey.900',
            fontFamily: 'monospace',
            fontSize: '0.875rem'
          }}
        >
          {logs.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No logs yet. Click "Connect" to start.
            </Typography>
          ) : (
            logs.map((log, index) => (
              <Typography key={index} variant="body2" color="grey.300" sx={{ mb: 0.5 }}>
                {log}
              </Typography>
            ))
          )}
        </Paper>
      </Paper>
    </Box>
  );
};

export default DirectMqttTest;
