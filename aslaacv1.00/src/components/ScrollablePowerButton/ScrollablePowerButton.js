import { useState, useCallback, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import Iconify from '../Iconify';

ScrollablePowerButton.propTypes = {
  onPowerAction: PropTypes.func.isRequired,
  onScheduleClick: PropTypes.func.isRequired,
  isEngineOn: PropTypes.bool,
  powerLabel: PropTypes.string,
  scheduleLabel: PropTypes.string,
  sx: PropTypes.object,
  disabled: PropTypes.bool,
  powerIcon: PropTypes.string,
  scheduleIcon: PropTypes.string,
  colorScheme: PropTypes.object,
  size: PropTypes.number,
  showScrollIndicator: PropTypes.bool,
};

export default function ScrollablePowerButton({
  onPowerAction,
  onScheduleClick,
  isEngineOn = false,
  powerLabel = 'Power',
  scheduleLabel = 'Schedule',
  sx = {},
  disabled = false,
  powerIcon = '⏻',
  scheduleIcon = 'material-symbols:alarm',
  colorScheme = {},
  size = 100,
  showScrollIndicator = true,
  ...other
}) {
  const [isScheduleMode, setIsScheduleMode] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const resetTimeoutRef = useRef(null);
  const scrollTimeoutRef = useRef(null);

  // Engine hold states (for 3-second countdown)
  const [isEngineHolding, setIsEngineHolding] = useState(false);
  const [engineHoldProgress, setEngineHoldProgress] = useState(0);
  const [engineHoldTimer, setEngineHoldTimer] = useState(null);

  // Auto-reset to power mode after 8 seconds
  const setResetTimeout = useCallback(() => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
    }
    resetTimeoutRef.current = setTimeout(() => {
      setIsScheduleMode(false);
    }, 8000);
  }, []);

  // Ref for the button element to add non-passive wheel listeners
  const buttonRef = useRef(null);

  // Handle scroll/swipe gestures
  const handleWheel = useCallback((event) => {
    if (disabled || isEngineHolding) return;

    // Check if this is an actual wheel event
    if (!event.deltaY && !event.deltaX && !event.deltaZ) {
      return;
    }

    try {
      event.preventDefault();
      event.stopPropagation();
    } catch (e) {
      console.warn('Cannot prevent default on passive wheel event');
    }

    setIsScrolling(true);

    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Determine scroll direction
    const deltaY = event.deltaY;
    const newMode = deltaY > 0; // Scroll down = Schedule mode, Scroll up = Power mode

    setIsScheduleMode(newMode);

    if (newMode) {
      setResetTimeout();
    } else {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
    }

    // Reset scrolling state after animation
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 300);
  }, [disabled, isEngineHolding, setResetTimeout]);

  // Handle touch gestures for mobile
  const touchStartY = useRef(0);
  const touchEndY = useRef(0);
  const touchStartTime = useRef(0);

  const handleTouchStart = useCallback((event) => {
    if (disabled || isEngineHolding) return;

    // If in power mode, handle engine hold logic
    if (!isScheduleMode) {
      // Start engine hold countdown
      setIsEngineHolding(true);
      setEngineHoldProgress(0);

      const startTime = Date.now();
      const duration = 3000; // 3 seconds

      const timer = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        setEngineHoldProgress(progress * 100);

        if (progress >= 1) {
          // Hold completed - execute power command
          clearInterval(timer);
          setIsEngineHolding(false);
          setEngineHoldProgress(0);
          onPowerAction(event);
        }
      }, 50);

      setEngineHoldTimer(timer);
    }

    touchStartY.current = event.touches[0].clientY;
    touchStartTime.current = Date.now();
  }, [disabled, isEngineHolding, isScheduleMode, onPowerAction]);

  const handleTouchMove = useCallback((event) => {
    if (disabled) return;
    touchEndY.current = event.touches[0].clientY;
  }, [disabled]);

  const handleTouchEnd = useCallback((event) => {
    if (disabled) return;

    // Handle engine hold release
    if (engineHoldTimer) {
      clearInterval(engineHoldTimer);
      setEngineHoldTimer(null);
    }
    setIsEngineHolding(false);
    setEngineHoldProgress(0);

    const deltaY = touchStartY.current - touchEndY.current;
    const touchDuration = Date.now() - touchStartTime.current;
    const minSwipeDistance = 40;

    // Only treat as swipe if distance and duration criteria are met
    if (Math.abs(deltaY) > minSwipeDistance && touchDuration > 100 && touchDuration < 1000) {
      try {
        event.preventDefault();
        event.stopPropagation();
      } catch (e) {
        console.warn('Cannot prevent default on passive touch event');
      }

      setIsScrolling(true);

      // Swipe up = Schedule mode, Swipe down = Power mode
      const newMode = deltaY > 0;
      setIsScheduleMode(newMode);

      if (newMode) {
        setResetTimeout();
      } else {
        if (resetTimeoutRef.current) {
          clearTimeout(resetTimeoutRef.current);
        }
      }

      setTimeout(() => {
        setIsScrolling(false);
      }, 300);
    } else if (!isScheduleMode && touchDuration < 200) {
      // Quick tap in power mode - execute power action
      onPowerAction(event);
    }
  }, [disabled, engineHoldTimer, isScheduleMode, setResetTimeout, onPowerAction]);
  // Handle mouse events for desktop
  const handleMouseDown = useCallback((event) => {
    if (disabled || isEngineHolding || isScheduleMode) return;

    event.preventDefault();

    // Start engine hold countdown
    setIsEngineHolding(true);
    setEngineHoldProgress(0);

    const startTime = Date.now();
    const duration = 3000; // 3 seconds

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      setEngineHoldProgress(progress * 100);

      if (progress >= 1) {
        // Hold completed - execute power command
        clearInterval(timer);
        setIsEngineHolding(false);
        setEngineHoldProgress(0);
        onPowerAction(event);
      }
    }, 50);

    setEngineHoldTimer(timer);
  }, [disabled, isEngineHolding, isScheduleMode, onPowerAction]);

  const handleMouseUp = useCallback((event) => {
    if (event) {
      event.preventDefault();
    }

    if (engineHoldTimer) {
      clearInterval(engineHoldTimer);
      setEngineHoldTimer(null);
    }
    setIsEngineHolding(false);
    setEngineHoldProgress(0);
  }, [engineHoldTimer]);

  // Handle button click (for schedule mode)
  const handleClick = useCallback((event) => {
    if (disabled || isScrolling || isEngineHolding) return;

    if (event.type !== 'click') return;

    if (isScheduleMode) {
      onScheduleClick(event);
    }
    // Power mode clicks are handled by mouse/touch hold events
  }, [disabled, isScrolling, isEngineHolding, isScheduleMode, onScheduleClick]);

  // Setup non-passive wheel event listener
  useEffect(() => {
    const buttonElement = buttonRef.current;
    if (!buttonElement) return;

    const wheelHandler = (event) => {
      handleWheel(event);
    };

    try {
      buttonElement.addEventListener('wheel', wheelHandler, { passive: false });
    } catch (e) {
      buttonElement.addEventListener('wheel', wheelHandler);
    }

    return () => {
      if (buttonElement) {
        buttonElement.removeEventListener('wheel', wheelHandler);
      }
    };
  }, [handleWheel]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      if (engineHoldTimer) {
        clearInterval(engineHoldTimer);
      }
    };
  }, [engineHoldTimer]);

  // Get current colors and content based on mode
  const currentColors = isScheduleMode
    ? colorScheme.schedule
    : (isEngineOn ? colorScheme.power.on : colorScheme.power.off);

  const currentIcon = isScheduleMode ? scheduleIcon : powerIcon;
  const currentLabel = isScheduleMode ? scheduleLabel : powerLabel;

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx,
      }}
      {...other}
    >
      {/* Scroll Indicator */}
      <AnimatePresence>
        {showScrollIndicator && !isScheduleMode && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            style={{
              position: 'absolute',
              top: -25,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              pointerEvents: 'none',
              zIndex: 10,
            }}
          >
            <motion.div
              animate={{
                y: [0, -3, 0],
                opacity: [0.6, 1, 0.6]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Iconify
                icon="material-symbols:keyboard-arrow-up"
                width={16}
                height={16}
                sx={{
                  color: colorScheme.schedule?.primary || '#9333ea',
                  filter: `drop-shadow(0 0 4px ${colorScheme.schedule?.glow || 'rgba(147, 51, 234, 0.4)'})`
                }}
              />
            </motion.div>
            <Typography
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: colorScheme.schedule?.primary || '#9333ea',
                textShadow: `0 0 4px ${colorScheme.schedule?.glow || 'rgba(147, 51, 234, 0.4)'}`,
                mt: 0.5,
              }}
            >
              Schedule
            </Typography>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Button */}
      <motion.div
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        animate={{
          scale: isScrolling ? 1.05 : 1,
          rotateY: isScrolling ? (isScheduleMode ? 180 : 0) : 0,
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut"
        }}
      >
        <Box
          ref={buttonRef}
          role="button"
          onClick={handleClick}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onTouchCancel={handleMouseUp}
          onContextMenu={(e) => e.preventDefault()}
          sx={{
            width: size,
            height: size,
            borderRadius: '16px',
            background: '#262626', // Premium dark gray for automotive feel
            border: 'none', // Remove all borders
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)', // Clean subtle shadow
            transition: "all 0.15s cubic-bezier(0.4, 0, 0.2, 1)", // Faster, smoother transitions
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: disabled ? 'not-allowed' : 'pointer',
            position: 'relative',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
            WebkitTouchCallout: 'none',
            WebkitTapHighlightColor: 'transparent',
            opacity: disabled ? 0.6 : 1,
            '&::before': {
              content: '""',
              position: 'absolute',
              top: -3,
              left: -3,
              right: -3,
              bottom: -3,
              borderRadius: '50%',
              background: `linear-gradient(45deg, ${currentColors?.accent || 'rgba(220, 38, 38, 0.3)'}, rgba(255,255,255,0.1))`,
              opacity: 0,
              transition: 'opacity 0.3s ease',
              zIndex: 0,
            },
            '&:hover::before': {
              opacity: 1,
            },
            '&:hover': {
              transform: 'translateY(-1px)', // Subtle elevation
              boxShadow: `
                0 4px 8px rgba(0, 0, 0, 0.2),
                inset 0 0 5px ${currentColors?.accent || '#14b8a6'}30
              `, // Increased shadow + inner glow
            },
            '&:active': {
              transform: 'translateY(1px)', // Depression effect
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.15)', // Reduced shadow on press
              transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)', // Smooth transition
            },
          }}
        >
          {/* Progress fill animation for hold gesture (only in power mode) */}
          {!isScheduleMode && (
            <Box
              sx={{
                position: 'absolute',
                top: '7.5%',
                left: '7.5%',
                width: '85%',
                height: '85%',
                borderRadius: '50%',
                background: `conic-gradient(from 0deg, rgba(0,255,100,0.8) 0%, rgba(0,255,100,0.8) ${engineHoldProgress}%, transparent ${engineHoldProgress}%, transparent 100%)`,
                opacity: isEngineHolding ? 1 : 0,
                transition: 'opacity 0.2s',
                zIndex: 3,
              }}
            />
          )}

          {/* Icon with smooth transition */}
          <motion.div
            key={currentIcon}
            initial={{ opacity: 0, scale: 0.8, rotateY: 180 }}
            animate={{ opacity: 1, scale: 1, rotateY: 0 }}
            exit={{ opacity: 0, scale: 0.8, rotateY: -180 }}
            transition={{ duration: 0.3 }}
          >
            {isScheduleMode ? (
              <Iconify
                icon={currentIcon}
                width={36}
                height={36}
                sx={{
                  color: currentColors?.text || '#ffffff',
                  mb: 0.5,
                  pointerEvents: 'none',
                  zIndex: 1,
                }}
              />
            ) : (
              <Box
                sx={{
                  fontSize: '28px', // Slightly smaller for cleaner look
                  color: currentColors?.icon || '#f0f0f0', // Clean off-white
                  filter: 'none', // Remove glow effects
                  transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                  mb: 1,
                  fontWeight: 300,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                  pointerEvents: 'none',
                  zIndex: 4,
                }}
              >
                {powerIcon}
              </Box>
            )}
          </motion.div>

          {/* Label with smooth transition */}
          <motion.div
            key={currentLabel}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Typography
              variant="caption"
              sx={{
                color: '#f0f0f0', // Clean off-white text
                fontSize: '14px', // Consistent 14px font size
                fontWeight: 300, // Light font weight
                textShadow: 'none', // Remove all text shadows
                transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                textAlign: 'center',
                lineHeight: 1.2,
                maxWidth: '70px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                userSelect: 'none',
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                pointerEvents: 'none',
                zIndex: 1,
                fontFamily: '"Inter", "SF Pro Display", "Roboto", sans-serif',
                mt: 1, // 8px spacing between icon and label
              }}
            >
              {!isScheduleMode && isEngineHolding
                ? `${Math.ceil((3000 - (engineHoldProgress * 30)) / 1000)}s`
                : currentLabel
              }
            </Typography>
          </motion.div>
        </Box>
      </motion.div>
    </Box>
  );
}