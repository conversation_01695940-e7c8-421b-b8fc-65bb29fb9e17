import React, { useEffect, useState } from 'react';
import { Box, Button, Typography, Paper, CircularProgress } from '@mui/material';
import mqtt from 'mqtt';

const SimpleMqttTest = () => {
  const [status, setStatus] = useState('Disconnected');
  const [client, setClient] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const connectMqtt = () => {
    setIsLoading(true);
    setStatus('Connecting...');
    setError(null);

    try {
      // Direct connection options
      const options = {
        clientId: `aslaacv_${Math.random().toString(16).substring(2, 10)}`,
        clean: true,
        connectTimeout: 4000,
        reconnectPeriod: 1000,
      };

      addLog('Connecting to MQTT broker...');

      // Connect to the specified IP and port
      const mqttClient = mqtt.connect('ws://*************:8083/mqtt', options);

      mqttClient.on('connect', () => {
        setStatus('Connected');
        setClient(mqttClient);
        setIsLoading(false);
        addLog('Connected to MQTT broker successfully!');

        // Subscribe to a test topic
        mqttClient.subscribe('aslaa/test', (err) => {
          if (!err) {
            addLog('Subscribed to aslaa/test topic');
            // Publish a test message
            mqttClient.publish('aslaa/test', 'Hello from SimpleMqttTest', { qos: 0 }, (error) => {
              if (!error) {
                addLog('Published test message to aslaa/test');
              } else {
                addLog(`Error publishing: ${error.message}`);
              }
            });
          } else {
            addLog(`Error subscribing: ${err.message}`);
          }
        });
      });

      mqttClient.on('message', (topic, message) => {
        const msg = message.toString();
        addLog(`Received message on ${topic}: ${msg}`);
      });

      mqttClient.on('error', (err) => {
        setStatus('Error');
        setError(err.message);
        setIsLoading(false);
        addLog(`Connection error: ${err.message}`);
        console.error('MQTT Error:', err);
      });

      mqttClient.on('close', () => {
        setStatus('Disconnected');
        setIsLoading(false);
        addLog('Connection closed');
      });

      mqttClient.on('offline', () => {
        setStatus('Offline');
        setIsLoading(false);
        addLog('Client is offline');
      });

      mqttClient.on('reconnect', () => {
        setStatus('Reconnecting');
        addLog('Attempting to reconnect...');
      });
    } catch (err) {
      setStatus('Error');
      setError(err.message);
      setIsLoading(false);
      addLog(`Exception: ${err.message}`);
      // console.error('MQTT Connection Exception:', err);
    }
  };

  const disconnectMqtt = () => {
    if (client) {
      client.end();
      setClient(null);
      setStatus('Disconnected');
      addLog('Disconnected from MQTT broker');
    }
  };

  const publishTestMessage = () => {
    if (client && client.connected) {
      client.publish('aslaa/test', `Test message at ${new Date().toLocaleTimeString()}`, { qos: 0 }, (error) => {
        if (!error) {
          addLog('Published test message');
        } else {
          addLog(`Error publishing: ${error.message}`);
        }
      });
    } else {
      addLog('Cannot publish: client not connected');
    }
  };

  useEffect(() => {
    return () => {
      if (client) {
        client.end();
      }
    };
  }, [client]);

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Simple MQTT Connection Test (*************:8083)
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ mr: 1 }}>
            Status:
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: status === 'Connected' ? 'green' :
                     status === 'Connecting...' || status === 'Reconnecting' ? 'orange' :
                     'error.main',
              fontWeight: 'bold'
            }}
          >
            {status}
          </Typography>
          {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
        </Box>

        {error && (
          <Typography color="error" sx={{ mb: 2 }}>
            Error: {error}
          </Typography>
        )}

        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <Button
            variant="contained"
            onClick={connectMqtt}
            disabled={status === 'Connected' || status === 'Connecting...' || isLoading}
          >
            Connect
          </Button>

          <Button
            variant="outlined"
            onClick={disconnectMqtt}
            disabled={!client || status === 'Disconnected'}
          >
            Disconnect
          </Button>

          <Button
            variant="contained"
            color="secondary"
            onClick={publishTestMessage}
            disabled={!client || status !== 'Connected'}
          >
            Publish Test Message
          </Button>
        </Box>

        <Typography variant="h6" gutterBottom>
          Logs
        </Typography>

        <Paper
          variant="outlined"
          sx={{
            p: 2,
            maxHeight: 300,
            overflow: 'auto',
            bgcolor: 'grey.900',
            fontFamily: 'monospace',
            fontSize: '0.875rem'
          }}
        >
          {logs.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No logs yet. Click "Connect" to start.
            </Typography>
          ) : (
            logs.map((log, index) => (
              <Typography key={index} variant="body2" color="grey.300" sx={{ mb: 0.5 }}>
                {log}
              </Typography>
            ))
          )}
        </Paper>
      </Paper>
    </Box>
  );
};

export default SimpleMqttTest;
