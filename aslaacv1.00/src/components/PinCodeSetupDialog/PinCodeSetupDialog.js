import PropTypes from 'prop-types';
import { useState, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  Stack,
  Typography,
  Button,
  TextField,
  Divider,
  IconButton,
  Alert,
} from '@mui/material';

import { useSnackbar } from 'notistack';
import { useTranslation } from 'react-i18next';
import Iconify from '../components/Iconify';
import useAuth from '../hooks/useAuth';
import axios from '../utils/axios';

// ----------------------------------------------------------------------

PinCodeSetupDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  onSuccess: PropTypes.func,
  phoneNumber: PropTypes.string, // if needed by your backend
};

export default function PinCodeSetupDialog({
  open = false,
  onClose = () => {},
  onSuccess = () => {},
  phoneNumber,
  ...other
}) {
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const { initialize } = useAuth();

  // Refs for the two PIN fields
  const newPinRef = useRef('');
  const confirmPinRef = useRef('');

  // Local state for errors
  const [mismatch, setMismatch] = useState(false);
  const [error, setError] = useState('');

  // Handlers for updating Refs
  const handleNewPinChange = (e) => {
    setMismatch(false);
    setError('');
    newPinRef.current = e.target.value;
  };

  const handleConfirmPinChange = (e) => {
    setMismatch(false);
    setError('');
    confirmPinRef.current = e.target.value;
  };

  const handleSetPin = async () => {
    // Clear old errors
    setError('');
    setMismatch(false);

    const newPin = newPinRef.current;
    const confirmPin = confirmPinRef.current;

    // Basic checks
    if (!newPin || !confirmPin) {
      setError('Please fill all required fields.');
      return;
    }
    if (newPin !== confirmPin) {
      setMismatch(true);
      return;
    }

    try {
      // Call your backend endpoint (adjust if needed)
      const response = await axios.post('/api/auth/set-pin', {
        phoneNumber,
        newPinCode: newPin,
      });

      if (response.data.success) {
        // Re-initialize auth (refresh user info)
        initialize();

        // Show success message
        enqueueSnackbar(response.data.message || 'Pin code set successfully!', {
          variant: 'success',
        });

        // Let parent know we succeeded
        onSuccess(newPin);

        // Close the dialog
        onClose();
      } else {
        setError(response.data.message || 'Error setting pin code.');
      }
    } catch (err) {
      setError('An error occurred while setting pin code.');
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="xs"
      {...other}
    >
      <Card sx={{ bgcolor: 'primary.dark', p: 3, position: 'relative' }}>
        {/* Title */}
        <Stack
          direction="row"
          spacing={2}
          alignItems="center"
          justifyContent="center"
          color="text.secondary"
        >
          <Iconify icon="ic:round-security" width={24} height={24} />
          <Typography variant="h4">
            {t('words.create_code')}
          </Typography>
        </Stack>

        <Typography
          variant="subtitle1"
          sx={{ textAlign: 'center', mb: 2 }}
          color="text.secondary"
        >
          {t('pinModal.newpin')}
        </Typography>

        {/* Close Button */}
        <IconButton
          sx={{ position: 'absolute', right: 10, top: 10 }}
          onClick={onClose}
        >
          <Iconify icon="eva:close-fill" width={30} height={30} />
        </IconButton>

        <Divider sx={{ mb: 3 }} />

        <DialogContent>
          <Stack spacing={2}>
            <TextField
              type="password"
              label={t('words.new_pin')}
              onChange={handleNewPinChange}
            />

            <TextField
              type="password"
              label={t('words.confirm_pin')}
              onChange={handleConfirmPinChange}
            />

            {/* Show mismatch message if pins don't match */}
            {mismatch && (
              <Alert severity="error">
                {t('pinModal.mismatch_error') || 'Pins do not match!'}
              </Alert>
            )}

            {/* General error */}
            {error && <Alert severity="error">{error}</Alert>}
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>
            {t('words.cancel') || 'Cancel'}
          </Button>
          <Button
            variant="contained"
            onClick={handleSetPin}
            fullWidth
          >
            {t('words.save_change') || 'Save Pin'}
          </Button>
        </DialogActions>
      </Card>
    </Dialog>
  );
}