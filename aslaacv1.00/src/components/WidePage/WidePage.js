import PropTypes from 'prop-types';
import { Helmet } from 'react-helmet-async';
import { forwardRef } from 'react';
// @mui
import { Box, Container } from '@mui/material';

// ----------------------------------------------------------------------

const WidePage = forwardRef(({ children, title = '', meta, ...other }, ref) => (
  <>
    <Helmet>
      <title>{title}</title>
      {meta}
    </Helmet>

    <Box ref={ref} {...other}>

      {children}


    </Box>
  </>
));

WidePage.propTypes = {
  children: PropTypes.node.isRequired,
  title: PropTypes.string,
  meta: PropTypes.node,
};

export default WidePage;
