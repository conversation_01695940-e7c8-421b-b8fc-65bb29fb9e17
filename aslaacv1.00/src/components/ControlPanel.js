
import PropTypes from 'prop-types';

// @mui
import { useTheme } from '@mui/material/styles';
import { Box } from '@mui/material';
import Iconify from './Iconify';

// ----------------------------------------------------------------------

CarTop.propTypes = {
  sx: PropTypes.object,
  handleLocation:PropTypes.func,
  handleTurnOn:PropTypes.func,
  handleTurnOff:PropTypes.func,
  handleLock:PropTypes.func,
  handleUnlock:PropTypes.func,
};



export default function CarTop({ sx,handleTurnOn,handleTurnOff,handleLock,handleUnlock,handleLocation }) {
  const theme = useTheme();
  const PRIMARY_LIGHTER = theme.palette.grey[500_24];
  const PRIMARY_MAIN = theme.palette.grey[500_48];
  // theme.palette.primary.main;
  const PRIMARY_DARK = theme.palette.grey[500_56];
  const PRIMAY_HOVER = 'white';
  const controlSvg = (
    <Box sx={{ width: {xs:"150px", sm:"180px", md:"200px"},  height:{sx:"150px",sm:"180px", height:"200px"}, ...sx }}>
      
      <svg width="100%" height="100%" viewBox="0 0 406 406"
        xmlns="http://www.w3.org/2000/svg" version="1.1" style={{ filter: 'drop-shadow(3px 3px 2px #000)' }}>

        <g stroke={PRIMARY_MAIN} strokeWidth="3" fill={PRIMARY_LIGHTER} style={{ cursor: 'pointer' }} className={'control'} onClick = {handleLock}>
          <g transform="translate(125,275) rotate(180 0 0) scale(3,-3)" >
            <path fill={PRIMAY_HOVER} stroke="none" d="M4 8V6a6 6 0 1 1 12 0v2h1a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2h1zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0zM7 6v2h6V6a3 3 0 0 0-6 0z" />
          </g>
          <path d="M0 200 h100 A100 100 0 0 0 200 300 v 100 A 200 200 0 0 1 0 200" />
        </g>
        <g stroke={PRIMARY_MAIN} strokeWidth="3" fill={PRIMARY_LIGHTER} style={{ cursor: 'pointer' }} onClick = {handleUnlock}>
          <g transform="translate(125,60) rotate(180 0 0) scale(3,-3)" >
            <path fill={PRIMAY_HOVER} stroke="none" d="M4 8V6a6 6 0 1 1 12 0h-3v2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2h1zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0zM7 6v2h6V6a3 3 0 0 0-6 0z" />
            
          </g>
          <path d="M0 200 h100 A100 100 0 0 1 200 100 v -100 A 200 200 0 0 0 0 200" />
        </g>
        <g stroke={PRIMARY_MAIN} strokeWidth="3" fill={PRIMARY_LIGHTER} style={{ cursor: 'pointer' }} onClick = {handleTurnOff}>
          <g transform="translate(275,125) rotate(0 180 0) scale(3,-3)" >
          <path   fill={PRIMAY_HOVER} stroke = "none" d="M10 11c-.57 0-1 .45-1 1s.43 1 1 1c.54 0 1-.45 1-1s-.46-1-1-1m.5-9c4.5 0 4.59 3.57 2.23 4.75c-.99.49-1.43 1.54-1.62 2.47c.48.2.89.51 1.22.91c3.7-2 7.67-1.21 7.67 2.37c0 4.5-3.57 4.6-4.74 2.23c-.5-.99-1.56-1.43-2.49-1.62c-.2.48-.51.89-.91 1.23C13.85 18.03 13.06 22 9.5 22c-4.5 0-4.6-3.58-2.24-4.76c.98-.49 1.42-1.53 1.62-2.45c-.49-.2-.92-.52-1.24-.92C3.95 15.85 0 15.07 0 11.5C0 7 3.56 6.89 4.73 9.26c.5.99 1.55 1.42 2.48 1.61c.19-.48.51-.9.92-1.22C6.14 5.96 6.93 2 10.5 2M22 13V7h2v6h-2m0 4v-2h2v2h-2Z"/>
          </g>
          <path d="M400 200 h-100 A100 100 0 0 0 200 100 v -100 A 200 200 0 0 1 400 200" />
        </g>
        <g stroke={PRIMARY_MAIN} strokeWidth="3" fill={PRIMARY_LIGHTER} style={{ cursor: 'pointer' }} onClick = {handleTurnOn}>
          <g transform="translate(340,270) rotate(180 0 0) scale(3.8,-3.8)"  fill={PRIMAY_HOVER} stroke="none" >
          <path d="M10 3c0 1.313-.304 2.508-.8 3.4a1.991 1.991 0 0 0-1.484-.38c-.28-.982-.91-2.04-1.838-2.969a8.368 8.368 0 0 0-.491-.454A5.976 5.976 0 0 1 8 2c.691 0 1.355.117 1.973.332c.018.219.027.442.027.668Zm0 5c0 .073-.004.146-.012.217c1.018-.019 2.2-.353 3.331-1.006a8.39 8.39 0 0 0 .57-.361a6.004 6.004 0 0 0-2.53-3.823a9.02 9.02 0 0 1-.145.64c-.34 1.269-.944 2.346-1.656 3.079c.277.343.442.78.442 1.254Zm-.137.728a2.007 2.007 0 0 1-1.07 1.109c.525.87 1.405 1.725 2.535 2.377c.2.116.402.222.605.317a5.986 5.986 0 0 0 2.053-4.111c-.208.073-.421.14-.641.199c-1.264.339-2.493.356-3.482.11ZM8 10c-.45 0-.866-.149-1.2-.4c-.494.89-.796 2.082-.796 3.391c0 .23.01.457.027.678A5.99 5.99 0 0 0 8 14c.94 0 1.83-.216 2.623-.602a8.359 8.359 0 0 1-.497-.458c-.925-.926-1.555-1.981-1.836-2.96c-.094.013-.191.02-.29.02ZM6 8c0-.08.005-.16.014-.239c-1.02.017-2.205.351-3.34 1.007a8.366 8.366 0 0 0-.568.359a6.003 6.003 0 0 0 2.525 3.839a8.37 8.37 0 0 1 .148-.653c.34-1.267.94-2.342 1.65-3.075A1.988 1.988 0 0 1 6 8Zm-3.347-.632c1.267-.34 2.498-.355 3.488-.107c.196-.494.583-.89 1.07-1.1c-.524-.874-1.406-1.733-2.541-2.388a8.363 8.363 0 0 0-.594-.312a5.987 5.987 0 0 0-2.06 4.106c.206-.074.418-.14.637-.199ZM8 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z"/>
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14Zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16Z"/>
          </g>
          <path d="M400 200 h-100 A100 100 0 0 1 200 300 v 100 A 200 200 0 0 0 400 200" />
        </g>
        <g stroke={PRIMARY_MAIN} strokeWidth="3" fill={PRIMARY_LIGHTER} style={{ cursor: 'pointer' }} onClick = {handleLocation}>
          <g transform="translate(270,130) rotate(180 0 0) scale(6,-6)" >
            <path fill={PRIMARY_DARK} stroke="none" d="M12 2C7.589 2 4 5.589 4 9.995C3.971 16.44 11.696 21.784 12 22c0 0 8.029-5.56 8-12c0-4.411-3.589-8-8-8zm0 12c-2.21 0-4-1.79-4-4s1.79-4 4-4s4 1.79 4 4s-1.79 4-4 4z" />
          </g>
          <circle cx="200" cy="200" r="96"  />
        </g>
        
      </svg>



    </Box>
  );
  return controlSvg;
}
