import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Alert,
  Link,
  Divider,
  InputAdornment,
  IconButton
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { Security, Visibility, VisibilityOff } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import axios from '../utils/axios';

const TwoFactorVerification = ({ 
  open, 
  onClose, 
  onSuccess, 
  phoneNumber,
  title = "Two-Factor Authentication Required" 
}) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [useBackupCode, setUseBackupCode] = useState(false);
  const [backupCode, setBackupCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showBackupCode, setShowBackupCode] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    if (open) {
      setVerificationCode('');
      setBackupCode('');
      setError('');
      setUseBackupCode(false);
      setShowBackupCode(false);
    }
  }, [open]);

  const handleVerify = async () => {
    const code = useBackupCode ? backupCode : verificationCode;
    
    if (!code) {
      setError('Please enter a verification code');
      return;
    }

    if (!useBackupCode && code.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    if (useBackupCode && code.length !== 8) {
      setError('Please enter a valid 8-character backup code');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const response = await axios.post('/api/2fa/verify', {
        token: code,
        phoneNumber: phoneNumber,
        isBackupCode: useBackupCode
      });
      
      if (response.data.success) {
        enqueueSnackbar('2FA verification successful!', { variant: 'success' });
        onSuccess();
      } else {
        setError(response.data.message || 'Invalid verification code');
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      setError(error.response?.data?.message || 'Failed to verify code');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleVerify();
    }
  };

  const toggleBackupCode = () => {
    setUseBackupCode(!useBackupCode);
    setError('');
    setVerificationCode('');
    setBackupCode('');
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="sm" 
      fullWidth
      disableEscapeKeyDown
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Security color="primary" />
          <Typography variant="h6">{title}</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Please enter the 6-digit code from your Google Authenticator app to continue.
          </Typography>
        </Alert>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {!useBackupCode ? (
          <Box>
            <TextField
              label="Verification Code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              fullWidth
              placeholder="Enter 6-digit code"
              inputProps={{ 
                maxLength: 6, 
                style: { textAlign: 'center', fontSize: '1.5em', letterSpacing: '0.5em' } 
              }}
              onKeyPress={handleKeyPress}
              autoFocus
              sx={{ mb: 2 }}
            />
            
            <Box textAlign="center">
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Can't access your authenticator app?
              </Typography>
              <Link 
                component="button" 
                variant="body2" 
                onClick={toggleBackupCode}
                sx={{ cursor: 'pointer' }}
              >
                Use a backup code instead
              </Link>
            </Box>
          </Box>
        ) : (
          <Box>
            <TextField
              label="Backup Code"
              value={backupCode}
              onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-F0-9]/g, '').slice(0, 8))}
              fullWidth
              placeholder="Enter 8-character backup code"
              type={showBackupCode ? 'text' : 'password'}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowBackupCode(!showBackupCode)}
                      edge="end"
                    >
                      {showBackupCode ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
                style: { fontFamily: 'monospace' }
              }}
              onKeyPress={handleKeyPress}
              autoFocus
              sx={{ mb: 2 }}
            />
            
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Each backup code can only be used once. After using this code, 
                make sure to generate new backup codes from your account settings.
              </Typography>
            </Alert>
            
            <Box textAlign="center">
              <Link 
                component="button" 
                variant="body2" 
                onClick={toggleBackupCode}
                sx={{ cursor: 'pointer' }}
              >
                Use authenticator app instead
              </Link>
            </Box>
          </Box>
        )}

        <Divider sx={{ my: 2 }} />
        
        <Typography variant="body2" color="text.secondary" textAlign="center">
          Having trouble? Contact support for assistance.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        
        <LoadingButton
          onClick={handleVerify}
          loading={loading}
          variant="contained"
          disabled={
            useBackupCode 
              ? backupCode.length !== 8 
              : verificationCode.length !== 6
          }
        >
          Verify
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

export default TwoFactorVerification;
