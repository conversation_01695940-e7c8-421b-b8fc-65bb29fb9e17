import PropTypes from 'prop-types';
import { Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

Button.propTypes = {
  sx: PropTypes.object,
  onClick: PropTypes.func.isRequired,
  svgContent: PropTypes.node.isRequired,
};

export default function Button({ sx, onClick, svgContent }) {
  const theme = useTheme();
  const PRIMARY_LIGHTER = theme.palette.grey[500_24];
  const PRIMARY_MAIN = theme.palette.grey[500_48];

  return (
    <Box
      sx={{ width: { xs: '150px', sm: '180px', md: '200px' }, height: { sx: '150px', sm: '180px', height: '200px' }, ...sx }}
      onClick={onClick}
    >
      <svg width="100%" height="100%" viewBox="0 0 406 406" xmlns="http://www.w3.org/2000/svg" version="1.1" style={{ filter: 'drop-shadow(3px 3px 2px #000)' }}>
        <g stroke={PRIMARY_MAIN} strokeWidth="3" fill={PRIMARY_LIGHTER} style={{ cursor: 'pointer' }}>
          {svgContent}
        </g>
      </svg>
    </Box>
  );
}

Button.defaultProps = {
  sx: {},
  svgContent: null,
};
