import PropTypes from 'prop-types';
import { Box } from '@mui/material';

// ----------------------------------------------------------------------
ChipIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
}
export default function ChipIcon({ width = 150, height = 150, ...other }) {

  return (
    <Box {...other}>
      <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
        width={width} height={height} viewBox="0 0 225 225"
        preserveAspectRatio="xMidYMid meet" style={{ filter: `drop-shadow(0px 0px 15px )` }}>
        
        <g transform="translate(0, 225) scale(0.1,-0.1)" 
          fill="currentColor" stroke="none">
          <path d="M825 2231 c-80 -21 -201 -102 -191 -128 3 -8 2 -12 -3 -8 -11 6 -47
                  -37 -56 -66 -4 -10 -35 -32 -72 -50 -121 -58 -199 -166 -211 -292 -4 -49 -12
                  -70 -38 -104 -19 -24 -39 -62 -46 -84 -7 -25 -20 -44 -32 -49 -32 -12 -90 -78
                  -124 -140 -79 -142 -56 -329 55 -452 22 -24 45 -63 52 -88 14 -49 68 -128 107
                  -159 19 -15 26 -32 30 -68 10 -104 97 -219 205 -271 48 -23 65 -39 98 -89 42
                  -66 77 -96 161 -139 49 -26 65 -29 150 -29 84 0 102 3 153 29 l58 28 62 -28
                  c54 -26 74 -29 157 -29 89 0 99 2 157 34 72 38 143 106 169 161 13 26 29 41
                  54 50 127 46 219 169 237 317 4 35 14 65 25 78 11 11 32 47 48 80 19 39 42 68
                  65 84 40 27 117 138 133 191 39 133 2 293 -88 380 -21 21 -39 49 -43 69 -11
                  55 -48 120 -92 160 -32 29 -42 48 -52 92 -25 123 -98 217 -208 269 -46 22 -66
                  38 -75 60 -21 50 -109 130 -179 164 -59 28 -76 31 -156 31 -78 0 -98 -4 -150
                  -28 l-59 -28 -61 27 c-64 29 -180 41 -240 25z m176 -87 c30 -9 69 -27 87 -40
                  30 -24 34 -24 60 -10 93 52 118 60 187 60 119 -1 216 -61 270 -168 25 -49 33
                  -56 84 -75 109 -40 173 -122 188 -242 7 -51 12 -64 26 -63 9 1 15 -2 12 -6 -3
                  -5 8 -18 24 -29 33 -24 81 -112 81 -149 0 -15 19 -45 53 -81 31 -34 59 -77 70
                  -107 21 -61 22 -156 1 -218 -17 -49 -101 -151 -133 -161 -11 -3 -24 -19 -30
                  -35 -6 -17 -16 -42 -21 -58 -6 -15 -25 -43 -42 -61 -28 -30 -33 -44 -39 -108
                  -13 -127 -79 -216 -191 -254 -47 -16 -56 -23 -81 -71 -56 -104 -138 -162 -246
                  -175 -57 -7 -142 16 -194 51 l-36 25 -70 -36 c-61 -32 -79 -36 -143 -37 -122
                  0 -228 66 -275 172 -12 29 -29 54 -36 56 -78 22 -108 36 -140 65 -50 45 -85
                  115 -93 187 -5 40 -13 61 -28 73 -73 57 -115 124 -116 187 0 11 -23 41 -50 67
                  -83 82 -111 202 -75 325 16 53 90 145 138 171 17 9 29 28 38 60 8 26 29 63 51
                  87 35 38 39 48 42 108 7 125 78 220 191 257 44 15 53 23 80 73 52 95 112 144
                  202 165 58 14 94 12 154 -5z" />
          <path d="M846 1781 c-14 -16 -17 -34 -14 -99 l3 -80 -47 -6 c-78 -11 -138 -76
                  -138 -150 l0 -35 -91 -3 c-75 -2 -94 -6 -103 -20 -8 -13 -8 -23 0 -35 9 -15
                  26 -18 103 -18 l91 0 0 -82 0 -83 -81 0 c-92 0 -119 -10 -119 -45 0 -36 31
                  -47 121 -43 l79 3 0 -82 0 -82 -91 -3 c-75 -2 -94 -6 -103 -20 -8 -13 -8 -23
                  0 -35 9 -15 28 -19 103 -21 l91 -3 0 -35 c0 -20 7 -51 16 -70 20 -41 87 -84
                  132 -84 l32 0 0 -82 c1 -86 12 -118 43 -118 31 0 47 41 47 122 l0 78 80 0 80
                  0 0 -82 c1 -85 12 -118 42 -118 28 0 37 25 40 110 l3 85 83 3 82 3 0 -91 c0
                  -86 1 -91 25 -102 43 -20 55 2 55 102 l0 87 46 6 c75 8 131 62 141 135 l6 42
                  76 0 c105 0 149 31 103 73 -14 13 -38 17 -100 17 l-82 0 0 80 0 80 78 0 c106
                  0 150 31 104 73 -14 13 -38 17 -100 17 l-82 0 0 80 0 80 83 0 c72 0 87 3 103
                  21 14 15 16 24 8 37 -9 14 -30 18 -101 20 l-90 3 -6 46 c-8 77 -64 132 -141
                  140 l-46 6 0 86 c0 90 -8 111 -42 111 -28 0 -38 -33 -38 -119 l0 -81 -85 0
                  -84 0 -3 91 c-2 73 -6 94 -20 103 -13 8 -22 6 -38 -9 -19 -17 -21 -28 -18
                  -102 l3 -83 -85 0 -85 0 3 81 c3 66 0 84 -14 100 -9 10 -22 19 -29 19 -7 0
                  -20 -9 -29 -19z m652 -283 c17 -17 17 -729 0 -746 -17 -17 -729 -17 -746 0
                  -17 17 -17 729 0 746 17 17 729 17 746 0z" />
          <path d="M1113 1452 c-20 -12 -293 -283 -309 -306 -10 -15 -14 -55 -14 -159
                  l0 -139 31 -29 31 -29 275 0 275 0 29 29 29 29 0 267 c0 282 -4 310 -49 335
                  -23 12 -280 14 -298 2z m267 -327 l0 -255 -255 0 -255 0 0 117 0 118 137 137
                  138 138 117 0 118 0 0 -255z"/>
        </g>
      </svg>

    </Box>
  )
}