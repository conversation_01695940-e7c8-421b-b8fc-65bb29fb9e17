import { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Alert,
  Stack
} from '@mui/material';
import ScrollablePowerButton from '../ScrollablePowerButton';

export default function InteractionMethodDemo() {
  const [lastAction, setLastAction] = useState('');

  const handlePowerClick = () => {
    setLastAction('Power button clicked');
  };

  const handleScheduleClick = () => {
    setLastAction('Schedule dialog opened');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom align="center">
        Enhanced Power Button with Schedule Indicator
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        The power button now features a clear, visible schedule indicator. Click the golden arrow above the button to access the scheduler without interfering with the existing 3-second countdown system.
      </Alert>

      {lastAction && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Last Action: {lastAction}
        </Alert>
      )}

      <Grid container spacing={3} justifyContent="center">
        <Grid item xs={12} md={6}>
          <Card sx={{ textAlign: 'center', p: 3 }}>
            <CardContent>
              <Stack spacing={3} alignItems="center">
                <Typography variant="h6" color="primary">
                  🎯 Visible Schedule Indicator
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Look for the animated golden arrow above the power button
                </Typography>

                <Box sx={{ my: 3, position: 'relative' }}>
                  <ScrollablePowerButton
                    onPowerClick={handlePowerClick}
                    onScheduleClick={handleScheduleClick}
                    powerLabel="Power"
                    scheduleLabel="Schedule"
                    powerIcon="carbon:flash-off"
                    scheduleIcon="material-symbols:alarm"
                    powerColor="red"
                    scheduleColor="warning.main"
                    showScheduleIndicator={true}
                  />
                </Box>

                <Typography variant="caption" color="text.secondary">
                  Click the golden arrow to transform the button into scheduler mode
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom align="center">
          Key Benefits of the Visible Indicator Approach:
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary" gutterBottom>
                  🔍 Discoverable
                </Typography>
                <Typography variant="body2">
                  Clear visual cue makes the scheduler feature immediately obvious to all users
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="subtitle2" color="success.main" gutterBottom>
                  🎯 Reliable
                </Typography>
                <Typography variant="body2">
                  Simple click interaction eliminates gesture detection issues and conflicts
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="subtitle2" color="warning.main" gutterBottom>
                  ♿ Accessible
                </Typography>
                <Typography variant="body2">
                  Works perfectly for users with different abilities and interaction preferences
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="subtitle2" color="info.main" gutterBottom>
                  🎨 Consistent
                </Typography>
                <Typography variant="body2">
                  Matches your existing UI design language with smooth animations
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}
