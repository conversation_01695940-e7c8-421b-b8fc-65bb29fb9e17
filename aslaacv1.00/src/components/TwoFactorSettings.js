import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typo<PERSON>,
  Box,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { 
  Security, 
  Warning, 
  CheckCircle, 
  Refresh,
  Download,
  ContentCopy
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import axios from '../utils/axios';
import TwoFactorSetup from './TwoFactorSetup';

const TwoFactorSettings = () => {
  const [twoFactorStatus, setTwoFactorStatus] = useState({
    twoFactorEnabled: false,
    twoFactorEnabledAt: null,
    unusedBackupCodes: 0,
    hasSecret: false
  });
  const [loading, setLoading] = useState(false);
  const [showSetup, setShowSetup] = useState(false);
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [showBackupDialog, setShowBackupDialog] = useState(false);
  const [disableCode, setDisableCode] = useState('');
  const [backupCode, setBackupCode] = useState('');
  const [newBackupCodes, setNewBackupCodes] = useState([]);
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    fetchTwoFactorStatus();
  }, []);

  const fetchTwoFactorStatus = async () => {
    try {
      const response = await axios.get('/api/2fa/status');
      if (response.data.success) {
        setTwoFactorStatus(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch 2FA status:', error);
    }
  };

  const handleDisable2FA = async () => {
    if (!disableCode || disableCode.length !== 6) {
      enqueueSnackbar('Please enter a valid 6-digit code', { variant: 'error' });
      return;
    }

    try {
      setLoading(true);
      const response = await axios.post('/api/2fa/disable', {
        token: disableCode
      });

      if (response.data.success) {
        enqueueSnackbar('2FA disabled successfully', { variant: 'success' });
        setShowDisableDialog(false);
        setDisableCode('');
        fetchTwoFactorStatus();
      } else {
        enqueueSnackbar(response.data.message || 'Failed to disable 2FA', { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(error.response?.data?.message || 'Failed to disable 2FA', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateBackupCodes = async () => {
    if (!backupCode || backupCode.length !== 6) {
      enqueueSnackbar('Please enter a valid 6-digit code', { variant: 'error' });
      return;
    }

    try {
      setLoading(true);
      const response = await axios.post('/api/2fa/backup-codes', {
        token: backupCode
      });

      if (response.data.success) {
        setNewBackupCodes(response.data.data.backupCodes);
        enqueueSnackbar('New backup codes generated', { variant: 'success' });
        setBackupCode('');
        fetchTwoFactorStatus();
      } else {
        enqueueSnackbar(response.data.message || 'Failed to generate backup codes', { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(error.response?.data?.message || 'Failed to generate backup codes', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const copyBackupCodes = () => {
    navigator.clipboard.writeText(newBackupCodes.join('\n'));
    enqueueSnackbar('Backup codes copied to clipboard', { variant: 'success' });
  };

  const downloadBackupCodes = () => {
    const content = `ASLAA 2FA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\n${newBackupCodes.join('\n')}\n\nKeep these codes safe! Each code can only be used once.`;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'aslaa-backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    enqueueSnackbar('Backup codes downloaded', { variant: 'success' });
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Security color="primary" />
          <Box>
            <Typography variant="h6" component="h2">
              Two-Factor Authentication
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Add an extra layer of security to your account
            </Typography>
          </Box>
        </Box>
        <Box mb={3}>
          <FormControlLabel
            control={
              <Switch 
                checked={twoFactorStatus.twoFactorEnabled}
                onChange={() => {
                  if (twoFactorStatus.twoFactorEnabled) {
                    setShowDisableDialog(true);
                  } else {
                    setShowSetup(true);
                  }
                }}
              />
            }
            label={
              <Box>
                <Typography variant="subtitle1">
                  Two-Factor Authentication
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {twoFactorStatus.twoFactorEnabled 
                    ? 'Your account is protected with 2FA' 
                    : 'Secure your account with an authenticator app'
                  }
                </Typography>
              </Box>
            }
          />
        </Box>

        {twoFactorStatus.twoFactorEnabled && (
          <Box>
            <Alert 
              severity="success" 
              icon={<CheckCircle />}
              sx={{ mb: 2 }}
            >
              <Typography variant="body2">
                2FA is enabled since {new Date(twoFactorStatus.twoFactorEnabledAt).toLocaleDateString()}
              </Typography>
            </Alert>

            <Box mb={2}>
              <Typography variant="subtitle2" gutterBottom>
                Backup Codes
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                You have {twoFactorStatus.unusedBackupCodes} unused backup codes remaining.
                These can be used to access your account if you lose your authenticator device.
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => setShowBackupDialog(true)}
                size="small"
              >
                Generate New Backup Codes
              </Button>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Alert severity="info">
              <Typography variant="body2">
                <strong>Important:</strong> If you lose access to your authenticator app, 
                use your backup codes to regain access to your account.
              </Typography>
            </Alert>
          </Box>
        )}

        {!twoFactorStatus.twoFactorEnabled && (
          <Alert severity="warning" icon={<Warning />}>
            <Typography variant="body2">
              Your account is not protected by two-factor authentication. 
              Enable 2FA to add an extra layer of security.
            </Typography>
          </Alert>
        )}
      </CardContent>

      {/* 2FA Setup Dialog */}
      <TwoFactorSetup
        open={showSetup}
        onClose={() => setShowSetup(false)}
        onComplete={() => {
          fetchTwoFactorStatus();
          setShowSetup(false);
        }}
      />

      {/* Disable 2FA Dialog */}
      <Dialog open={showDisableDialog} onClose={() => setShowDisableDialog(false)}>
        <DialogTitle>Disable Two-Factor Authentication</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Disabling 2FA will make your account less secure. 
              Enter your current authenticator code to confirm.
            </Typography>
          </Alert>
          
          <TextField
            label="Verification Code"
            value={disableCode}
            onChange={(e) => setDisableCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            fullWidth
            placeholder="Enter 6-digit code"
            inputProps={{ maxLength: 6, style: { textAlign: 'center' } }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDisableDialog(false)}>Cancel</Button>
          <LoadingButton
            onClick={handleDisable2FA}
            loading={loading}
            color="error"
            variant="contained"
          >
            Disable 2FA
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* Generate Backup Codes Dialog */}
      <Dialog open={showBackupDialog} onClose={() => setShowBackupDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Generate New Backup Codes</DialogTitle>
        <DialogContent>
          {newBackupCodes.length === 0 ? (
            <Box>
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  This will invalidate all your existing backup codes. 
                  Enter your current authenticator code to confirm.
                </Typography>
              </Alert>
              
              <TextField
                label="Verification Code"
                value={backupCode}
                onChange={(e) => setBackupCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                fullWidth
                placeholder="Enter 6-digit code"
                inputProps={{ maxLength: 6, style: { textAlign: 'center' } }}
              />
            </Box>
          ) : (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  New backup codes generated successfully! Save these codes in a secure location.
                </Typography>
              </Alert>
              
              <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
                <Grid container spacing={1}>
                  {newBackupCodes.map((code, index) => (
                    <Grid item xs={6} key={index}>
                      <Chip 
                        label={code} 
                        variant="outlined" 
                        size="small"
                        sx={{ fontFamily: 'monospace', width: '100%' }}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Paper>

              <Box display="flex" gap={1} justifyContent="center">
                <Button
                  variant="outlined"
                  startIcon={<ContentCopy />}
                  onClick={copyBackupCodes}
                >
                  Copy
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={downloadBackupCodes}
                >
                  Download
                </Button>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setShowBackupDialog(false);
            setNewBackupCodes([]);
            setBackupCode('');
          }}>
            {newBackupCodes.length > 0 ? 'Close' : 'Cancel'}
          </Button>
          
          {newBackupCodes.length === 0 && (
            <LoadingButton
              onClick={handleGenerateBackupCodes}
              loading={loading}
              variant="contained"
            >
              Generate Codes
            </LoadingButton>
          )}
        </DialogActions>
      </Dialog>
    </Card>
  );
};

export default TwoFactorSettings;
