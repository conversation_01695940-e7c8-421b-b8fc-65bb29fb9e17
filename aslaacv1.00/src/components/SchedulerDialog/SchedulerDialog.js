import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Stack,
  Typography,
  Box,
  Alert,
  Chip,
  FormControlLabel,
  Switch,
  alpha,
  IconButton
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

import { useSnackbar } from 'notistack';
import axios from '../../utils/axios';
import Iconify from '../Iconify';

SchedulerDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  deviceNumber: PropTypes.string,
  deviceType: PropTypes.string,
};

const COMMAND_OPTIONS = [
  { value: ':as', label: 'Turn On Engine', icon: 'carbon:power', color: 'success.main' },
  { value: ':untar', label: 'Turn Off Engine', icon: 'carbon:power-off', color: 'error.main' },
];

export default function SchedulerDialog({ open, onClose, deviceNumber, deviceType = 'mqtt' }) {
  const { enqueueSnackbar } = useSnackbar();
  
  const [selectedDateTime, setSelectedDateTime] = useState(dayjs().add(1, 'hour'));
  const [selectedCommand, setSelectedCommand] = useState(':as');
  const [duration, setDuration] = useState(30); // Duration in minutes for SMS devices
  const [loading, setLoading] = useState(false);
  const [scheduledCommands, setScheduledCommands] = useState([]);
  const [repeatDaily, setRepeatDaily] = useState(false); // New state for everyday option
  const [fetchingCommands, setFetchingCommands] = useState(false);

  // Fetch scheduled commands when dialog opens
  useEffect(() => {
    if (open && deviceNumber) {
      fetchScheduledCommands();
    }
  }, [open, deviceNumber]);

  const fetchScheduledCommands = async () => {
    if (!deviceNumber) return;
    
    setFetchingCommands(true);
    try {
      const response = await axios.get('/api/scheduledcommand/');
      
      // Check if response.data is an object with data property, or direct array
      const commandsData = response.data?.data || response.data || [];
      
      // Filter commands for this device
      const deviceCommands = Array.isArray(commandsData) 
        ? commandsData.filter(cmd => cmd.deviceNumber === deviceNumber)
        : [];
      
      setScheduledCommands(deviceCommands.map(cmd => ({
        id: cmd._id,
        command: cmd.payload.includes('as') ? ':as' : ':untar',
        time: dayjs(cmd.scheduledTime).format('MMM DD, YYYY HH:mm'),
        scheduledTime: cmd.scheduledTime,
        status: cmd.status || 'pending',
        executionResult: cmd.executionResult,
        lastExecuted: cmd.lastExecuted ? dayjs(cmd.lastExecuted).format('MMM DD, HH:mm') : null,
        repeat: cmd.isRecurring ? 'Daily' : null
      })));
    } catch (error) {
      console.error('Error fetching scheduled commands:', error);
      enqueueSnackbar('Failed to load scheduled commands', { variant: 'error' });
    } finally {
      setFetchingCommands(false);
    }
  };

  const handleScheduleCommand = async () => {
    if (!deviceNumber) {
      enqueueSnackbar('Device number is required', { variant: 'error' });
      return;
    }

    if (!selectedDateTime || selectedDateTime.isBefore(dayjs())) {
      enqueueSnackbar('Please select a future date and time', { variant: 'error' });
      return;
    }

    setLoading(true);

    try {
      // Format the command without the colon prefix
      const commandValue = selectedCommand.replace(':', '');
      
      // Create request body with the simplified format
      const requestBody = {
        deviceNumber,
        command: commandValue,
        scheduledTime: selectedDateTime.toISOString(),
        isRecurring: repeatDaily
      };

      // Add duration for SMS devices if applicable
      if (deviceType === 'sms') {
        requestBody.duration = duration;
      }

      // Call the new API endpoint
      const response = await axios.post('/api/scheduledcommand/create', requestBody);

      if (response.data) {
        enqueueSnackbar('Command scheduled successfully!', { variant: 'success' });
        
        // Add to scheduled commands list with the returned ID
        const newScheduledCommand = {
          id: response.data._id,
          command: selectedCommand,
          time: selectedDateTime.format('MMM DD, YYYY HH:mm'),
          status: 'scheduled',
          repeat: repeatDaily ? 'Daily' : null
        };
        setScheduledCommands(prev => [...prev, newScheduledCommand]);
        
        // Reset form
        setSelectedDateTime(dayjs().add(1, 'hour'));
        setSelectedCommand(':as');
        setDuration(30);
        setRepeatDaily(false);
      } else {
        enqueueSnackbar('Failed to schedule command', { variant: 'error' });
      }
    } catch (error) {
      console.error('Scheduling error:', error);
      enqueueSnackbar('Error scheduling command', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  const selectedCommandInfo = COMMAND_OPTIONS.find(cmd => cmd.value === selectedCommand);

  const handleDeleteCommand = async (commandId) => {
    if (!commandId) {
      enqueueSnackbar('Invalid command ID', { variant: 'error' });
      return;
    }
    
    try {
      // Call the new delete API endpoint
      await axios.delete(`/api/scheduledcommand/${commandId}`);
      
      // Filter out the command with the matching ID
      setScheduledCommands(prev => prev.filter(cmd => cmd.id !== commandId));
      enqueueSnackbar('Command deleted successfully', { variant: 'success' });
    } catch (error) {
      console.error('Error deleting command:', error);
      enqueueSnackbar('Error deleting command', { variant: 'error' });
    }
  };

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'executed':
        return '#10b981'; // green
      case 'failed':
        return '#ef4444'; // red
      case 'pending':
        return '#f59e0b'; // yellow/amber
      case 'skipped':
        return '#9ca3af'; // gray
      default:
        return '#9ca3af'; // default gray
    }
  };

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'executed':
        return 'eva:checkmark-circle-2-fill';
      case 'failed':
        return 'eva:alert-circle-fill';
      case 'pending':
        return 'eva:clock-fill';
      case 'skipped':
        return 'eva:slash-outline';
      default:
        return 'eva:question-mark-circle-fill';
    }
  };

  // Helper function to format execution results in a human-readable way
  const formatExecutionResult = (result, command) => {
    if (!result) return null;
    
    try {
      // Try to parse the result as JSON
      const parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
      
      // Check if it's an error message
      if (parsedResult.error || parsedResult.errorMessage) {
        return parsedResult.error || parsedResult.errorMessage;
      }
      
      // If it has device data, format it nicely
      if (parsedResult.volt || parsedResult.Lon) {
        const commandType = command.includes('as') ? 'started' : 'stopped';
        
        // Build a readable summary
        let summary = `Engine ${commandType} successfully.`;
        
        // Add battery info if available
        if (parsedResult.volt) {
          summary += ` Battery: ${parseFloat(parsedResult.volt).toFixed(1)}V,`;
        }
        
        // Add temperature if available
        if (parsedResult.temp) {
          summary += ` Temp: ${parsedResult.temp}°C,`;
        }
        
        // Add signal strength if available
        if (parsedResult.rssi) {
          summary += ` Signal: ${parsedResult.rssi} dBm`;
        }
        
        return summary;
      }
      
      // For common error messages, provide friendly versions
      if (result.includes('timeout') || result.includes('timed out')) {
        return 'Command timed out. Device may be offline.';
      }
      
      if (result.includes('offline') || result.includes('disconnected')) {
        return 'Device was offline.';
      }
      
      if (result.includes('rejected') || result.includes('denied')) {
        return 'Command was rejected by the device.';
      }
      
      // If we can't parse it or identify a pattern, return the original
      return result;
    } catch (e) {
      // If parsing fails, check for common error strings
      if (result.includes('timeout') || result.includes('timed out')) {
        return 'Command timed out. Device may be offline.';
      }
      
      if (result.includes('offline') || result.includes('disconnected')) {
        return 'Device was offline.';
      }
      
      if (result.includes('rejected') || result.includes('denied')) {
        return 'Command was rejected by the device.';
      }
      
      // Return the original string if we can't parse it
      return result;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '400px',
          background: 'linear-gradient(180deg, #000000 0%, #111111 100%)', // Premium dark gradient
          border: '1px solid rgba(255, 255, 255, 0.05)', // Subtle border
          boxShadow: '0 10px 20px rgba(0, 0, 0, 0.5)', // Premium shadow
          backdropFilter: 'blur(10px)', // Subtle blur effect
          color: '#f0f0f0', // Light text color
          overflow: 'hidden' // Ensure content doesn't overflow rounded corners
        }
      }}
    >
      <DialogTitle sx={{ 
        pb: 1,
        borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
        background: 'rgba(0, 0, 0, 0.3)'
      }}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="material-symbols:schedule" width={24} height={24} sx={{ color: '#14b8a6' }} />
          <Typography variant="h6" sx={{ color: '#f0f0f0', fontWeight: 500 }}>Schedule Command</Typography>
        </Stack>
        <Typography variant="body2" sx={{ mt: 0.5, color: '#a3a3a3', fontSize: '0.85rem' }}>
          Set a future time to execute device commands
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ 
        p: 3,
        background: 'rgba(0, 0, 0, 0.2)',
      }}>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Device Info */}
          <Alert 
            severity="info" 
            sx={{ 
              borderRadius: 1,
              background: 'rgba(6, 182, 212, 0.1)', // Cyan accent with transparency
              border: '1px solid rgba(6, 182, 212, 0.2)',
              color: '#f0f0f0',
              '& .MuiAlert-icon': { color: '#06b6d4' }
            }}
          >
            <Typography variant="body2">
              <strong>Device:</strong> {deviceNumber} ({deviceType?.toUpperCase()})
            </Typography>
          </Alert>

          {/* Command Selection */}
          <TextField
            select
            label="Select Command"
            value={selectedCommand}
            onChange={(e) => setSelectedCommand(e.target.value)}
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(38, 38, 38, 0.6)', // Dark gray with transparency
                borderRadius: 1,
                '& fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.1)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(20, 184, 166, 0.5)', // Teal accent on hover
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#14b8a6', // Teal accent when focused
                },
              },
              '& .MuiInputLabel-root': {
                color: '#a3a3a3',
              },
              '& .MuiSelect-select': {
                color: '#f0f0f0',
              },
              '& .MuiSvgIcon-root': {
                color: '#a3a3a3',
              },
            }}
          >
            {COMMAND_OPTIONS.map((option) => (
              <MenuItem key={option.value} value={option.value} sx={{ 
                background: '#262626',
                '&:hover': { background: '#333333' },
                '&.Mui-selected': { background: '#333333' }
              }}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify icon={option.icon} width={20} height={20} sx={{ color: option.color }} />
                  <Typography sx={{ color: '#f0f0f0' }}>{option.label}</Typography>
                </Stack>
              </MenuItem>
            ))}
          </TextField>

          {/* Date Time Picker */}
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateTimePicker
              label="Schedule Date & Time"
              value={selectedDateTime}
              onChange={setSelectedDateTime}
              minDateTime={dayjs()}
              renderInput={(params) => (
                <TextField 
                  {...params} 
                  fullWidth 
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'rgba(38, 38, 38, 0.6)',
                      borderRadius: 1,
                      '& fieldset': {
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                      },
                      '&:hover fieldset': {
                        borderColor: 'rgba(20, 184, 166, 0.5)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#14b8a6',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: '#a3a3a3',
                    },
                    '& .MuiInputBase-input': {
                      color: '#f0f0f0',
                    },
                    '& .MuiSvgIcon-root': {
                      color: '#a3a3a3',
                    },
                  }}
                />
              )}
            />
          </LocalizationProvider>

          {/* Repeat Daily Option */}
          <FormControlLabel
            control={
              <Switch
                checked={repeatDaily}
                onChange={(e) => setRepeatDaily(e.target.checked)}
                color="primary"
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: '#14b8a6',
                    '&:hover': {
                      backgroundColor: alpha('#14b8a6', 0.1),
                    },
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: '#14b8a6',
                  },
                }}
              />
            }
            label={
              <Typography sx={{ color: '#f0f0f0', fontSize: '0.9rem' }}>
                Repeat every day
              </Typography>
            }
          />

          {/* Duration for SMS devices */}
          {deviceType === 'sms' && (
            <TextField
              label="Duration (minutes)"
              type="number"
              value={duration}
              onChange={(e) => setDuration(Number(e.target.value))}
              inputProps={{ min: 1, max: 1440 }}
              helperText="How long the command should remain active"
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(38, 38, 38, 0.6)',
                  borderRadius: 1,
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(20, 184, 166, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#14b8a6',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: '#a3a3a3',
                },
                '& .MuiInputBase-input': {
                  color: '#f0f0f0',
                },
                '& .MuiFormHelperText-root': {
                  color: '#737373',
                },
              }}
            />
          )}

          {/* Command Preview */}
          {selectedCommandInfo && (
            <Box sx={{ 
              p: 2, 
              bgcolor: 'rgba(38, 38, 38, 0.4)', 
              borderRadius: 1,
              border: '1px solid rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(5px)'
            }}>
              <Typography variant="subtitle2" gutterBottom sx={{ color: '#d4d4d4' }}>
                Command Preview:
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Chip
                  icon={<Iconify icon={selectedCommandInfo.icon} width={16} height={16} />}
                  label={selectedCommandInfo.label}
                  color="primary"
                  variant="outlined"
                  size="small"
                  sx={{ 
                    borderColor: selectedCommandInfo.color,
                    color: selectedCommandInfo.color === 'success.main' ? '#10b981' : '#ef4444',
                    backgroundColor: 'rgba(0, 0, 0, 0.2)',
                    '& .MuiChip-icon': {
                      color: 'inherit'
                    }
                  }}
                />
                <Typography variant="body2" sx={{ color: '#a3a3a3' }}>
                  at {selectedDateTime.format('MMM DD, YYYY HH:mm')}
                  {repeatDaily && ' (repeats daily)'}
                </Typography>
              </Stack>
            </Box>
          )}

          {/* Scheduled Commands List */}
          {scheduledCommands.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom sx={{ color: '#d4d4d4' }}>
                Scheduled Commands:
              </Typography>
              <Stack spacing={1}>
                {scheduledCommands.map((cmd) => (
                  <Box 
                    key={cmd.id || `cmd-${cmd.time}`} 
                    sx={{ 
                      p: 1.5, 
                      bgcolor: 'rgba(38, 38, 38, 0.4)', 
                      borderRadius: 1,
                      border: `1px solid rgba(${cmd.status === 'executed' ? '16, 185, 129' : 
                                                    cmd.status === 'failed' ? '239, 68, 68' : 
                                                    cmd.status === 'pending' ? '245, 158, 11' : 
                                                    '115, 115, 115'}, 0.2)`
                    }}
                  >
                    <Stack spacing={1}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Stack direction="row" spacing={1} alignItems="center">
                          <Iconify 
                            icon={getStatusIcon(cmd.status)} 
                            width={16} 
                            height={16} 
                            sx={{ color: getStatusColor(cmd.status) }} 
                          />
                          <Typography variant="body2" sx={{ color: '#f0f0f0', fontWeight: 500 }}>
                            {cmd.command.replace(':', '')}
                          </Typography>
                          <Chip 
                            label={cmd.status} 
                            size="small"
                            sx={{ 
                              height: 20, 
                              fontSize: '0.65rem',
                              backgroundColor: `rgba(${cmd.status === 'executed' ? '16, 185, 129' : 
                                                                cmd.status === 'failed' ? '239, 68, 68' : 
                                                                cmd.status === 'pending' ? '245, 158, 11' : 
                                                                '115, 115, 115'}, 0.1)`,
                              color: getStatusColor(cmd.status),
                              border: `1px solid rgba(${cmd.status === 'executed' ? '16, 185, 129' : 
                                                          cmd.status === 'failed' ? '239, 68, 68' : 
                                                          cmd.status === 'pending' ? '245, 158, 11' : 
                                                          '115, 115, 115'}, 0.3)`
                            }}
                          />
                        </Stack>
                        <IconButton 
                          size="small" 
                          onClick={() => handleDeleteCommand(cmd.id)}
                          sx={{ 
                            color: 'rgba(239, 68, 68, 0.8)',
                            '&:hover': { 
                              backgroundColor: 'rgba(239, 68, 68, 0.1)',
                              color: '#ef4444'
                            }
                          }}
                        >
                          <Iconify icon="eva:trash-2-outline" width={16} height={16} />
                        </IconButton>
                      </Stack>
                      
                      <Stack spacing={0.5}>
                        <Typography variant="caption" sx={{ color: '#a3a3a3', fontSize: '0.7rem' }}>
                          Scheduled: {cmd.time}
                        </Typography>
                        
                        {cmd.lastExecuted && (
                          <Typography variant="caption" sx={{ color: '#a3a3a3', fontSize: '0.7rem' }}>
                            Last executed: {cmd.lastExecuted}
                          </Typography>
                        )}
                        
                        {cmd.executionResult && (
                          <Typography 
                            variant="caption" 
                            sx={{ 
                              color: cmd.status === 'failed' ? '#ef4444' : '#a3a3a3',
                              fontSize: '0.7rem',
                              fontStyle: 'italic'
                            }}
                          >
                            Result: {formatExecutionResult(cmd.executionResult, cmd.command)}
                          </Typography>
                        )}
                        
                        {cmd.repeat && (
                          <Chip 
                            label={cmd.repeat} 
                            size="small" 
                            sx={{ 
                              height: 20, 
                              fontSize: '0.65rem',
                              backgroundColor: 'rgba(20, 184, 166, 0.1)',
                              color: '#14b8a6',
                              border: '1px solid rgba(20, 184, 166, 0.3)',
                              mt: 0.5
                            }}
                          />
                        )}
                      </Stack>
                    </Stack>
                  </Box>
                ))}
              </Stack>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ 
        px: 3, 
        pb: 3, 
        pt: 2,
        borderTop: '1px solid rgba(255, 255, 255, 0.05)',
        background: 'rgba(0, 0, 0, 0.3)'
      }}>
        <Button 
          onClick={handleClose} 
          disabled={loading}
          sx={{
            color: '#a3a3a3',
            borderRadius: 1,
            '&:hover': {
              backgroundColor: 'rgba(115, 115, 115, 0.1)',
            },
          }}
        >
          Cancel
        </Button>
        <LoadingButton
          onClick={handleScheduleCommand}
          loading={loading}
          variant="contained"
          startIcon={<Iconify icon="material-symbols:schedule" />}
          sx={{
            backgroundColor: '#14b8a6',
            color: '#f0f0f0',
            borderRadius: 1,
            '&:hover': {
              backgroundColor: '#0d9488',
            },
            '&.Mui-disabled': {
              backgroundColor: 'rgba(20, 184, 166, 0.3)',
            },
          }}
        >
          Schedule Command
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
