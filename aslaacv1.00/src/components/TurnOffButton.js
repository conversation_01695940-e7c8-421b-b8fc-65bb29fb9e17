import PropTypes from 'prop-types';
import Button from './Button';

TurnOffButton.propTypes = {
  sx: PropTypes.object,
  handleTurnOff: PropTypes.func.isRequired,
};

export default function TurnOffButton({ sx, handleTurnOff }) {
  const svgContent = (
    <>
      <g transform="translate(275,125) rotate(0 180 0) scale(3,-3)">
        <path fill="white" stroke="none" d="M10 11c-.57 0-1 .45-1 1s.43 1 1 1c.54 0 1-.45 1-1s-.46-1-1-1m.5-9c4.5 0 4.59 3.57 2.23 4.75c-.99.49-1.43 1.54-1.62 2.47c.48.2.89.51 1.22.91c3.7-2 7.67-1.21 7.67 2.37c0 4.5-3.57 4.6-4.74 2.23c-.5-.99-1.56-1.43-2.49-1.62c-.2.48-.51.89-.91 1.23C13.85 18.03 13.06 22 9.5 22c-4.5 0-4.6-3.58-2.24-4.76c.98-.49 1.42-1.53 1.62-2.45c-.49-.2-.92-.52-1.24-.92C3.95 15.85 0 15.07 0 11.5C0 7 3.56 6.89 4.73 9.26c.5.99 1.55 1.42 2.48 1.61c.19-.48.51-.9.92-1.22C6.14 5.96 6.93 2 10.5 2M22 13V7h2v6h-2m0 4v-2h2v2h-2Z"/>
      </g>
      <path d="M400 200 h-100 A100 100 0 0 0 200 100 v -100 A 200 200 0 0 1 400 200" />
    </>
  );

  return <Button sx={sx} onClick={handleTurnOff} svgContent={svgContent} />;
}
