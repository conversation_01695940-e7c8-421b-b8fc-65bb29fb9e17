server {
    server_name aslaa.mn www.aslaa.mn;
    root /var/www/aslaaradev/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
	
    index index.php;

    charset utf-8;


    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
	fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
	include fastcgi_params;
	fastcgi_read_timeout 300;
     }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
