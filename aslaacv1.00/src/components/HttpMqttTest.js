import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Paper, List, ListItem, Divider, CircularProgress } from '@mui/material';

// This component uses the EMQX HTTP API to test MQTT functionality
// It's a fallback in case WebSocket connections don't work
const HttpMqttTest = () => {
  const [topic, setTopic] = useState('aslaa/test');
  const [message, setMessage] = useState('Hello from HTTP MQTT Test');
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // EMQX public broker HTTP API endpoint
  const API_URL = 'https://broker.emqx.io:8081/api/v4';
  
  // Add log entry
  const addLog = (type, content) => {
    setLogs(prev => [
      ...prev,
      {
        id: Date.now(),
        type,
        content,
        time: new Date().toLocaleTimeString()
      }
    ]);
  };

  // Publish message using HTTP API
  const handlePublish = async () => {
    if (!topic || !message) return;
    
    setIsLoading(true);
    
    try {
      addLog('System', `Publishing to ${topic}: ${message}`);
      
      const response = await fetch(`${API_URL}/mqtt/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          payload: message,
          qos: 1,
          retain: false,
        }),
      });
      
      const data = await response.json();
      
      if (data.code === 0) {
        addLog('Success', `Message published successfully to ${topic}`);
      } else {
        addLog('Error', `Failed to publish: ${data.message}`);
      }
    } catch (error) {
      addLog('Error', `Error publishing message: ${error.message}`);
      console.error('Error publishing message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check broker status using HTTP API
  const checkBrokerStatus = async () => {
    setIsLoading(true);
    
    try {
      addLog('System', 'Checking broker status...');
      
      const response = await fetch(`${API_URL}/brokers`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (data.code === 0) {
        addLog('Success', `Broker is online. Node: ${data.data[0]?.node}`);
      } else {
        addLog('Error', `Failed to check broker: ${data.message}`);
      }
    } catch (error) {
      addLog('Error', `Error checking broker status: ${error.message}`);
      console.error('Error checking broker status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear logs
  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          HTTP MQTT Test
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          This test uses HTTP API instead of WebSocket connection
        </Typography>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>Publish Message</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Topic"
              variant="outlined"
              size="small"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
            />
            <TextField
              label="Message"
              variant="outlined"
              size="small"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
            />
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button 
                variant="contained" 
                onClick={handlePublish}
                disabled={isLoading || !topic || !message}
              >
                {isLoading ? <CircularProgress size={24} /> : 'Publish'}
              </Button>
              <Button 
                variant="outlined" 
                onClick={checkBrokerStatus}
                disabled={isLoading}
              >
                Check Broker Status
              </Button>
              <Button 
                variant="text" 
                onClick={clearLogs}
                disabled={isLoading || logs.length === 0}
              >
                Clear Logs
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Logs
        </Typography>
        <List>
          {logs.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No logs yet
            </Typography>
          ) : (
            logs.map((log, index) => (
              <React.Fragment key={log.id}>
                {index > 0 && <Divider />}
                <ListItem>
                  <Box sx={{ width: '100%' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography 
                        variant="subtitle2" 
                        color={
                          log.type === 'Success' ? 'success.main' : 
                          log.type === 'Error' ? 'error.main' : 
                          'primary.main'
                        }
                      >
                        {log.type}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {log.time}
                      </Typography>
                    </Box>
                    <Typography variant="body2">
                      {log.content}
                    </Typography>
                  </Box>
                </ListItem>
              </React.Fragment>
            ))
          )}
        </List>
      </Paper>
    </Box>
  );
};

export default HttpMqttTest;
