import styled from "@emotion/styled";
import { Card, Typography, Box } from "@mui/material";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import beep from "../assets/beep.mp3";
// -------------------------------------------
const RootStyle = styled('div')(() => ({
    right: 0,
    bottom: 0,
    zIndex: 99999,
    width: '100%',
    height: '100%',
    position: 'fixed',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor:'#061c2ad1'
  }));

export default function FindingScreen({ message, description, buttonPosition, showBirdAnimation = true}) {
    const [showBird, setShowBird] = useState(false);
    const [showParticles, setShowParticles] = useState(false);
    const [showCarPulse, setShowCarPulse] = useState(false);


    // Small envelope for message sending
    const selectedBird = '✉️';  // Small envelope icon for sending commands/messages

    useEffect(() => {
        if (showBirdAnimation) {
            // Start bird animation after a short delay
            const timer = setTimeout(() => {
                setShowBird(true);

                // Play a subtle sound effect
                try {
                    const audio = new Audio(beep);
                    audio.volume = 0.3; // Keep it subtle
                    audio.play().catch(() => {
                        // Audio play failed - user interaction required
                    });
                } catch (error) {
                    // Ignore audio errors
                }

                // Show car pulse when bird is about to land
                setTimeout(() => setShowCarPulse(true), 2200);



                // Show particles when bird reaches destination
                setTimeout(() => setShowParticles(true), 2500);
            }, 300);
            return () => clearTimeout(timer);
        }
    }, [showBirdAnimation]);

    // Calculate bird flight path with dramatic movement
    const startPosition = buttonPosition || {
        x: window.innerWidth * 0.75,
        y: window.innerHeight * 0.85  // Start a bit higher from bottom
    };

    // Simple destination position like Windows file copying - adjusted to car top center
    const endPosition = {
        x: window.innerWidth * 0.5,    // Center of screen horizontally
        y: window.innerHeight * 0.25   // Higher up to reach car top center
    };

    return (
    <RootStyle>
        {/* Simple Delivery Confirmation */}
        <AnimatePresence>
            {showCarPulse && showBirdAnimation && (
                <motion.div
                    initial={{
                        left: endPosition.x,
                        top: endPosition.y,
                        scale: 0,
                        opacity: 1
                    }}
                    animate={{
                        scale: [0, 1.5, 2],
                        opacity: [1, 0.8, 0]
                    }}
                    exit={{ opacity: 0 }}
                    transition={{
                        duration: 1.5,
                        ease: "easeOut"
                    }}
                    style={{
                        position: 'fixed',
                        width: '60px',
                        height: '60px',
                        borderRadius: '50%',
                        border: '2px solid #00ff64',
                        backgroundColor: 'rgba(0,255,100,0.1)',
                        zIndex: 99998,
                        pointerEvents: 'none',
                        transform: 'translate(-50%, -50%)'
                    }}
                />
            )}
        </AnimatePresence>


        {/* Simple File Transfer Animation */}
        <AnimatePresence>
            {showBird && showBirdAnimation && (
                <motion.div
                    initial={{
                        left: startPosition.x,
                        top: startPosition.y,
                        scale: 1,
                        opacity: 1
                    }}
                    animate={{
                        left: endPosition.x,
                        top: endPosition.y,
                        scale: [1, 1.2, 1],  // Subtle scale change
                        opacity: [1, 1, 0.8]
                    }}
                    exit={{
                        scale: 0.5,
                        opacity: 0
                    }}
                    transition={{
                        duration: 2,
                        ease: "easeInOut"
                    }}
                    style={{
                        position: 'fixed',
                        fontSize: '32px',  // Smaller, cleaner size
                        zIndex: 999999,
                        pointerEvents: 'none',
                        transform: 'translate(-50%, -50%)',
                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
                    }}
                >
                    {selectedBird}
                </motion.div>
            )}
        </AnimatePresence>

        {/* Magical Particle Effects */}
        <AnimatePresence>
            {showParticles && showBirdAnimation && (
                <>
                    {/* Sparkle particles */}
                    {[...Array(8)].map((_, i) => {
                        const particles = ['✨', '⭐', '💫', '🌟'];
                        const particle = particles[i % particles.length];
                        const angle = (i / 8) * Math.PI * 2;
                        const radius = 60 + Math.random() * 40;

                        return (
                            <motion.div
                                key={`sparkle-${i}`}
                                initial={{
                                    left: endPosition.x,
                                    top: endPosition.y,
                                    scale: 0,
                                    opacity: 1,
                                    rotate: 0
                                }}
                                animate={{
                                    left: endPosition.x + Math.cos(angle) * radius,
                                    top: endPosition.y + Math.sin(angle) * radius,
                                    scale: [0, 1.2, 0.8, 0],
                                    opacity: [1, 1, 0.6, 0],
                                    rotate: [0, 180, 360]
                                }}
                                exit={{ opacity: 0 }}
                                transition={{
                                    duration: 2,
                                    delay: i * 0.1,
                                    ease: "easeOut"
                                }}
                                style={{
                                    position: 'fixed',
                                    fontSize: '14px',
                                    zIndex: 99999,
                                    pointerEvents: 'none'
                                }}
                            >
                                {particle}
                            </motion.div>
                        );
                    })}

                    {/* Heart particles for extra charm */}
                    {[...Array(4)].map((_, i) => (
                        <motion.div
                            key={`heart-${i}`}
                            initial={{
                                left: endPosition.x,
                                top: endPosition.y,
                                scale: 0,
                                opacity: 1
                            }}
                            animate={{
                                left: endPosition.x + (Math.random() - 0.5) * 120,
                                top: endPosition.y - 50 - Math.random() * 80,
                                scale: [0, 1, 0.5, 0],
                                opacity: [1, 0.8, 0.4, 0],
                                rotate: [0, 15, -15, 0]
                            }}
                            exit={{ opacity: 0 }}
                            transition={{
                                duration: 2.5,
                                delay: 0.5 + i * 0.2,
                                ease: "easeOut"
                            }}
                            style={{
                                position: 'fixed',
                                fontSize: '12px',
                                zIndex: 99999,
                                pointerEvents: 'none'
                            }}
                        >
                            💖
                        </motion.div>
                    ))}
                </>
            )}
        </AnimatePresence>

        {/* Main Loading Card */}
        <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: showBirdAnimation ? 1 : 0 }}
        >
            <Card sx={{
                width: 300,
                minHeight: 100,
                border: '1px solid',
                borderColor: 'grey.50048',
                p: 4,
                textAlign: 'center',
                backgroundColor: 'transparent',
                backdropFilter: 'blur(10px)',
                boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
            }}>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                    {message}
                </Typography>
                <Typography variant="subtitle2" sx={{ opacity: 0.8 }}>
                    {description}
                </Typography>

                {/* Pulsing Loading Indicator */}
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                    <motion.div
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                        style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: '#00ff64',
                            margin: '0 4px'
                        }}
                    />
                    <motion.div
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.2
                        }}
                        style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: '#00ff64',
                            margin: '0 4px'
                        }}
                    />
                    <motion.div
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.4
                        }}
                        style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: '#00ff64',
                            margin: '0 4px'
                        }}
                    />
                </Box>
            </Card>
        </motion.div>
    </RootStyle>
    )
}