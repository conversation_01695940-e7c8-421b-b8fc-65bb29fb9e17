import mqtt from 'mqtt';

/**
 * MQTT TCP Service
 *
 * This service provides a singleton instance for MQTT connections over TCP (port 1883)
 * Note: This will only work in Node.js environments, not in browsers
 */

// MQTT broker configuration
const MQTT_CONFIG = {
  // Client settings
  clientId: `aslaacv_${Math.random().toString(16).substring(2, 10)}`, // Random client ID
  clean: true,
  connectTimeout: 4000,
  reconnectPeriod: 1000,
  // TCP specific options
  port: 1883,
  protocol: 'mqtt',
};

// Primary MQTT broker URL (TCP)
const PRIMARY_BROKER = 'mqtt://39.104.209.84';

// Backup MQTT broker URLs (TCP)
const BACKUP_BROKERS = [
  'mqtt://45.77.243.70',
  'mqtt://149.28.139.146',
  'mqtt://broker.emqx.io' // Public EMQX broker as last resort
];

// Current broker URL
let currentBrokerIndex = 0;
let currentBrokerUrl = PRIMARY_BROKER;

// MQTT client instance
let client = null;

// Event callbacks
const callbacks = {
  connect: [],
  message: [],
  error: [],
  reconnect: [],
  offline: [],
  disconnect: [],
};

/**
 * Initialize MQTT connection
 * @returns {Object} MQTT client instance
 */
/**
 * Try to connect to the next broker in the list
 */
const tryNextBroker = () => {
  currentBrokerIndex = (currentBrokerIndex + 1) % (BACKUP_BROKERS.length + 1);
  currentBrokerUrl = currentBrokerIndex === 0 ? PRIMARY_BROKER : BACKUP_BROKERS[currentBrokerIndex - 1];
  console.log(`Switching to broker: ${currentBrokerUrl}`);
  return currentBrokerUrl;
};

/**
 * Initialize MQTT connection with fallback support
 * @returns {Object} MQTT client instance
 */
export const initMqtt = () => {
  if (client && client.connected) {
    return client;
  }

  // If client exists but is not connected, end it
  if (client) {
    client.end(true);
    client = null;
  }

  console.log('Connecting to MQTT broker:', currentBrokerUrl);
  console.log('With config:', JSON.stringify(MQTT_CONFIG));

  try {
    // Create MQTT client
    client = mqtt.connect(currentBrokerUrl, MQTT_CONFIG);
    console.log('MQTT client created');

    // Set up reconnect logic with broker fallback
    let reconnectCount = 0;
    client.on('reconnect', () => {
      reconnectCount++;
      console.log(`Reconnect attempt ${reconnectCount}`);

      // After 3 failed reconnect attempts, try the next broker
      if (reconnectCount >= 3) {
        reconnectCount = 0;
        const newBrokerUrl = tryNextBroker();
        // For TCP connections, we need to create a new client with the new URL
        client.end(true);
        client = mqtt.connect(newBrokerUrl, MQTT_CONFIG);
      }
    });

    // Set up event handlers
    client.on('connect', () => {
      console.log('Connected to MQTT broker');
      callbacks.connect.forEach(callback => callback());
    });

    client.on('message', (topic, message) => {
      console.log(`Received message on topic ${topic}: ${message.toString()}`);
      callbacks.message.forEach(callback => callback(topic, message));
    });

    client.on('error', (error) => {
      console.error('MQTT connection error:', error);
      console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
      callbacks.error.forEach(callback => callback(error));
    });

    client.on('reconnect', () => {
      console.log('Reconnecting to MQTT broker');
      callbacks.reconnect.forEach(callback => callback());
    });

    client.on('offline', () => {
      console.log('MQTT client is offline');
      callbacks.offline.forEach(callback => callback());
    });

    client.on('disconnect', () => {
      console.log('Disconnected from MQTT broker');
      callbacks.disconnect.forEach(callback => callback());
    });

    return client;
  } catch (error) {
    console.error('Error creating MQTT client:', error);
    console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    return null;
  }
};

/**
 * Subscribe to a topic
 * @param {string} topic - Topic to subscribe to
 * @param {Object} options - Subscription options
 * @returns {boolean} Success status
 */
export const subscribe = (topic, options = { qos: 0 }) => {
  if (!client || !client.connected) {
    console.error('MQTT client not connected');
    return false;
  }

  client.subscribe(topic, options, (error) => {
    if (error) {
      console.error(`Error subscribing to ${topic}:`, error);
      return false;
    }
    console.log(`Subscribed to ${topic}`);
  });

  return true;
};

/**
 * Publish a message to a topic
 * @param {string} topic - Topic to publish to
 * @param {string} message - Message to publish
 * @param {Object} options - Publish options
 * @returns {boolean} Success status
 */
export const publish = (topic, message, options = { qos: 0, retain: false }) => {
  if (!client || !client.connected) {
    console.error('MQTT client not connected');
    return false;
  }

  client.publish(topic, message, options, (error) => {
    if (error) {
      console.error(`Error publishing to ${topic}:`, error);
      return false;
    }
    console.log(`Published to ${topic}: ${message}`);
  });

  return true;
};

/**
 * Unsubscribe from a topic
 * @param {string} topic - Topic to unsubscribe from
 * @returns {boolean} Success status
 */
export const unsubscribe = (topic) => {
  if (!client || !client.connected) {
    console.error('MQTT client not connected');
    return false;
  }

  client.unsubscribe(topic, (error) => {
    if (error) {
      console.error(`Error unsubscribing from ${topic}:`, error);
      return false;
    }
    console.log(`Unsubscribed from ${topic}`);
  });

  return true;
};

/**
 * Add event listener
 * @param {string} event - Event name (connect, message, error, etc.)
 * @param {Function} callback - Callback function
 */
export const on = (event, callback) => {
  if (callbacks[event]) {
    callbacks[event].push(callback);
  }
};

/**
 * Remove event listener
 * @param {string} event - Event name
 * @param {Function} callback - Callback function to remove
 */
export const off = (event, callback) => {
  if (callbacks[event]) {
    callbacks[event] = callbacks[event].filter(cb => cb !== callback);
  }
};

/**
 * Disconnect MQTT client
 */
export const disconnect = () => {
  if (client && client.connected) {
    client.end();
    client = null;
    console.log('MQTT client disconnected');
  }
};

/**
 * Get connection status
 * @returns {boolean} Connection status
 */
export const isConnected = () => {
  return client && client.connected;
};

const mqttTcpService = {
  initMqtt,
  subscribe,
  publish,
  unsubscribe,
  on,
  off,
  disconnect,
  isConnected,
};

export default mqttTcpService;
