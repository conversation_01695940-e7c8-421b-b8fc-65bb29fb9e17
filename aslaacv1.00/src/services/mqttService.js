import mqtt from 'mqtt';

/**
 * MQTT Service for Device Configuration
 *
 * This service provides MQTT functionality specifically tailored for device configuration
 * It supports both WebSocket (for browsers) and TCP (for Node.js/Electron) connections
 */

// Client instance
let client = null;
let isConnected = false;

// Event listeners
const eventListeners = {
  connect: [],
  disconnect: [],
  message: [],
  error: [],
  reconnect: [],
};

/**
 * Connect to MQTT broker
 * @param {string} brokerUrl - The broker URL (e.g., mqtt://broker.example.com or ws://broker.example.com:8083/mqtt)
 * @param {Object} options - Connection options
 * @returns {boolean} - Whether the connection attempt was initiated
 */
const connect = (brokerUrl, options = {}) => {
  try {
    // Disconnect existing client if any
    if (client) {
      try {
        client.end(true);
      } catch (e) {
        console.error('MQTT: Error ending existing client', e);
      }
      client = null;
      isConnected = false;
    }

    // Set default options - using exact same options as working test
    const defaultOptions = {
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      connectTimeout: 30000,
      clientId: `aslaacv_${Math.random().toString(16).substring(2, 10)}`,
    };

    // Merge with user options
    const mqttOptions = { ...defaultOptions, ...options };

    // console.log(`MQTT: Connecting to ${brokerUrl} with options:`, mqttOptions);

    // Ensure the URL is properly formatted
    let finalUrl = brokerUrl;

    // Make sure we're using WebSocket URL in browser
    if (typeof window !== 'undefined') {
      // If URL doesn't already have ws:// or wss://, add it
      if (!finalUrl.startsWith('ws://') && !finalUrl.startsWith('wss://')) {
        // If it's just an IP or hostname, add ws:// and default port
        if (!finalUrl.includes('://')) {
          finalUrl = `ws://${finalUrl}:8083/mqtt`;
        }
        // If it's mqtt:// protocol, convert to ws://
        else if (finalUrl.startsWith('mqtt://')) {
          finalUrl = finalUrl.replace('mqtt://', 'ws://');
          // Add port and path if not present
          if (!finalUrl.includes(':')) {
            finalUrl = `${finalUrl}:8083/mqtt`;
          } else if (!finalUrl.includes('/')) {
            finalUrl = `${finalUrl}/mqtt`;
          }
        }
      }
    }

    // console.log(`MQTT: Final connection URL: ${finalUrl}`);

    // Create client - use the simplest possible approach
    try {
      // Store the URL and options globally for debugging
      window.lastMqttUrl = finalUrl;
      window.lastMqttOptions = mqttOptions;

      // Store a direct reference to the client in window for debugging
      window.mqttClient = mqtt.connect(finalUrl, mqttOptions);
      client = window.mqttClient;
      // console.log('MQTT: Client created successfully');

      // Add a connection timeout
      const connectionTimeout = setTimeout(() => {
        if (!isConnected) {
          // console.error('MQTT: Connection timeout after', mqttOptions.connectTimeout, 'ms');

          // Try to get more information about the client state
          // console.log('MQTT: Client state at timeout:',
          //   client ? {
          //     connected: client.connected,
          //     reconnecting: client.reconnecting,
          //     disconnecting: client.disconnecting,
          //     disconnected: client.disconnected
          //   } : 'No client');

          // Dispatch timeout event
          const timeoutEvent = new CustomEvent('mqtt-connection-failed', {
            detail: {
              message: 'Connection timeout - no connect event received',
              url: finalUrl,
              options: mqttOptions
            }
          });
          window.dispatchEvent(timeoutEvent);
        }
      }, mqttOptions.connectTimeout || 30000);

      // Clear timeout on connect
      client.on('connect', () => {
        clearTimeout(connectionTimeout);
      });

    } catch (connectError) {
      // console.error('MQTT: Error creating client', connectError);

      // Dispatch custom event
      const event = new CustomEvent('mqtt-connection-failed', {
        detail: { error: connectError, message: 'Failed to create MQTT client', url: finalUrl }
      });
      window.dispatchEvent(event);

      return false;
    }

    // Set up event handlers
    client.on('connect', () => {
      // console.log('MQTT: Connected to broker');
      isConnected = true;

      // Dispatch custom event for components to listen to
      const event = new CustomEvent('mqtt-connected', {
        detail: { url: brokerUrl }
      });
      window.dispatchEvent(event);

      // Call registered callbacks
      eventListeners.connect.forEach(callback => callback());
    });

    client.on('disconnect', () => {
      // console.log('MQTT: Disconnected from broker');
      isConnected = false;

      // Dispatch custom event
      const event = new CustomEvent('mqtt-disconnected');
      window.dispatchEvent(event);

      // Call registered callbacks
      eventListeners.disconnect.forEach(callback => callback());
    });

    client.on('offline', () => {
      // console.log('MQTT: Client is offline');
      isConnected = false;

      // Dispatch custom event
      const event = new CustomEvent('mqtt-disconnected');
      window.dispatchEvent(event);
    });

    client.on('error', (error) => {
      // console.error('MQTT: Connection error', error);

      // Get a more descriptive error message
      let errorMessage = 'Unknown error';
      if (error) {
        if (typeof error === 'string') {
          errorMessage = error;
        } else if (error.message) {
          errorMessage = error.message;
        } else if (error.toString) {
          errorMessage = error.toString();
        }
      }

      // Dispatch custom event with more details
      const event = new CustomEvent('mqtt-connection-failed', {
        detail: {
          error,
          message: errorMessage,
          url: brokerUrl
        }
      });
      window.dispatchEvent(event);

      // Call registered callbacks
      eventListeners.error.forEach(callback => callback(error));
    });

    client.on('message', (topic, message) => {
      try {
        const rawMessage = message.toString();
        // console.log(`MQTT: Received message on ${topic}:`, rawMessage);

        // Try to parse as JSON
        let payload;
        try {
          payload = JSON.parse(rawMessage);
          // console.log('MQTT: Successfully parsed message as JSON:', payload);
        } catch (e) {
          // console.log('MQTT: Message is not valid JSON, using as string');
          payload = rawMessage;
        }

        // Dispatch custom event with detailed information
        const event = new CustomEvent('mqtt-message', {
          detail: {
            topic,
            payload,
            raw: rawMessage,
            timestamp: new Date().toISOString()
          }
        });

        // console.log('MQTT: Dispatching mqtt-message event with payload:', payload);
        window.dispatchEvent(event);

        // Call registered callbacks
        eventListeners.message.forEach(callback => {
          try {
            callback(topic, message);
          } catch (callbackError) {
            // console.error('MQTT: Error in message callback', callbackError);
          }
        });
      } catch (error) {
        // console.error('MQTT: Error processing message', error);

        // Try to dispatch a basic event even if processing failed
        try {
          const event = new CustomEvent('mqtt-message', {
            detail: {
              topic,
              payload: message.toString(),
              error: error.message,
              timestamp: new Date().toISOString()
            }
          });
          window.dispatchEvent(event);
        } catch (e) {
          // console.error('MQTT: Failed to dispatch error event', e);
        }
      }
    });

    client.on('reconnect', () => {
      // console.log('MQTT: Attempting to reconnect');

      // Dispatch custom event
      const event = new CustomEvent('mqtt-reconnecting');
      window.dispatchEvent(event);

      // Call registered callbacks
      eventListeners.reconnect.forEach(callback => callback());
    });

    // Add a connection timeout
    const connectionTimeout = setTimeout(() => {
      if (!isConnected && client) {
        // console.error('MQTT: Connection timeout');

        // Dispatch custom event
        const event = new CustomEvent('mqtt-connection-failed', {
          detail: {
            message: 'Connection timeout',
            url: brokerUrl
          }
        });
        window.dispatchEvent(event);

        // Try to clean up
        try {
          client.end(true);
        } catch (e) {
          // console.error('MQTT: Error ending client after timeout', e);
        }
        client = null;
        isConnected = false;
      }
    }, mqttOptions.connectTimeout || 10000);

    // Clear the timeout if connected
    client.on('connect', () => {
      clearTimeout(connectionTimeout);
    });

    return true;
  } catch (error) {
    // console.error('MQTT: Error in connect function', error);

    // Dispatch custom event
    const event = new CustomEvent('mqtt-connection-failed', {
      detail: {
        error,
        message: error.message || 'Unknown error in connect function',
        url: brokerUrl
      }
    });
    window.dispatchEvent(event);

    return false;
  }
};

/**
 * Subscribe to a topic
 * @param {string} topic - The topic to subscribe to
 * @param {Object} options - Subscription options (e.g., { qos: 1 })
 * @returns {boolean} - Whether the subscription was successful
 */
const subscribe = (topic, options = { qos: 0 }) => {
  if (!client || !isConnected) {
    // console.error('MQTT: Cannot subscribe, client not connected');
    return false;
  }

  try {
    client.subscribe(topic, options, (error) => {
      if (error) {
        // console.error(`MQTT: Error subscribing to ${topic}:`, error);
      } else {
        // console.log(`MQTT: Subscribed to ${topic}`);
      }
    });
    return true;
  } catch {
    // console.error(`MQTT: Error subscribing to ${topic}:`, error);
    return false;
  }
};

/**
 * Publish a message to a topic
 * @param {string} topic - The topic to publish to
 * @param {string|Object} message - The message to publish (objects will be stringified)
 * @param {Object} options - Publish options (e.g., { qos: 1, retain: true })
 * @returns {boolean} - Whether the publish was successful
 */
const publish = (topic, message, options = { qos: 0 }) => {
  if (!client || !isConnected) {
    // console.error('MQTT: Cannot publish, client not connected');
    return false;
  }

  try {
    // Convert object to string if needed
    const messageStr = typeof message === 'object' ? JSON.stringify(message) : message;

    client.publish(topic, messageStr, options, (error) => {
      if (error) {
        // console.error(`MQTT: Error publishing to ${topic}:`, error);
      } else {
        // console.log(`MQTT: Published to ${topic}:`, messageStr);
      }
    });
    return true;
  } catch {
    // console.error(`MQTT: Error publishing to ${topic}:`, error);
    return false;
  }
};

/**
 * Unsubscribe from a topic
 * @param {string} topic - The topic to unsubscribe from
 * @returns {boolean} - Whether the unsubscribe was successful
 */
const unsubscribe = (topic) => {
  if (!client || !isConnected) {
    // console.error('MQTT: Cannot unsubscribe, client not connected');
    return false;
  }

  try {
    client.unsubscribe(topic, (error) => {
      if (error) {
        // console.error(`MQTT: Error unsubscribing from ${topic}:`, error);
      } else {
        // console.log(`MQTT: Unsubscribed from ${topic}`);
      }
    });
    return true;
  } catch {
    // console.error(`MQTT: Error unsubscribing from ${topic}:`, error);
    return false;
  }
};

/**
 * Disconnect from the broker
 */
const disconnect = () => {
  if (client) {
    client.end(true);
    client = null;
    isConnected = false;
    // console.log('MQTT: Disconnected from broker');
  }
};

/**
 * Add event listener
 * @param {string} event - Event name ('connect', 'disconnect', 'message', 'error', 'reconnect')
 * @param {Function} callback - Callback function
 */
const on = (event, callback) => {
  if (eventListeners[event]) {
    eventListeners[event].push(callback);
  }
};

/**
 * Remove event listener
 * @param {string} event - Event name
 * @param {Function} callback - Callback function to remove
 */
const off = (event, callback) => {
  if (eventListeners[event]) {
    eventListeners[event] = eventListeners[event].filter(cb => cb !== callback);
  }
};

/**
 * Direct connect method that bypasses the service layer
 * This uses the exact same approach as the working test page
 */
const directConnect = (broker, options = {}) => {
  try {
    const url = `ws://${broker}:8083/mqtt`;
    const clientId = options.clientId || `direct_${Math.random().toString(16).substring(2, 10)}`;

    const connectOptions = {
      clientId: clientId,
      username: options.username || "admin",
      password: options.password || "public",
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      connectTimeout: 30000
    };

    // console.log(`MQTT Direct: Connecting to ${url}`);
    const directClient = mqtt.connect(url, connectOptions);

    // Store in global for debugging
    window.directMqttClient = directClient;

    return directClient;
  } catch {
    // console.error('MQTT Direct: Connection error', error);
    return null;
  }
};

// Export the service
const mqttService = {
  connect,
  subscribe,
  publish,
  unsubscribe,
  disconnect,
  directConnect,
  on,
  off,
  get isConnected() { return isConnected; }
};

export default mqttService;
