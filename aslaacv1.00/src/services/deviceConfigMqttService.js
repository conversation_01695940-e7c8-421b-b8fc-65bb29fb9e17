import mqtt from 'mqtt';

/**
 * MQTT Service for Device Configuration
 *
 * This service provides MQTT functionality specifically for device configuration
 * using WebSocket connections.
 */

// Client instance
let client = null;
let isConnected = false;

// Event listeners
const eventListeners = {
  connect: [],
  disconnect: [],
  message: [],
  error: [],
  reconnect: [],
  configReceived: [],
};

/**
 * Connect to MQTT broker
 * @param {string} ipAddress - The IP address of the MQTT broker
 * @param {Object} options - Connection options
 * @returns {boolean} - Whether the connection attempt was initiated
 */
const connect = (ipAddress, options = {}) => {
  try {
    // Disconnect existing client if any
    if (client) {
      try {
        client.end(true);
      } catch (e) {
        console.error('DeviceConfig MQTT: Error ending existing client', e);
      }
      client = null;
      isConnected = false;
    }

    // Format the broker URL for WebSocket connection
    const brokerUrl = `ws://${ipAddress}:8083/mqtt`;
    console.log(`DeviceConfig MQTT: Connecting to ${brokerUrl}`);

    // Set default options
    const mqttOptions = {
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      connectTimeout: 30000,
      clientId: `device_config_${Math.random().toString(16).substring(2, 10)}`,
      ...options
    };

    // Create client
    client = mqtt.connect(brokerUrl, mqttOptions);

    // Set up event handlers
    client.on('connect', () => {
      console.log('DeviceConfig MQTT: Connected successfully');
      isConnected = true;
      eventListeners.connect.forEach(callback => callback());
    });

    client.on('message', (topic, message) => {
      const messageStr = message.toString();
      console.log(`DeviceConfig MQTT: Received message on ${topic}: ${messageStr}`);

      // Notify all message listeners
      eventListeners.message.forEach(callback => callback(topic, messageStr));

      // Handle configuration messages specifically
      if (topic.match(/device\/(\w+)\/config/)) {
        try {
          const data = JSON.parse(messageStr);
          const deviceId = topic.split('/')[1];
          eventListeners.configReceived.forEach(callback => callback(deviceId, data));
        } catch (err) {
          console.error('DeviceConfig MQTT: Error parsing config message', err);
        }
      }
    });

    client.on('error', (error) => {
      console.error('DeviceConfig MQTT: Connection error', error);
      eventListeners.error.forEach(callback => callback(error));
    });

    client.on('close', () => {
      console.log('DeviceConfig MQTT: Connection closed');
      isConnected = false;
    });

    client.on('reconnect', () => {
      console.log('DeviceConfig MQTT: Reconnecting');
      eventListeners.reconnect.forEach(callback => callback());
    });

    return true;
  } catch (error) {
    console.error('DeviceConfig MQTT: Connection exception', error);
    return false;
  }
};

/**
 * Subscribe to a topic
 * @param {string} topic - The topic to subscribe to
 * @param {Object} options - Subscription options (e.g., { qos: 1 })
 * @returns {boolean} - Whether the subscription was successful
 */
const subscribe = (topic, options = { qos: 0 }) => {
  if (!client || !isConnected) {
    console.error('DeviceConfig MQTT: Cannot subscribe, client not connected');
    return false;
  }

  try {
    client.subscribe(topic, options, (error) => {
      if (error) {
        console.error(`DeviceConfig MQTT: Error subscribing to ${topic}:`, error);
      } else {
        console.log(`DeviceConfig MQTT: Subscribed to ${topic}`);
      }
    });
    return true;
  } catch (error) {
    console.error(`DeviceConfig MQTT: Error subscribing to ${topic}:`, error);
    return false;
  }
};

/**
 * Publish a message to a topic
 * @param {string} topic - The topic to publish to
 * @param {string|Object} message - The message to publish (objects will be JSON stringified)
 * @param {Object} options - Publish options (e.g., { qos: 1, retain: true })
 * @returns {boolean} - Whether the publish was successful
 */
const publish = (topic, message, options = { qos: 0 }) => {
  if (!client || !isConnected) {
    console.error('DeviceConfig MQTT: Cannot publish, client not connected');
    return false;
  }

  try {
    // Convert object to JSON string if needed
    const messageStr = typeof message === 'object' ? JSON.stringify(message) : message;

    client.publish(topic, messageStr, options, (error) => {
      if (error) {
        // console.error(`DeviceConfig MQTT: Error publishing to ${topic}:`, error);
      } else {
        // console.log(`DeviceConfig MQTT: Published to ${topic}: ${messageStr}`);
      }
    });
    return true;
  } catch {
    // console.error(`DeviceConfig MQTT: Error publishing to ${topic}:`, error);
    return false;
  }
};

/**
 * Unsubscribe from a topic
 * @param {string} topic - The topic to unsubscribe from
 * @returns {boolean} - Whether the unsubscribe was successful
 */
const unsubscribe = (topic) => {
  if (!client || !isConnected) {
    console.error('DeviceConfig MQTT: Cannot unsubscribe, client not connected');
    return false;
  }

  try {
    client.unsubscribe(topic, (error) => {
      if (error) {
        console.error(`DeviceConfig MQTT: Error unsubscribing from ${topic}:`, error);
      } else {
        console.log(`DeviceConfig MQTT: Unsubscribed from ${topic}`);
      }
    });
    return true;
  } catch (error) {
    console.error(`DeviceConfig MQTT: Error unsubscribing from ${topic}:`, error);
    return false;
  }
};

/**
 * Register an event listener
 * @param {string} event - The event to listen for (connect, disconnect, message, error, reconnect, configReceived)
 * @param {Function} callback - The callback function
 */
const on = (event, callback) => {
  if (eventListeners[event]) {
    eventListeners[event].push(callback);
  } else {
    console.error(`DeviceConfig MQTT: Unknown event type: ${event}`);
  }
};

/**
 * Remove an event listener
 * @param {string} event - The event to remove the listener from
 * @param {Function} callback - The callback function to remove
 */
const off = (event, callback) => {
  if (eventListeners[event]) {
    eventListeners[event] = eventListeners[event].filter(cb => cb !== callback);
  }
};

/**
 * Disconnect from the MQTT broker
 */
const disconnect = () => {
  if (client) {
    client.end();
    client = null;
    isConnected = false;
    // console.log('DeviceConfig MQTT: Disconnected');
  }
};

/**
 * Request device configuration
 * @param {string} deviceId - Optional device ID to request config for (if omitted, requests all)
 * @returns {boolean} - Whether the request was sent
 */
const requestConfig = (deviceId = null) => {
  const topic = 'device/config/request';
  const message = {
    requestType: deviceId ? 'single' : 'all',
    deviceId: deviceId
  };

  return publish(topic, message);
};

/**
 * Save device configuration
 * @param {string} deviceId - The device ID
 * @param {Object} config - The configuration data
 * @returns {boolean} - Whether the save request was sent
 */
const saveConfig = (deviceId, config) => {
  const topic = `device/${deviceId}/config/update`;
  return publish(topic, config);
};

// Export the service
const deviceConfigMqttService = {
  connect,
  disconnect,
  subscribe,
  publish,
  unsubscribe,
  requestConfig,
  saveConfig,
  on,
  off,
  get isConnected() { return isConnected; }
};

export default deviceConfigMqttService;
