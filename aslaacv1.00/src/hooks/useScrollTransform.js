import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Custom hook for detecting alternative gestures and managing transform state
 * @param {Object} options - Configuration options
 * @param {number} options.resetTimeout - Time in ms before auto-reset (default: 8000)
 * @param {string} options.method - Interaction method: 'double-tap', 'long-press', 'corner-tap' (default: 'double-tap')
 * @param {number} options.longPressDelay - Long press duration in ms (default: 1000)
 * @returns {Object} - { isTransformed, handleClick, handleMouseDown, handleMouseUp, handleTouchStart, handleTouchEnd, reset }
 */
export default function useScrollTransform(options = {}) {
  const {
    resetTimeout = 8000,
    method = 'double-tap',
    longPressDelay = 1000
  } = options;

  const [isTransformed, setIsTransformed] = useState(false);

  const resetTimeoutRef = useRef(null);
  const lastTapRef = useRef(0);
  const longPressTimerRef = useRef(null);
  const touchStartTimeRef = useRef(0);

  // Clear reset timeout
  const clearResetTimeout = useCallback(() => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = null;
    }
  }, []);

  // Clear long press timer
  const clearLongPressTimer = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  }, []);

  // Set reset timeout
  const setResetTimeout = useCallback(() => {
    clearResetTimeout();
    resetTimeoutRef.current = setTimeout(() => {
      setIsTransformed(false);
    }, resetTimeout);
  }, [resetTimeout, clearResetTimeout]);

  // Manual reset function
  const reset = useCallback(() => {
    setIsTransformed(false);
    clearResetTimeout();
    clearLongPressTimer();
  }, [clearResetTimeout, clearLongPressTimer]);

  // Handle double-tap
  const handleDoubleTap = useCallback(() => {
    if (isTransformed) {
      setIsTransformed(false);
      clearResetTimeout();
    } else {
      setIsTransformed(true);
      setResetTimeout();
    }
  }, [isTransformed, setResetTimeout, clearResetTimeout]);

  // Handle click events (for double-tap and corner-tap)
  const handleClick = useCallback((event) => {
    if (method === 'double-tap') {
      const now = Date.now();
      const timeSinceLastTap = now - lastTapRef.current;

      if (timeSinceLastTap < 300) { // Double-tap detected
        event.preventDefault();
        event.stopPropagation();
        handleDoubleTap();
        lastTapRef.current = 0; // Reset to prevent triple-tap
      } else {
        lastTapRef.current = now;
        // Let the first tap pass through for normal functionality
      }
    } else if (method === 'corner-tap') {
      // Check if click is in the top-right corner (scheduler area)
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      const cornerSize = Math.min(rect.width, rect.height) * 0.3; // 30% of button size

      if (x > rect.width - cornerSize && y < cornerSize) {
        event.preventDefault();
        event.stopPropagation();
        handleDoubleTap(); // Reuse the toggle logic
      }
    }
  }, [method, handleDoubleTap]);

  // Handle mouse down (for long-press on desktop)
  const handleMouseDown = useCallback((event) => {
    if (method === 'long-press') {
      clearLongPressTimer();
      longPressTimerRef.current = setTimeout(() => {
        event.preventDefault();
        event.stopPropagation();
        handleDoubleTap(); // Reuse toggle logic
      }, longPressDelay);
    }
  }, [method, longPressDelay, handleDoubleTap, clearLongPressTimer]);

  // Handle mouse up (for long-press on desktop)
  const handleMouseUp = useCallback(() => {
    if (method === 'long-press') {
      clearLongPressTimer();
    }
  }, [method, clearLongPressTimer]);

  // Handle touch start (for long-press on mobile)
  const handleTouchStart = useCallback((event) => {
    if (method === 'long-press') {
      touchStartTimeRef.current = Date.now();
      clearLongPressTimer();
      longPressTimerRef.current = setTimeout(() => {
        event.preventDefault();
        event.stopPropagation();
        handleDoubleTap(); // Reuse toggle logic
      }, longPressDelay);
    }
  }, [method, longPressDelay, handleDoubleTap, clearLongPressTimer]);

  // Handle touch end (for long-press on mobile)
  const handleTouchEnd = useCallback((event) => {
    if (method === 'long-press') {
      clearLongPressTimer();
      const touchDuration = Date.now() - touchStartTimeRef.current;

      // If it was a quick tap (less than 200ms), let it pass through for normal functionality
      if (touchDuration < 200) {
        // This will be handled by the normal click handler
        return;
      }
    }
  }, [method, clearLongPressTimer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearResetTimeout();
      clearLongPressTimer();
    };
  }, [clearResetTimeout, clearLongPressTimer]);

  return {
    isTransformed,
    handleClick,
    handleMouseDown,
    handleMouseUp,
    handleTouchStart,
    handleTouchEnd,
    reset,
    method // For debugging purposes
  };
}
