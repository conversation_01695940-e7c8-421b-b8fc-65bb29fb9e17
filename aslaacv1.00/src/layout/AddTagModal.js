
import PropTypes from 'prop-types';
import { useRef, useState } from 'react';
import { useSnackbar } from 'notistack';
import { useSelector } from 'react-redux';
// @mui
import { Stack, Button, Dialog, DialogContent, DialogActions, DialogTitle, IconButton, Typography, TextField } from '@mui/material';

// redux
import { useDispatch } from '../../redux/store';
// _mock
import _mock from '../../_mock';
import Iconify from '../../components/Iconify';

// ----------------------------------------------------------------------


AddTagModal.propTypes = {
  onModalClose: PropTypes.func,
}
export default function AddTagModal({ onModalClose, ...other }) {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { board } = useSelector((state) => state.kanban);

  const tagName = useRef("");

  const setTagName = (event) => {
    tagName.current = event.target.value;
  }

  const handleAddTag = () => {
    // dispatch(addTag({ card: task, columnId: id }));
    enqueueSnackbar(`${tagName.current} Tag add success!`);

  };

  return (
    <Dialog
      aria-describedby="alert-dialog-slide-description"
      fullWidth
      scroll={'body'}
      maxWidth={'xs'}
      onClose={onModalClose}
      {...other}
    >
      <Stack spacing={2} direction={'row'} alignItems={'center'} pt={5} justifyContent={'center'} color={'text.secondary'}>
        <Iconify icon={'fluent:tag-20-filled'} width={24} height={24} />
        <Typography variant={'h4'} >
          Add tag
        </Typography>
      </Stack>
      <Typography sx={{ textAlign: 'center', mb: 2 }} variant={'subtitle1'} color={'text.secondary'} >
        Tags allow you to easily organize your programs
      </Typography>
      <IconButton
        sx={{ position: 'absolute', right: 10, top: 10, zIndex: 1 }}
        onClick={onModalClose}
      >
        <Iconify icon={'eva:close-fill'} width={30} height={30} />
      </IconButton>
      <DialogContent dividers sx={{ textAlign: 'center'}}>
        <TextField
          label="Tag Name"
          onChange={setTagName}
        />
      </DialogContent>
      <DialogActions>
        <Button
          variant={'contained'}
          size={'large'}
          fullWidth
          sx={{ width: '70%', mx: 'auto', }}
          onClick={handleAddTag}
        >
          Create Tag
        </Button>
      </DialogActions>

    </Dialog>
  );
}





