import { useState, useEffect,useCallback } from 'react';
// @mui
import { alpha } from '@mui/material/styles';
import {  Stack, MenuItem,  Button } from '@mui/material';
import { useTranslation } from "react-i18next";
// routes 

// components
import MenuPopover from '../components/MenuPopover';
import { IconButtonAnimate } from '../components/animate';
import Iconify from '../components/Iconify';
//


// ----------------------------------------------------------------------

const MENU_OPTIONS = [
    {
        label: 'Монгол',
        value: 'mn',
        icon: 'twemoji:flag-mongolia'
    },
    {
        label: 'English',
        value: 'en',
        icon: 'twemoji:flag-england'
    },
    {
        label: 'Россия',
        value: 'ru',
        icon: 'twemoji:flag-russia'
    },
];

// ----------------------------------------------------------------------

export default function SettingPopover() {
    const [menuOptions] = useState(MENU_OPTIONS);
    const [currentLng, setCurrentLng] = useState(MENU_OPTIONS[0]);

    const { i18n } = useTranslation();

    const [open, setOpen] = useState(null);

    const handleOpen = (event) => {
        setOpen(event.currentTarget);
    };

    const handleClose = () => {
        setOpen(null);

    };

// Inside your component
const handleChangeLng = useCallback((option) => {
    localStorage.setItem("language", option.value);
    i18n.changeLanguage(option.value);
    setCurrentLng(option);
    setOpen(null);
}, [i18n]); // i18n is a dependency, so the function will be recalculated if i18n changes


useEffect(() => {
    const lang = localStorage.getItem("language");

    if (!lang || lang === 'mn') {
        handleChangeLng(menuOptions[0]);
    }
    else if (lang === 'en') {
        handleChangeLng(menuOptions[1]);
    }
    else if (lang === 'ru') {
        handleChangeLng(menuOptions[2]);
    }
}, [handleChangeLng, menuOptions]); // Now handleChangeLng is stable


    return (
        <>
            <IconButtonAnimate onClick={handleOpen}
                sx={
                    {
                        p: 0,
                        ...(open && {
                            '&:before': {
                                zIndex: 1,
                                content: "''",
                                width: '100%',
                                height: '100%',
                                borderRadius: '50%',
                                position: 'absolute',
                                bgcolor: (theme) => alpha(theme.palette.grey[900], 0.1),
                            },
                        }),
                    }
                } >
                <Iconify icon={currentLng.icon}
                    width={
                        { sx: 20, md: 30 }}
                    height={
                        { sx: 20, md: 30 }}
                /> </IconButtonAnimate>


            <MenuPopover open={Boolean(open)}
                anchorEl={open}
                onClose={handleClose}
                sx={
                    {
                        p: 0,
                        mt: 1.5,
                        ml: 0.75,
                        '& .MuiMenuItem-root': {
                            typography: 'body2',
                            borderRadius: 0.75,
                            lineHeight: 1,
                        },
                    }
                } >

                <Stack sx={{ p: 1 }} >
                    {
                        menuOptions.map((option) => (
                            <MenuItem key={option.label}
                                to={option.linkTo}
                                component={Button}

                                onClick={() => handleChangeLng(option)} sx={{ minHeight: { xs: 24 } }}>
                                <Iconify icon={option.icon} width={24} height={24} />
                                &nbsp;&nbsp;{option.label}

                            </MenuItem>
                        ))

                    }
                </Stack>

            </MenuPopover>

        </>
    );
}