import {Outlet } from "react-router-dom";
// @mui
import { Stack } from "@mui/material";
// components
//
import MainHeader from "./MainHeader";
import useAuth from "../hooks/useAuth";
import { useEffect } from "react";
import axios from "../utils/axios";

// ----------------------------------------------------------------------

export default function Layout() {
  const { user } = useAuth();
  useEffect(() => {
    if (user && user.device) {
      // console.log("check online");
      // check online
      axios
        .post(`/api/device/checkline`, {
          deviceNumber: user?.device?.deviceNumber,
        })
        .then(() => {
          // console.log("check simcard");
          // check simcard

        })
        .catch(() => {});

      // dispatch(getNotifications());
    }
  }, [user]);
  return (
    <Stack sx={{ minHeight: 1 }}>
      <MainHeader />
      <Outlet />
    </Stack>
  );
}
