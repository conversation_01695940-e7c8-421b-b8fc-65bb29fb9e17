import PropTypes from "prop-types";
import { useSelector } from "react-redux";
import {
  Avatar,
  Badge,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  List,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
  ListS<PERSON>header,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import {  useState } from "react";
import { IconButtonAnimate } from "../components/animate";
import Iconify from "../components/Iconify";
import MenuPopover from "../components/MenuPopover";
import Scrollbar from "../components/Scrollbar";
import { fToNow } from "../utils/formatUtils";
import axios from "../utils/axios";
import { dispatch } from "../redux/store";
import { getNotifications } from "../redux/slices/notification";
import useAuth from "../hooks/useAuth";


export default function NotificationPopover() {
  const { notifications } = useSelector((state) => state.notification);
  const unreads = notifications?.filter((n) => n.read === false);
  const totalUnRead = unreads.length;
  const [ setLoaded] = useState(false);
  const [open, setOpen] = useState(null);
  const { user } = useAuth();
  const [fetched, setFetched] = useState(false);

  const sendCheckCommand = () => {
    axios
      .post(`api/device/check-sim`, {
        deviceNumber: user?.device?.deviceNumber,
      })
      .then((res) => {
        setLoaded(true);
        setTimeout(() => {
          dispatch(getNotifications());
        }, 1000);
        setTimeout(() => {
          dispatch(getNotifications());
        }, 2000);
        setTimeout(() => {
          dispatch(getNotifications());
          setFetched(true);
        }, 4000);
      })
      .catch((err) => {});
  };
  const handleOpen = (event) => {
    if (totalUnRead > 0) {
      setOpen(event.currentTarget);
    } else {
      sendCheckCommand();
    }
  };

  const handleClose = () => {
    setOpen(null);
  };

  const onRead = (notification) => {
    setOpen(null);
    axios
      .post(`/api/log/read-sim-log`, { id: notification._id })
      .then((res) => {
        dispatch(getNotifications());
      });
  };
  return (
    <>
      <IconButtonAnimate
        color={open ? "primary" : "default"}
        onClick={handleOpen}
        sx={{ width: 40, height: 40 }}
      >
        <Badge badgeContent={totalUnRead} color="error">
          <Iconify icon="flat-color-icons:sim-card" width={20} height={20} />
        </Badge>
      </IconButtonAnimate>

      <MenuPopover
        open={Boolean(open)}
        anchorEl={open}
        onClose={handleClose}
        sx={{ width: 360, p: 0, mt: 1.5, ml: 0.75 }}
      >
        {fetched && (
          <>
            <Box sx={{ display: "flex", alignItems: "center", py: 2, px: 2.5 }}>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle1">Notifications</Typography>
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  You have {totalUnRead} unread messages
                </Typography>
              </Box>

              <Tooltip title=" Mark all as read">
                <IconButtonAnimate
                  color="primary"
                  onClick={() => {
                    dispatch(getNotifications());
                  }}
                >
                  <Iconify icon="ic:outline-refresh" width={20} height={20} />
                </IconButtonAnimate>
              </Tooltip>
            </Box>

            <Divider sx={{ borderStyle: "dashed" }} />

            <Scrollbar
              sx={{ height: { xs: 500, sm: window.innerHeight - 200 } }}
            >
              <List
                disablePadding
                subheader={
                  <ListSubheader
                    disableSticky
                    sx={{ py: 1, px: 2.5, typography: "overline" }}
                  >
                    New
                  </ListSubheader>
                }
              >
                {unreads?.map((notification) => (
                  <NotificationItem
                    key={notification._id}
                    notification={notification}
                    readAction={() => onRead(notification)}
                  />
                ))}
              </List>
            </Scrollbar>
          </>
        )}
        {!fetched && (
          <Stack justifyContent={"center"} alignItems={"center"} p={2}>
            <CircularProgress />
          </Stack>
        )}
      </MenuPopover>
    </>
  );
}

// ----------------------------------------------------------------------

NotificationItem.propTypes = {
  notification: PropTypes.shape({
    createdAt: PropTypes.instanceOf(Date),
    id: PropTypes.string,
    isUnRead: PropTypes.bool,
    title: PropTypes.string,
    description: PropTypes.string,
    type: PropTypes.string,
    avatar: PropTypes.any,
  }),
};

export function NotificationItem({
  notification,
  removeAction = false,
  readAction = false,
  navigateAction = false,
}) {
  const { avatar, title } = renderContent(notification);

  return (
    <ListItemButton
      onClick={() => {
        if (readAction !== false) {
          readAction();
        }
      }}
      sx={{
        py: 1.5,
        px: 2.5,
        mt: "1px",
        ...(!notification.read && {
          bgcolor: "action.selected",
        }),
      }}
    >
      <ListItemAvatar>
        <Avatar sx={{ bgcolor: "background.neutral" }}>{avatar}</Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={title}
        secondary={
          <Typography
            variant="caption"
            sx={{
              mt: 0.5,
              display: "flex",
              alignItems: "center",
              color: "text.disabled",
            }}
          >
            <Iconify
              icon="eva:clock-outline"
              sx={{ mr: 0.5, width: 16, height: 16 }}
            />
            {fToNow(notification?.received)}
          </Typography>
        }
      />
      {removeAction !== false && readAction !== false && (
        <Stack direction="row" gap={1}>
          <IconButton onClick={removeAction}>
            <Iconify icon="tabler:trash" />
          </IconButton>
          {!notification.read && (
            <IconButton onClick={readAction}>
              <Iconify icon="eva:done-all-fill" />
            </IconButton>
          )}
        </Stack>
      )}
    </ListItemButton>
  );
}

// ----------------------------------------------------------------------

function renderContent(notification) {
  const title = (
    <Typography variant="subtitle2">{notification.content}</Typography>
  );

  return {
    avatar: (
      <img
        alt={notification.title}
        src="https://minimal-assets-api.vercel.app/assets/icons/ic_notification_chat.svg"
      />
    ),
    title,
  };
}
