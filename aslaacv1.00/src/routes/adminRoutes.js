import { lazy } from 'react';
import AuthGuard from '../guards/AuthGuard';
import AdminGuard from '../guards/AdminGuard';

const DeviceEdit = lazy(() => import('../pages/admin/DeviceEdit'));
const UserManage = lazy(() => import('../pages/admin/UserManage'));
const TransactionLogs = lazy(() => import('../pages/admin/TransactionLogs'));
const WithdawRequests = lazy(() => import('../pages/admin/WithdrawLogs'));
const Orders = lazy(() => import('../pages/admin/OrderList'));
const AppManagement = lazy(() => import('../pages/admin/AppManagement'));

const adminRoutes = {
  path: 'admin',
  children: [
    { path: 'device/:id', element: <AuthGuard><AdminGuard><DeviceEdit /></AdminGuard></AuthGuard> },
    { path: 'user-manage', element: <AuthGuard><AdminGuard><UserManage /></AdminGuard></AuthGuard> },
    { path: 'orders', element: <AuthGuard><AdminGuard><Orders /></AdminGuard></AuthGuard> },
    { path: 'transactions', element: <AuthGuard><AdminGuard><TransactionLogs /></AdminGuard></AuthGuard> },
    { path: 'withdraw-requests', element: <AuthGuard><AdminGuard><WithdawRequests /></AdminGuard></AuthGuard> },
    { path: 'app-management', element: <AuthGuard><AdminGuard><AppManagement /></AdminGuard></AuthGuard> },
  ],
};

export default adminRoutes;
