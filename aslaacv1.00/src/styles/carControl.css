/* Car Control System Fonts and Styling */

/* Digital Display Font Classes - Refined for Nikola Semi */
.digital-font {
  font-family: 'Orbitron', monospace !important;
  font-weight: 500; /* Reduced weight for cleaner look */
  letter-spacing: 0.1em;
  text-shadow: 0 0 3px rgba(0, 255, 255, 0.2); /* Reduced glow */
  color: #f0f0f0; /* Clean off-white */
}

.digital-font-large {
  font-family: 'Orbitron', monospace !important;
  font-weight: 600; /* Reduced from 700 */
  font-size: 1.2rem;
  letter-spacing: 0.15em;
  text-shadow: 0 0 3px rgba(0, 255, 255, 0.2); /* Reduced glow from 0.4 to 0.2 */
  color: #f0f0f0; /* Clean off-white */
}

.digital-font-small {
  font-family: 'Orbitron', monospace !important;
  font-weight: 400; /* Reduced from 500 */
  font-size: 0.85rem;
  letter-spacing: 0.08em;
  text-shadow: 0 0 3px rgba(0, 255, 255, 0.2); /* Consistent reduced glow */
  color: #f0f0f0; /* Clean off-white */
}

/* Automotive Data Font Classes - Standardized */
.automotive-font {
  font-family: 'Roboto Mono', monospace !important;
  font-weight: 500; /* Values */
  letter-spacing: 0.05em; /* Standardized spacing */
}

.automotive-font-label {
  font-family: 'Roboto Mono', monospace !important;
  font-weight: 400; /* Labels */
  letter-spacing: 0.05em; /* Standardized spacing */
}

.automotive-font-bold {
  font-family: 'Roboto Mono', monospace !important;
  font-weight: 700;
  letter-spacing: 0.05em; /* Standardized spacing */
}

/* Technical Information Font Classes - Standardized */
.technical-font {
  font-family: 'Roboto Mono', monospace !important; /* Unified font */
  font-weight: 400;
  letter-spacing: 0.05em; /* Standardized spacing */
  font-size: 0.9rem;
}

/* Car Status Display */
.car-status-display {
  font-family: 'Orbitron', monospace !important;
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: 0.12em;
  text-transform: uppercase;
  text-shadow: 0 0 6px rgba(0, 255, 255, 0.3);
}

/* Speed and Measurement Display - Refined for Nikola Semi */
.speed-display {
  font-family: 'Orbitron', monospace !important;
  font-weight: 600; /* Reduced from 800 for cleaner look */
  font-size: 1.3rem;
  letter-spacing: 0.2em;
  text-shadow: 0 0 3px rgba(0, 255, 255, 0.2); /* Reduced glow from 0.5 to 0.2 */
  color: #f0f0f0; /* Clean off-white instead of cyan */
}

/* Voltage and Battery Display */
.voltage-display {
  font-family: 'Orbitron', monospace !important;
  font-weight: 600;
  letter-spacing: 0.1em;
}

.voltage-display.high {
  color: #00ff00;
  text-shadow: 0 0 5px rgba(0, 255, 0, 0.4);
}

.voltage-display.medium {
  color: #ffff00;
  text-shadow: 0 0 5px rgba(255, 255, 0, 0.4);
}

.voltage-display.low {
  color: #ff0000;
  text-shadow: 0 0 5px rgba(255, 0, 0, 0.4);
}

/* Temperature Display */
.temperature-display {
  font-family: 'Orbitron', monospace !important;
  font-weight: 600;
  letter-spacing: 0.08em;
  color: #ff8c00;
  text-shadow: 0 0 4px rgba(255, 140, 0, 0.3);
}

/* Signal Strength Display - Standardized */
.signal-display {
  font-family: 'Roboto Mono', monospace !important;
  font-weight: 500; /* Value weight */
  letter-spacing: 0.05em; /* Standardized spacing */
  font-size: 0.9rem;
}

/* GPS Coordinates Display - Standardized */
.gps-display {
  font-family: 'Roboto Mono', monospace !important; /* Unified font */
  font-weight: 500; /* Value weight */
  letter-spacing: 0.05em; /* Standardized spacing */
  font-size: 0.85rem;
  color: #00bfff;
}

/* Control Button Text */
.control-button-text {
  font-family: 'Roboto Mono', monospace !important;
  font-weight: 700;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

/* Status Indicators */
.status-indicator {
  font-family: 'Orbitron', monospace !important;
  font-weight: 600;
  font-size: 0.8rem;
  letter-spacing: 0.15em;
  text-transform: uppercase;
}

.status-indicator.online {
  color: #00ff00;
  text-shadow: 0 0 5px rgba(0, 255, 0, 0.4);
}

.status-indicator.offline {
  color: #ff0000;
  text-shadow: 0 0 5px rgba(255, 0, 0, 0.4);
}

.status-indicator.connecting {
  color: #ffff00;
  text-shadow: 0 0 5px rgba(255, 255, 0, 0.4);
}

/* Device Information Display */
.device-info {
  font-family: 'Roboto Mono', monospace !important;
  font-weight: 500;
  letter-spacing: 0.04em;
  font-size: 0.9rem;
}

/* Protocol Display - Standardized */
.protocol-display {
  font-family: 'Roboto Mono', monospace !important; /* Unified font */
  font-weight: 500; /* Value weight */
  letter-spacing: 0.05em; /* Standardized spacing */
  font-size: 0.8rem;
  text-transform: uppercase;
  color: #00bfff;
  text-shadow: 0 0 3px rgba(0, 191, 255, 0.3);
}

/* Mobile Responsive Adjustments */
@media (max-width: 600px) {
  .digital-font-large {
    font-size: 1rem;
    letter-spacing: 0.12em;
  }
  
  .speed-display {
    font-size: 1.1rem;
    letter-spacing: 0.15em;
  }
  
  .car-status-display {
    font-size: 0.95rem;
    letter-spacing: 0.1em;
  }
  
  .digital-font {
    font-size: 0.9rem;
    letter-spacing: 0.08em;
  }
  
  .automotive-font {
    font-size: 0.85rem;
  }
  
  .technical-font {
    font-size: 0.8rem;
  }
}

/* High contrast mode for better visibility */
.high-contrast .digital-font {
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.6);
  font-weight: 700;
}

.high-contrast .voltage-display.high {
  text-shadow: 0 0 8px rgba(0, 255, 0, 0.6);
}

.high-contrast .voltage-display.low {
  text-shadow: 0 0 8px rgba(255, 0, 0, 0.6);
}

/* Animation for digital displays */
@keyframes digital-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
  }
  50% {
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.6);
  }
}

.digital-font.animated {
  animation: digital-glow 2s ease-in-out infinite;
}

/* Car dashboard style background for text */
.dashboard-text {
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 255, 255, 0.3);
}
