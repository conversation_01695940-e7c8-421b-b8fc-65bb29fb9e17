import numeral from 'numeral';
import { format, getTime, formatDistanceToNow } from 'date-fns';

// Number Formatting Functions
export function formatExpiredDate(dateString) {
  const dateParts = dateString.split('/');
  return `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
}
export function getCurrentSeason() {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) {
    return 'spring';
  } else if (month >= 5 && month <= 7) {
    return 'summer';
  } else if (month >= 8 && month <= 10) {
    return 'autumn';
  } else {
    return 'winter';
  }
}
export function getSimCardColor(expiredDate) {
  if (!expiredDate) return "inherit";
  let delimiter = '/';
  if (expiredDate.includes('-')) {
    delimiter = '-';
  }
  const dateParts = expiredDate.split(delimiter);
  if (dateParts.length !== 3) {
    // console.error("Invalid date format:", expiredDate);
    return "inherit";
  }
  let [day, month, year] = dateParts.map(part => parseInt(part, 10));
  if (year < 100) {
    year += 2000;
  }
  if (month < 1 || month > 12 || day < 1 || day > 31) {
    // console.error("Invalid date value:", expiredDate);
    return "inherit";
  }
  const expiredDateObj = new Date(year, month - 1, day);
  if (isNaN(expiredDateObj.getTime())) {
    // console.error("Constructed date is invalid:", expiredDateObj);
    return "inherit";
  }
  const currentDate = new Date();
  const diffTime = expiredDateObj - currentDate;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  if (diffDays < 0) {
    return "red";
  } else if (diffDays <= 7) {
    return "yellow";
  } else {
    return "inherit";
  }
}

export function fPercent(number) {
  return numeral(number / 100).format('0.0%');
}

export function fNumber(number) {
  return numeral(number).format();
}

export function fShortenNumber(number) {
  return numeral(number).format('0.00a').replace('.00', '');
}

export function fData(number) {
  return numeral(number).format('0.0 b');
}

export function fRemain(current, period) {
  const rm = period - current;
  const d = Math.floor(rm / 3600 / 24 / 1000);
  const h = Math.floor((rm - d * 3600 * 24 * 1000) / 3600 / 1000);
  const m = Math.floor((rm - d * 3600 * 24 * 1000 - h * 3600 * 1000) / 60 / 1000);
  const value = ((d > 0 ? (`${d}d `) : '') + (h > 0 ? (`${h}h `) : '') + (m > 0 ? (`${m}m `) : ''));
  return {
      text:`${value}`,
      isRemain:rm>0,
  }
}

export function fRemainD(remain) {
  const rm = remain;
  const d = Math.floor(rm / 3600 / 24 / 1000);
  const h = Math.floor((rm - d * 3600 * 24 * 1000) / 3600 / 1000);
  const m = Math.floor((rm - d * 3600 * 24 * 1000 - h * 3600 * 1000) / 60 / 1000);
  const value = ((d > 0 ? (`${d}d `) : '') + (h > 0 ? (`${h}h `) : '') + (m > 0 ? (`${m}m `) : ''));
  return {
      text:`${value}`,
      isRemain:rm>0,
  }
}

// Date Formatting Functions
export function fDate(date) {
    try{
        return format(new Date(date), 'dd MMMM yyyy');
    }
    catch(err){
        return ''
    }
}

export function formatSQLDate(date) {
    if (date)
        return format(new Date(date), 'yyyy-MM-dd');
    else
        return ""
}

export function fDateTime(date) {
    try{
        return format(new Date(date), 'dd MMM yyyy HH:mm');
    }
    catch(err){
        return ''
    }
}

export function fTimestamp(date) {
    return getTime(new Date(date));
}

export function fDateTimeSuffix(date) {
    return format(new Date(date), 'dd/MM/yyyy hh:mm p');
}

export function fToNow(date) {
    return formatDistanceToNow(new Date(date), {
        addSuffix: true
    });
}

export function fTimer(date) {
    if (date)
        return format(new Date(date), 'hh:mm:ss');
    else
        return ''
}

// Additional Formatter Function
export const getYMDHISDate = (date) => {
    if(date && date.indexOf("T")!==-1){
        const d = date.split("T")[0];
        const t = date.split("T")[1];
        return `${d} ${t.substring(0,8)}`;
    }
    return date;
}
