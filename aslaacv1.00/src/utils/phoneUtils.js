// Utility function to mask the first 4 digits of a phone number
export const maskPhoneNumber = (phoneNumber) => {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
        return phoneNumber;
    }
    
    // If phone number is shorter than 4 digits, return as is
    if (phoneNumber.length <= 4) {
        return phoneNumber;
    }
    
    // Replace first 4 digits with asterisks
    return '****' + phoneNumber.substring(4);
};
