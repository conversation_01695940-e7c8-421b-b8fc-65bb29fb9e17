{"name": "client", "version": "0.1.0", "private": true, "scripts": {"start": "export NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "export NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx ./src"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": [">0.2%", "not dead", "not op_mini all"], "dependencies": {"@emotion/cache": "^11.7.1", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@hookform/resolvers": "^2.8.8", "@iconify/react": "^3.1.0", "@mui/icons-material": "^5.10.6", "@mui/lab": "^5.0.0-alpha.68", "@mui/material": "^5.2.4", "@mui/x-date-pickers": "^5.0.8", "@reduxjs/toolkit": "^1.8.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "axios": "^0.26.0", "change-case": "^4.1.2", "date-fns": "^2.24.0", "dayjs": "^1.11.6", "dotenv": "^16.4.7", "downloadjs": "^1.4.7", "file-saver": "^2.0.5", "firebase": "^9.6.7", "framer-motion": "^4.1.17", "google-map-react": "^2.1.10", "history": "^5.2.0", "i18next": "^21.6.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.0", "js-file-download": "^0.4.12", "jsonwebtoken": "^8.5.1", "jwt-decode": "^3.1.2", "leaflet": "^1.9.1", "leaflet-routing-machine": "^3.2.12", "license-key-generator": "^1.1.2", "moment": "^2.29.4", "mqtt": "^5.13.0", "notistack": "^2.0.3", "nprogress": "^0.2.0", "numeral": "^2.0.6", "paho-mqtt": "^1.1.0", "prop-types": "^15.7.2", "pusher-js": "^7.0.6", "qrcode.react": "^4.2.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-dropzone": "^12.0.4", "react-export-table-to-excel": "^1.0.6", "react-firebase-hooks": "^5.0.3", "react-helmet-async": "^1.2.2", "react-hook-form": "^7.27.0", "react-i18next": "^11.18.6", "react-intersection-observer": "^8.33.1", "react-lazy-load-image-component": "^1.5.1", "react-leaflet": "^4.1.0", "react-leaflet-marker": "^2.0.0", "react-otp-input": "^3.1.1", "react-qr-barcode-scanner": "^1.0.6", "react-qr-code": "^2.0.5", "react-redux": "^7.2.6", "react-router": "^6.2.1", "react-router-dom": "^6.2.1", "react-scripts": "^4.0.3", "react-snowfall": "^1.2.1", "react-spring": "^9.7.3", "react-text-mask": "^5.5.0", "redux": "^4.1.2", "redux-persist": "^6.0.0", "simplebar": "^5.3.6", "simplebar-react": "^2.3.6", "socket.io-client": "^4.4.1", "stylis": "^4.0.13", "stylis-plugin-rtl": "^2.1.1", "swiper": "^8.4.4", "use-sound": "^4.0.1", "web-vitals": "^0.2.4", "workbox-background-sync": "^5.1.3", "workbox-broadcast-update": "^5.1.3", "workbox-cacheable-response": "^5.1.3", "workbox-core": "^5.1.3", "workbox-expiration": "^5.1.3", "workbox-google-analytics": "^5.1.3", "workbox-navigation-preload": "^5.1.3", "workbox-precaching": "^5.1.3", "workbox-range-requests": "^5.1.3", "workbox-routing": "^5.1.3", "workbox-strategies": "^5.1.3", "workbox-streams": "^5.1.3", "yup": "^0.32.11"}, "devDependencies": {"prettier": "^2.5.1", "typescript": "^4.4.4"}}