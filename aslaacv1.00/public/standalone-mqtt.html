<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone MQTT Client</title>
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            margin-bottom: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.success {
            color: #28a745;
        }
        .button-group {
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0069d9;
        }
        button.disconnect {
            background-color: #dc3545;
        }
        button.disconnect:hover {
            background-color: #c82333;
        }
        .device-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .device-info div {
            margin-bottom: 10px;
        }
        .device-info strong {
            display: inline-block;
            width: 120px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Standalone MQTT Client</h1>

        <div id="status" class="status disconnected">Disconnected</div>

        <div class="form-group">
            <label for="device-number">Device Number:</label>
            <input type="text" id="device-number" value="123456">
        </div>

        <div class="device-info">
            <div><strong>Device:</strong> <span id="device-number-display">-</span></div>
            <div><strong>Firmware:</strong> <span id="firmware-version">-</span></div>
            <div><strong>Sound:</strong> <span id="sound-enabled">-</span></div>
            <div><strong>Key:</strong> <span id="key-enabled">-</span></div>
        </div>

        <div class="button-group">
            <button id="connect-1">Connect to *************</button>
            <button id="connect-2">Connect to ************</button>
            <button id="connect-3">Connect to **************</button>
            <button id="connect-emqx">Connect to broker.emqx.io</button>
            <button id="disconnect" class="disconnect">Disconnect</button>
            <button id="check">Send Check Command</button>
            <button id="clear-log">Clear Log</button>
        </div>

        <div id="log" class="log"></div>
    </div>

    <script>
        // Get elements
        const deviceNumberInput = document.getElementById('device-number');
        const deviceNumberDisplay = document.getElementById('device-number-display');
        const firmwareVersionElement = document.getElementById('firmware-version');
        const soundEnabledElement = document.getElementById('sound-enabled');
        const keyEnabledElement = document.getElementById('key-enabled');
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');

        // Get device number from URL parameter if available
        const urlParams = new URLSearchParams(window.location.search);
        const deviceNumberFromUrl = urlParams.get('device');
        if (deviceNumberFromUrl) {
            deviceNumberInput.value = deviceNumberFromUrl;
        }

        // Set initial device number
        deviceNumberDisplay.textContent = deviceNumberInput.value;

        // Update device number when input changes
        deviceNumberInput.addEventListener('input', () => {
            deviceNumberDisplay.textContent = deviceNumberInput.value;
        });

        // Log function
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Clear log button
        document.getElementById('clear-log').addEventListener('click', () => {
            logElement.innerHTML = '';
        });

        // MQTT client
        let client = null;
        let activeBroker = null;

        // Connect buttons
        document.getElementById('connect-1').addEventListener('click', () => connect('*************'));
        document.getElementById('connect-2').addEventListener('click', () => connect('************'));
        document.getElementById('connect-3').addEventListener('click', () => connect('**************'));
        document.getElementById('connect-emqx').addEventListener('click', () => connect('broker.emqx.io'));

        // Disconnect button
        document.getElementById('disconnect').addEventListener('click', disconnect);

        // Check button
        document.getElementById('check').addEventListener('click', sendCheckCommand);

        // Connect function
        function connect(broker) {
            try {
                // Update status
                statusElement.className = 'status connecting';
                statusElement.textContent = `Connecting to ${broker}...`;

                // Disconnect existing client if any
                if (client) {
                    disconnect();
                }

                // Get device number
                const deviceNumber = deviceNumberInput.value;

                // Create client ID
                const clientId = `standalone_${deviceNumber}_${Math.random().toString(16).substring(2, 10)}`;

                // Create broker URL
                const brokerUrl = `ws://${broker}:8083/mqtt`;

                log(`Connecting to ${brokerUrl} with client ID ${clientId}`);

                // Connection options
                const options = {
                    clientId: clientId,
                    username: "admin",
                    password: "public",
                    clean: true,
                    keepalive: 60,
                    reconnectPeriod: 1000,
                    connectTimeout: 30000
                };

                // Create client
                client = mqtt.connect(brokerUrl, options);
                activeBroker = broker;

                // Set up event handlers
                client.on('connect', () => {
                    log(`Connected to ${broker}!`, 'success');
                    statusElement.className = 'status connected';
                    statusElement.textContent = `Connected to ${broker}`;

                    // Subscribe to device topics
                    const deviceNumber = deviceNumberInput.value;
                    client.subscribe(`${deviceNumber}/msg`);
                    client.subscribe(deviceNumber);
                    log(`Subscribed to ${deviceNumber}/msg and ${deviceNumber}`);

                    // Send check command
                    sendCheckCommand();
                });

                client.on('message', (topic, message) => {
                    try {
                        const msg = message.toString();
                        log(`Received message on ${topic}: ${msg}`);

                        // Try to parse as JSON
                        try {
                            const payload = JSON.parse(msg);

                            // Update device info
                            if (payload.firmware) {
                                firmwareVersionElement.textContent = payload.firmware;
                            }

                            if (payload.sound !== undefined) {
                                soundEnabledElement.textContent =
                                    payload.sound === 'on' || payload.sound === true ? 'Enabled' : 'Disabled';
                            }

                            if (payload.key !== undefined) {
                                keyEnabledElement.textContent =
                                    payload.key === 'on' || payload.key === true ? 'Enabled' : 'Disabled';
                            }
                        } catch (e) {
                            // Not JSON, that's fine
                            log('Message is not valid JSON');
                        }
                    } catch (error) {
                        log(`Error processing message: ${error.message}`, 'error');
                    }
                });

                client.on('error', (error) => {
                    log(`Error: ${error.message}`, 'error');
                    statusElement.className = 'status disconnected';
                    statusElement.textContent = `Error: ${error.message}`;
                });

                client.on('offline', () => {
                    log(`Client is offline`, 'error');
                    statusElement.className = 'status disconnected';
                    statusElement.textContent = 'Offline';
                });

                client.on('reconnect', () => {
                    log(`Reconnecting to ${broker}...`);
                    statusElement.className = 'status connecting';
                    statusElement.textContent = `Reconnecting to ${broker}...`;
                });

                return true;
            } catch (error) {
                log(`Error creating client: ${error.message}`, 'error');
                return false;
            }
        }

        // Disconnect function
        function disconnect() {
            if (client) {
                client.end();
                client = null;
                activeBroker = null;

                statusElement.className = 'status disconnected';
                statusElement.textContent = 'Disconnected';

                log('Disconnected from broker');
            }
        }

        // Send check command
        function sendCheckCommand() {
            if (!client) {
                log('Cannot send check command, not connected', 'error');
                return;
            }

            try {
                const deviceNumber = deviceNumberInput.value;

                const checkCommand = {
                    cmd: "check",
                    device: deviceNumber,
                    timestamp: new Date().toISOString()
                };

                const topic = `${deviceNumber}/cmd`;
                const message = JSON.stringify(checkCommand);

                log(`Sending check command to ${topic}: ${message}`);
                client.publish(topic, message);
            } catch (error) {
                log(`Error sending check command: ${error.message}`, 'error');
            }
        }

        // Auto-connect to first broker on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                connect('*************');
            }, 1000);
        });
    </script>
</body>
</html>
