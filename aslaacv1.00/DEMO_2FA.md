# 🔐 Two-Factor Authentication Demo

## How to Access 2FA Settings

The 2FA settings have been added to your settings menu. Here's how to access it:

### Step 1: Open Settings Menu
1. Look for the user/profile icon in your app's header/navigation
2. Click on it to open the settings dropdown menu

### Step 2: Find 2FA Option
In the settings menu, you'll see a new option:
```
🔐 Two-Factor Authentication
```
This appears right after the "Change PIN Code" option.

### Step 3: Click to Open 2FA Settings
Click on the "🔐 Two-Factor Authentication" menu item to open the 2FA management dialog.

## What You'll See

### When 2FA is Disabled (Default)
- A toggle switch showing "Two-Factor Authentication" is OFF
- Description: "Secure your account with an authenticator app"
- Warning message: "Your account is not protected by two-factor authentication"

### When You Enable 2FA
1. **Setup Flow**: 
   - QR code appears for scanning with Google Authenticator
   - Manual entry key provided as backup
   - Instructions for setup

2. **Verification**:
   - Enter 6-digit code from your authenticator app
   - System verifies the code

3. **Backup Codes**:
   - 10 single-use backup codes are generated
   - Option to copy or download them
   - Warning to save them securely

### When 2FA is Enabled
- Toggle switch shows "Two-Factor Authentication" is ON
- Success message: "2FA is enabled since [date]"
- Shows number of unused backup codes remaining
- Button to "Generate New Backup Codes"
- Information about backup code usage

## Testing the Feature

### 1. Enable 2FA
```
Settings Menu → 🔐 Two-Factor Authentication → Toggle ON
```

### 2. Scan QR Code
- Use Google Authenticator app
- Scan the displayed QR code
- Or manually enter the secret key

### 3. Verify Setup
- Enter the 6-digit code from your app
- Click "Verify & Enable"

### 4. Save Backup Codes
- Copy or download the 10 backup codes
- Store them securely (password manager, safe place)

### 5. Test Login with 2FA
- Log out of your account
- Log back in with phone number and PIN
- You'll now see a 2FA verification screen
- Enter the 6-digit code from Google Authenticator
- Or use a backup code if needed

## API Endpoints Available

The following endpoints are now available for 2FA:

```
GET  /api/2fa/status           - Get 2FA status
POST /api/2fa/setup            - Initiate 2FA setup
POST /api/2fa/enable           - Enable 2FA with verification
POST /api/2fa/verify           - Verify TOTP/backup code
POST /api/2fa/disable          - Disable 2FA
POST /api/2fa/backup-codes     - Generate new backup codes
```

## Security Features

✅ **Rate Limiting**: 5 attempts per 15 minutes for verification
✅ **Encryption**: Secrets stored encrypted in database
✅ **Replay Protection**: Tokens can only be used once
✅ **Audit Logging**: All 2FA events are logged
✅ **Backup Codes**: 10 single-use recovery codes
✅ **Time Tolerance**: ±60 seconds for clock drift

## Troubleshooting

### "Invalid TOTP token"
- Check your phone's time is correct
- Wait for next code (codes change every 30 seconds)
- Try using a backup code

### Can't see 2FA option in menu
- Make sure you're logged in
- Check that the TwoFactorSettings component is imported
- Verify the menu item was added correctly

### Frontend not loading
- Check browser console for errors
- Ensure all dependencies are installed:
  ```bash
  npm install qrcode.react react-otp-input
  ```

## File Locations

### Backend Files
- `controller/twoFactorController.js` - Main 2FA logic
- `routes/api/twoFactor.js` - API routes
- `middleware/rateLimiter.js` - Rate limiting
- `utils/security.js` - Security utilities
- `models/user.js` - Extended user model

### Frontend Files
- `components/TwoFactorSettings.js` - Main settings component
- `components/TwoFactorSetup.js` - Setup flow component
- `components/TwoFactorVerification.js` - Login verification
- `layout/SettingPopover.js` - Updated with 2FA menu item

## Next Steps

1. **Test the implementation** thoroughly
2. **Train your team** on how 2FA works
3. **Communicate to users** about the new security feature
4. **Monitor usage** and security events
5. **Consider making 2FA mandatory** for admin accounts

## Support

If you encounter any issues:
1. Check the browser console for errors
2. Review the server logs for API errors
3. Verify environment variables are set correctly
4. Test with the provided test script: `npm run test:2fa`

The 2FA implementation is now complete and ready for use! 🎉
