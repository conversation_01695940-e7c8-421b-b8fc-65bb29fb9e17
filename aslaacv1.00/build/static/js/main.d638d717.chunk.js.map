{"version": 3, "sources": ["hooks/useResponsive.js", "utils/jwt.js", "contexts/JWTContext.js", "config/apiConfig.js", "config/headerConfig.js", "config/pusherConfig.js", "config/pricingConfig.js", "components/Logo.js", "redux/slices/notification.js", "config/firebase-config.js", "layout/LogoOnlyLayout.js", "utils/axios.js", "reportWebVitals.js", "contexts/CollapseDrawerContext.js", "theme/palette.js", "utils/getFontValue.js", "theme/typography.js", "theme/breakpoints.js", "theme/index.js", "components/NotistackProvider.js", "components/MotionLazyContainer.js", "guards/GuestGuard.js", "routes/authRoutes.js", "components/LoadingScreen.js", "guards/AuthGuard.js", "routes/mainRoutes.js", "guards/AdminGuard.js", "routes/adminRoutes.js", "routes/errorRoutes.js", "routes/index.js", "components/Footer.js", "App.js", "serviceWorkerRegistration.js", "i18n.js", "index.js", "redux/slices/car.js", "redux/rootReducer.js", "redux/store.js", "hooks/useAuth.js"], "names": ["useResponsive", "query", "key", "start", "end", "theme", "useTheme", "mediaUp", "useMediaQuery", "breakpoints", "up", "mediaDown", "down", "mediaBetween", "between", "mediaOnly", "only", "setSession", "accessToken", "localStorage", "setItem", "axios", "defaults", "headers", "common", "Authorization", "concat", "removeItem", "initialState", "isAuthenticated", "isInitialized", "user", "handlers", "INITIALIZE", "state", "action", "payload", "OTPFINAL", "final", "LOGINED", "LOGOUT", "reducer", "type", "AuthContext", "createContext", "method", "login", "Promise", "resolve", "logout", "initialize", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "dispatch", "useReducer", "async", "window", "getItem", "decoded", "jwtDecode", "currentTime", "Date", "now", "exp", "isValidToken", "response", "get", "data", "err", "console", "error", "useEffect", "log", "_jsxs", "Provider", "value", "post", "phoneNumber", "pinVerify", "token", "status", "otpVerify", "otp", "callback", "otpResult", "success", "codeVerify", "pinCode", "message", "HOST_API", "HEADER", "MOBILE_HEIGHT", "MAIN_DESKTOP_HEIGHT", "PRICE_PER_MONTH", "Logo", "disabledLink", "sx", "logo", "_jsx", "Box", "width", "height", "src", "alt", "_Fragment", "RouterLink", "to", "slice", "createSlice", "name", "isLoading", "notifications", "reducers", "startLoading", "<PERSON><PERSON><PERSON><PERSON>", "setNotifications", "actions", "getNotifications", "app", "initializeApp", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "messaging", "getMessaging", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "top", "left", "lineHeight", "max<PERSON><PERSON><PERSON>", "position", "padding", "spacing", "LogoOnlyLayout", "Outlet", "axiosInstance", "create", "baseURL", "interceptors", "use", "reject", "reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB", "collapseClick", "collapseHover", "onToggleCollapse", "onHoverEnter", "onHoverLeave", "CollapseDrawerContext", "CollapseDrawerProvider", "isDesktop", "collapse", "setCollapse", "useState", "click", "hover", "isCollapse", "handleToggleCollapse", "handleHoverEnter", "handleHoverLeave", "createGradient", "color1", "color2", "PRIMARY", "lighter", "light", "main", "dark", "darker", "INFO", "SUCCESS", "WARNING", "ERROR", "GREY", "alpha", "GRADIENTS", "primary", "info", "warning", "COMMON", "black", "white", "contrastText", "secondary", "grey", "gradients", "divider", "selected", "disabled", "disabledBackground", "focus", "hoverOpacity", "disabledOpacity", "palette", "mode", "text", "background", "paper", "default", "neutral", "active", "pxToRem", "responsiveFontSizes", "sm", "md", "lg", "fontSize", "FONT_DIGITAL", "FONT_AUTOMOTIVE", "typography", "fontFamily", "fontWeightRegular", "fontWeightMedium", "fontWeightBold", "h1", "fontWeight", "letterSpacing", "h2", "h3", "h4", "h5", "h6", "subtitle1", "subtitle2", "body1", "body2", "caption", "overline", "textTransform", "button", "digitalDisplay", "automotiveData", "automotiveLabel", "technicalInfo", "car<PERSON>tatus", "values", "xs", "xl", "ThemeProvider", "themeOptions", "useMemo", "shape", "borderRadius", "createTheme", "StyledEngineProvider", "injectFirst", "MUIThemeProvider", "CssBaseline", "NotistackProvider", "SnackbarProvider", "maxSnack", "MotionLazyContainer", "motion", "div", "initial", "opacity", "animate", "exit", "<PERSON><PERSON><PERSON>", "useAuth", "Navigate", "<PERSON><PERSON>", "lazy", "VerifyCode", "ForgotPassword", "authRoutes", "path", "element", "LoadingScreen", "style", "display", "justifyContent", "alignItems", "CircularProgress", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "useLocation", "requestedLocation", "setRequestedLocation", "Home", "LandingPage", "PinCode", "TimeCommand", "HelpPage", "LogManagement", "LogLicense", "Notification", "CarLocationsWithRouter", "SimCardLogs", "GpsHistoryOnMap", "ConfigureDrivers", "LicenseProfile", "DriverProfile", "DeviceRegister", "ConfigGPS", "Order", "DeviceConfig", "MqttTestPage", "PahoMqttConfig", "mainRoutes", "index", "<PERSON><PERSON><PERSON><PERSON>", "_user$role", "role", "includes", "DeviceEdit", "UserManage", "TransactionLogs", "WithdawRequests", "Orders", "AppManagement", "adminRoutes", "NotFound", "errorRoutes", "replace", "Router", "useRoutes", "Footer", "margin", "App", "i18n", "useTranslation", "lang", "changeLanguage", "Suspense", "fallback", "isLocalhost", "Boolean", "location", "hostname", "match", "registerValidSW", "swUrl", "config", "navigator", "serviceWorker", "register", "registration", "onupdatefound", "installingWorker", "installing", "onstatechange", "controller", "onUpdate", "onSuccess", "catch", "Backend", "LanguageDetector", "initReactI18next", "init", "debug", "fallbackLng", "lng", "whitelist", "interpolation", "escapeValue", "react", "useSuspense", "ReactDOM", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReduxProvider", "store", "PersistGate", "loading", "persistor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "URL", "process", "href", "origin", "addEventListener", "fetch", "contentType", "indexOf", "ready", "unregister", "reload", "checkValidServiceWorker", "serviceWorkerRegistration", "routines", "setCarStatus", "setRoutines", "rootPersistConfig", "storage", "keyPrefix", "rootReducer", "combineReducers", "car", "carReducer", "notification", "notificationReducer", "configureStore", "persistReducer", "middleware", "getDefaultMiddleware", "serializableCheck", "immutableCheck", "persistStore", "useAppSelector", "context", "useContext", "Error"], "mappings": "kGAAA,wDAMe,SAASA,EAAcC,EAAOC,EAAKC,EAAOC,GACvD,MAAMC,EAAQC,cAERC,EAAUC,YAAcH,EAAMI,YAAYC,GAAGR,IAE7CS,EAAYH,YAAcH,EAAMI,YAAYG,KAAKV,IAEjDW,EAAeL,YAAcH,EAAMI,YAAYK,QAAQX,EAAOC,IAE9DW,EAAYP,YAAcH,EAAMI,YAAYO,KAAKd,IAEvD,MAAc,OAAVD,EACKM,EAGK,SAAVN,EACKU,EAGK,YAAVV,EACKY,EAGK,SAAVZ,EACKc,EAEF,IACT,C,0IC1BA,MA4BME,EAAcC,IACdA,GACFC,aAAaC,QAAQ,cAAeF,GACpCG,IAAMC,SAASC,QAAQC,OAAOC,cAAa,UAAAC,OAAaR,KAKxDC,aAAaQ,WAAW,sBACjBN,IAAMC,SAASC,QAAQC,OAAOC,cACvC,E,WC9BF,MAAMG,EAAe,CACjBC,iBAAiB,EACjBC,eAAe,EACfC,KAAM,MAGJC,EAAW,CACbC,WAAYA,CAACC,EAAOC,KAChB,MAAM,gBAAEN,EAAe,KAAEE,GAASI,EAAOC,QACzC,MAAO,IACAF,EACHL,kBACAC,eAAe,EACfC,OACH,EAGLM,SAAUA,CAACH,EAAOC,KACd,MAAM,MAAEG,GAAUH,EAAOC,QACzB,MAAO,IACAF,EACHL,iBAAiB,EACjBC,eAAe,EACfQ,QACAP,KAAM,KACT,EAELQ,QAASA,CAACL,EAAOC,KACb,MAAM,KAAEJ,GAASI,EAAOC,QAExB,MAAO,IACAF,EACHL,iBAAiB,EACjBE,OACH,EAELS,OAASN,IAAK,IACPA,EACHL,iBAAiB,EACjBS,MAAO,KAEPP,KAAM,QAMRU,EAAUA,CAACP,EAAOC,IAAYH,EAASG,EAAOO,MAAQV,EAASG,EAAOO,MAAMR,EAAOC,GAAUD,EAE7FS,EAAcC,wBAAc,IAC3BhB,EACHiB,OAAQ,MACRC,MAAOA,IAAMC,QAAQC,UACrBC,OAAQA,IAAMF,QAAQC,UACtBE,WAAYA,IAAMH,QAAQC,YAS9B,SAASG,EAAYC,GAAgB,IAAf,SAAEC,GAAUD,EAC9B,MAAOlB,EAAOoB,GAAYC,qBAAWd,EAASb,GACxCsB,EAAaM,UACf,IACI,MAAMtC,EAAcuC,OAAOtC,aAAauC,QAAQ,eAChD,GAAIxC,GD5EMA,KACpB,IAAKA,EACH,OAAO,EAKT,MAAMyC,EAAUC,YAAU1C,GACpB2C,EAAcC,KAAKC,MAAQ,IAEjC,OAAOJ,EAAQK,IAAMH,CAAW,ECkEHI,CAAa/C,GAAc,CAC1CD,EAAWC,GACX,MAAMgD,QAAiB7C,IAAM8C,IAAI,yBAC3B,KAAEpC,GAASmC,EAASE,KAC1Bd,EAAS,CACLZ,KAAM,aACNN,QAAS,CACLP,iBAAiB,EACjBE,SAGZ,MACIuB,EAAS,CACLZ,KAAM,aACNN,QAAS,CACLP,iBAAiB,EACjBE,KAAM,OAatB,CATE,MAAOsC,GACLC,QAAQC,MAAMF,GACdf,EAAS,CACLZ,KAAM,aACNN,QAAS,CACLP,iBAAiB,EACjBE,KAAM,OAGlB,GAGJyC,qBAAU,KACNF,QAAQG,IAAI,wDACZvB,GAAY,GACb,IAsHH,OAAQwB,eAAC/B,EAAYgC,SAAQ,CAACC,MAC1B,IACO1C,EACHW,OAAQ,MACRC,MA3DMU,UAEV,MAAMU,QAAiB7C,IAAMwD,KAAK,kBAAmB,CACjDC,gBAKJ,GAFkBZ,EAASE,KAAKW,UAG5B,MAAO,UAEX,MAAM,MAAEC,EAAK,KAAEjD,GAASmC,EAASE,KAGjC,GAAKY,EAWD,MAAoB,aAAhBjD,EAAKkD,OACE,YAEXhE,EAAW+D,GACX1B,EAAS,CACLZ,KAAM,UACNN,QAAS,CACLL,UAGD,YApBP,IAEI,OADA0B,OAAOqB,YAAcA,EACd,KAIX,CAHE,MAAOT,GAEL,OADAC,QAAQG,IAAIJ,GACL,KACX,CAgBJ,EAuBIpB,OAlBOO,UACX,IACIvC,EAAW,MAEXqC,EAAS,CAAEZ,KAAM,UAKrB,CAHE,MAAO2B,GAELC,QAAQG,IAAIJ,EAChB,GAUIa,UA1HU1B,MAAO2B,EAAKC,KAC1B,IACI,MAAMN,EAAcrB,OAAOqB,YACrBO,QAAkBhE,IAAMwD,KAAK,sBAAuB,CACtDC,cACAK,SAEE,QAAEG,GAAYD,EAAUjB,KAC9B,GAAIkB,EAAS,CAET,MAAMpB,QAAiB7C,IAAMwD,KAAK,qBAAsB,CACpDC,gBAEJ,GAAwB,MAApBZ,EAASe,OAAgB,CACzB,MAAM,MAAED,EAAK,KAAEjD,GAASmC,EAASE,KACjCnD,EAAW+D,GACX1B,EAAS,CACLZ,KAAM,UACNN,QAAS,CACLL,UAGRqD,EAAS,CAAEE,SAAS,GACxB,CACJ,MACIF,EAAS,CAAEE,SAAS,EAAOjB,IAAK,oBAKxC,CAFE,MAAOA,GACLe,EAAS,CAAEE,SAAS,EAAOjB,IAAK,oBACpC,GA6FIkB,WA1FW/B,MAAOsB,EAAaU,KACnC,MAAMtB,QAAiB7C,IAAMwD,KAAK,oBAAqB,CAAEC,cAAaU,aAEhE,MAAER,EAAK,KAAEjD,GAASmC,EAASE,KAGjC,IAAKY,EACD,IAEI,OADAvB,OAAOqB,YAAcA,EACd,CAAEQ,SAAS,EAAOG,QAAS,8BAGtC,CAFE,MAAOpB,GACL,MAAO,CAAEiB,SAAS,EAAOG,QAAS,8BACtC,CAEJ,MAAoB,aAAhB1D,EAAKkD,OACE,CAAEK,SAAS,EAAOG,QAAS,gEAEtCxE,EAAW+D,GACX1B,EAAS,CACLZ,KAAM,UACNN,QAAS,CACLL,UAGD,CAAEuD,SAAS,EAAMG,QAAS,6BAA6B,EAmE1DvC,cAEPG,SAAA,CAAE,IAAEA,EAAS,MAClB,C,yICvPQ,MAAMqC,EAAW,wBCAZC,EAAS,CAClBC,cAAe,GACfC,oBAAqB,I,OCFlB,MCAMC,EAAkB,G,mCCC/B,8DAae,SAASC,EAAI3C,GAAgC,IAA/B,aAAE4C,GAAe,EAAK,GAAEC,GAAI7C,EAErD,MAAM8C,EAAQC,cAACC,IAAG,CAACH,GAAM,CAAEI,MAAO,GAAIC,OAAQ,MAAOL,GAAM5C,SACxD8C,cAAA,OAAKE,MAAO,OAAOC,OAAO,OAAOC,IAAM,iBAAiBC,IAAM,WAIjE,OAAIR,EAAuBtB,eAAA+B,WAAA,CAAApD,SAAA,CAAE,IAAG6C,EAAM,OAE/BxB,eAACgC,IAAU,CAACC,GAAK,QAAOtD,SAAA,CAAC,IAAG6C,EAAM,MACzC,C,mCCxBJ,+DAMA,MAKMU,EAAQC,YAAY,CACxBC,KAAM,eACNlF,aAPmB,CACnBmF,WAAW,EACXxC,MAAO,KACPyC,cAAe,IAKfC,SAAU,CACRC,aAAahF,GACXA,EAAM6E,WAAY,CACpB,EACAI,SAASjF,EAAOC,GACdD,EAAMqC,MAAQpC,EAAOC,QACrBF,EAAM6E,WAAY,CACpB,EACAK,iBAAiBlF,EAAOC,GACtBD,EAAM6E,WAAY,EAClB7E,EAAM8E,cAAgB7E,EAAOC,OAC/B,KAIWwE,MAAa,QACrB,MAAM,iBAAEQ,GAAqBR,EAAMS,QAEnC,SAASC,IAEd,OAAO9D,UACLF,YAASsD,EAAMS,QAAQH,gBACvB,IAEE,MAAMhD,QAAiB7C,IAAM8C,IAAI,uBACjCb,YAASsD,EAAMS,QAAQD,iBAAiBlD,EAASE,KAAKA,MAGxD,CAFE,MAAOG,GACPjB,YAASsD,EAAMS,QAAQF,SAAS5C,GAClC,EAEJ,C,mCC5CA,4FAGO,MAUDgD,EAAMC,YAVkB,CAC5BC,OAAQ,0CACRC,WAAY,gCACZC,UAAW,gBACXC,cAAe,4BACfC,kBAAmB,eACnBC,MAAO,8CAOIC,EAAYC,YAAaT,E,mCChBtC,qEASA,MAAMU,EAAcC,YAAO,SAAPA,EAAiB9E,IAAA,IAAC,MAAE/C,GAAO+C,EAAA,MAAM,CACnD+E,IAAK,EACLC,KAAM,EACNC,WAAY,EACZC,SAAU,QACVC,SAAU,WACVC,QAASnI,EAAMoI,QAAQ,EAAG,EAAG,GAC7B,CAACpI,EAAMI,YAAYC,GAAG,OAAQ,CAC5B8H,QAASnI,EAAMoI,QAAQ,EAAG,EAAG,IAEhC,IAIc,SAASC,IACtB,OACEhE,eAAA+B,WAAA,CAAApD,SAAA,CACE8C,cAAC8B,EAAW,CAAA5E,SACV8C,cAACJ,IAAI,CAACC,cAAY,MAEpBG,cAACwC,IAAM,MAGb,C,oVChCA,+BAMA,MAAMC,EAAgBvH,IAAMwH,OAAO,CACjCC,QAASpD,MAGXkD,EAAcG,aAAa7E,SAAS8E,KACjC9E,GAAaA,IACbK,GAAUxB,QAAQkG,OAAQ1E,EAAML,UAAYK,EAAML,SAASE,MAAS,0BAGxDwE,K,6KCHAM,MAZUC,IACnBA,GAAeA,aAAuBC,UACxC,gCAAqBC,MAAKjG,IAAkD,IAAjD,OAAEkG,EAAM,OAAEC,EAAM,OAAEC,EAAM,OAAEC,EAAM,QAAEC,GAAStG,EACpEkG,EAAOH,GACPI,EAAOJ,GACPK,EAAOL,GACPM,EAAON,GACPO,EAAQP,EAAY,GAExB,E,wDCFF,MAAMvH,EAAe,CACnB+H,eAAe,EACfC,eAAe,EACfC,iBAAkBA,OAClBC,aAAcA,OACdC,aAAcA,QAGVC,EAAwBpH,wBAAchB,GAQ5C,SAASqI,EAAsB7G,GAAgB,IAAf,SAAEC,GAAUD,EAC1C,MAAM8G,EAAYlK,YAAc,KAAM,OAE/BmK,EAAUC,GAAeC,mBAAS,CACvCC,OAAO,EACPC,OAAO,IAGT/F,qBAAU,KACH0F,GACHE,EAAY,CACVE,OAAO,EACPC,OAAO,GAEX,GACC,CAACL,IAgBJ,OACE/D,cAAC6D,EAAsBrF,SAAQ,CAC7BC,MAAO,CACL4F,WAAYL,EAASG,QAAUH,EAASI,MACxCZ,cAAeQ,EAASG,MACxBV,cAAeO,EAASI,MACxBV,iBApBuBY,KAC3BL,EAAY,IAAKD,EAAUG,OAAQH,EAASG,OAAQ,EAoBhDR,aAjBmBY,KACnBP,EAASG,OACXF,EAAY,IAAKD,EAAUI,OAAO,GACpC,EAeIR,aAZmBY,KACvBP,EAAY,IAAKD,EAAUI,OAAO,GAAQ,GAYtClH,SAEDA,GAGP,C,0DChEA,SAASuH,EAAeC,EAAQC,GAC9B,MAAM,8BAANpJ,OAAqCmJ,EAAM,MAAAnJ,OAAKoJ,EAAM,IACxD,CAGA,MAAMC,EAAU,CACdC,QAAS,UACTC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WASJC,EAAO,CACXL,QAAS,UACTC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WAEJE,EAAU,CACdN,QAAS,UACTC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WAEJG,EAAU,CACdP,QAAS,UACTC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WAEJI,EAAQ,CACZR,QAAS,UACTC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WAGJK,EAAO,CACX,EAAG,UACH,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,KAAOC,YAAM,UAAW,KACxB,MAAQA,YAAM,UAAW,KACzB,MAAQA,YAAM,UAAW,KACzB,MAAQA,YAAM,UAAW,KACzB,MAAQA,YAAM,UAAW,KACzB,MAAQA,YAAM,UAAW,KACzB,MAAQA,YAAM,UAAW,KACzB,MAAQA,YAAM,UAAW,KAGrBC,EAAY,CAChBC,QAAShB,EAAeG,EAAQE,MAAOF,EAAQG,MAC/CW,KAAMjB,EAAeS,EAAKJ,MAAOI,EAAKH,MACtC5F,QAASsF,EAAeU,EAAQL,MAAOK,EAAQJ,MAC/CY,QAASlB,EAAeW,EAAQN,MAAOM,EAAQL,MAC/C3G,MAAOqG,EAAeY,EAAMP,MAAOO,EAAMN,OAGrCa,EAAS,CACbvK,OAAQ,CAAEwK,MAAO,OAAQC,MAAO,WAChCL,QAAS,IAAKb,EAASmB,aAAc,WACrCC,UAAW,CAnEXnB,QAAS,UACTC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,UA+DmBc,aAAc,WACzCL,KAAM,IAAKR,EAAMa,aAAc,WAC/B5G,QAAS,IAAKgG,EAASY,aAAcT,EAAK,MAC1CK,QAAS,IAAKP,EAASW,aAAcT,EAAK,MAC1ClH,MAAO,IAAKiH,EAAOU,aAAc,WACjCE,KAAMX,EACNY,UAAWV,EACXW,QAASb,EAAK,OACdtJ,OAAQ,CACNoI,MAAOkB,EAAK,KACZc,SAAUd,EAAK,KACfe,SAAUf,EAAK,OACfgB,mBAAoBhB,EAAK,OACzBiB,MAAOjB,EAAK,KACZkB,aAAc,GACdC,gBAAiB,MAkBNC,MAdC,CACd1B,KAAM,IACDY,EACHe,KAAM,OACNC,KAAM,CAAEnB,QAAS,UAAWO,UAAW,UAAWK,SAAU,WAC5DQ,WAAY,CACVC,MAAO,UACPC,QAAS,UACTC,QAAS,WAEXhL,OAAQ,CAAEiL,OAAQ,aAAcrB,EAAO5J,U,OC3EpC,SAASkL,EAAQzI,GACtB,MAAM,GAANlD,OAAUkD,EAAQ,GAAE,MACtB,CAEO,SAAS0I,EAAmBlK,GAAkB,IAAjB,GAAEmK,EAAE,GAAEC,EAAE,GAAEC,GAAIrK,EAChD,MAAO,CACL,2BAA4B,CAC1BsK,SAAUL,EAAQE,IAEpB,2BAA4B,CAC1BG,SAAUL,EAAQG,IAEpB,4BAA6B,CAC3BE,SAAUL,EAAQI,IAGxB,CClDA,MAIME,EAAe,sBACfC,EAAkB,yBAqHTC,MAlHI,CACjBC,WATmB,0BAUnBC,kBAAmB,IACnBC,iBAAkB,IAClBC,eAAgB,IAChBC,GAAI,CACFC,WAAY,IACZ9F,WAAY,KACZqF,SAAUL,EAAQ,IAClBe,cAAe,KACZd,EAAoB,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAE/CY,GAAI,CACFF,WAAY,IACZ9F,WAAY,GAAK,GACjBqF,SAAUL,EAAQ,OACfC,EAAoB,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAE/Ca,GAAI,CACFH,WAAY,IACZ9F,WAAY,IACZqF,SAAUL,EAAQ,OACfC,EAAoB,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAE/Cc,GAAI,CACFJ,WAAY,IACZ9F,WAAY,IACZqF,SAAUL,EAAQ,OACfC,EAAoB,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAE/Ce,GAAI,CACFL,WAAY,IACZ9F,WAAY,IACZqF,SAAUL,EAAQ,OACfC,EAAoB,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAE/CgB,GAAI,CACFN,WAAY,IACZ9F,WAAY,GAAK,GACjBqF,SAAUL,EAAQ,OACfC,EAAoB,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAE/CiB,UAAW,CACTP,WAAY,IACZ9F,WAAY,IACZqF,SAAUL,EAAQ,KAEpBsB,UAAW,CACTR,WAAY,IACZ9F,WAAY,GAAK,GACjBqF,SAAUL,EAAQ,KAEpBuB,MAAO,CACLvG,WAAY,IACZqF,SAAUL,EAAQ,KAEpBwB,MAAO,CACLxG,WAAY,GAAK,GACjBqF,SAAUL,EAAQ,KAEpByB,QAAS,CACPzG,WAAY,IACZqF,SAAUL,EAAQ,KAEpB0B,SAAU,CACRZ,WAAY,IACZ9F,WAAY,IACZqF,SAAUL,EAAQ,IAClB2B,cAAe,aAEjBC,OAAQ,CACNd,WAAY,IACZ9F,WAAY,GAAK,GACjBqF,SAAUL,EAAQ,IAClB2B,cAAe,cAGjBE,eAAgB,CACdpB,WAAYH,EACZQ,WAAY,IACZT,SAAUL,EAAQ,IAClBe,cAAe,QACf/F,WAAY,KAEd8G,eAAgB,CACdrB,WAAYF,EACZO,WAAY,IACZT,SAAUL,EAAQ,IAClBe,cAAe,SACf/F,WAAY,KAEd+G,gBAAiB,CACftB,WAAYF,EACZO,WAAY,IACZT,SAAUL,EAAQ,IAClBe,cAAe,SACf/F,WAAY,KAEdgH,cAAe,CACbvB,WAAYF,EACZO,WAAY,IACZT,SAAUL,EAAQ,IAClBe,cAAe,SACf/F,WAAY,KAEdiH,UAAW,CACTxB,WAAYH,EACZQ,WAAY,IACZT,SAAUL,EAAQ,IAClBe,cAAe,SACf/F,WAAY,MC9GD5H,MAVK,CAClB8O,OAAQ,CACNC,GAAI,IACJjC,GAAI,IACJC,GAAI,IACJC,GAAI,KACJgC,GAAI,OCSO,SAASC,EAAatM,GAAgB,IAAf,SAAEC,GAAUD,EAI5C,MAAMuM,EAAeC,mBACnB,MACE/C,QAASA,EAAQ1B,KACjB0C,aACApN,cACAoP,MAAO,CAAEC,aAAc,MAGzB,IAGAzP,EAAQ0P,YAAYJ,GAE1B,OACExJ,cAAC6J,IAAoB,CAACC,aAAW,EAAA5M,SAC/BqB,eAACwL,IAAgB,CAAC7P,MAAOA,EAAMgD,SAAA,CAC7B8C,cAACgK,IAAW,IACX9M,MAIT,C,aC/Be+M,MARWhN,IAAmB,IAAlB,SAAEC,GAAUD,EACrC,OACE+C,cAACkK,IAAgB,CAACC,SAAU,EAAEjN,SAC3BA,GACgB,E,SCQRkN,MAZanN,IAAmB,IAAlB,SAAEC,GAAUD,EACvC,OACE+C,cAACqK,IAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,KAAM,CAAEF,QAAS,GAAItN,SAEpBA,GACU,E,eCAF,SAASyN,EAAU1N,GAAgB,IAAf,SAAEC,GAAUD,EAC7C,MAAM,gBAAEvB,GAAoBkP,cAE5B,OAAIlP,EACKsE,cAAC6K,IAAQ,CAACrK,GAAG,UAGfR,cAAAM,WAAA,CAAApD,SAAGA,GACZ,CChBA,MAAM4N,EAAQC,gBAAK,IAAM,6EACnBC,EAAaD,gBAAK,IAAM,8EACxBE,EAAiBF,gBAAK,IAAM,kCAkBnBG,MAhBI,CACjBC,KAAM,OACNjO,SAAU,CACR,CACEiO,KAAM,QACNC,QACEpL,cAAC2K,EAAU,CAAAzN,SACT8C,cAAC8K,EAAK,OAIZ,CAAEK,KAAM,SAAUC,QAAU7M,eAACoM,EAAU,CAAAzN,SAAA,CAAC,IAAC8C,cAACgL,EAAU,QACpD,CAAEG,KAAM,kBAAmBC,QAAU7M,eAACoM,EAAU,CAAAzN,SAAA,CAAC,IAAC8C,cAACiL,EAAc,U,SCRtDI,MAROA,IAElBrL,cAAA,OAAKsL,MAAO,CAAEC,QAAS,OAAQC,eAAgB,SAAUC,WAAY,SAAUtL,OAAQ,SAAUjD,SAC/F8C,cAAC0L,IAAgB,MCSR,SAASC,GAAS1O,GAAgB,IAAf,SAAEC,GAAUD,EAC5C,MAAM,gBAAEvB,EAAe,cAAEC,GAAkBiP,eACrC,SAAEgB,GAAaC,eACdC,EAAmBC,GAAwB7H,mBAAS,MAE3D,OAAKvI,EAGAD,EAODoQ,GAAqBF,IAAaE,GACpCC,EAAqB,MACd/L,cAAC6K,IAAQ,CAACrK,GAAIsL,KAGhB9L,cAAAM,WAAA,CAAApD,SAAGA,KAXJ0O,IAAaE,GACfC,EAAqBH,GAEhB5L,cAAC6K,IAAQ,CAACrK,GAAI,iBANdR,cAACqL,EAAa,GAezB,CChCA,MAAMW,GAAOjB,gBAAK,IAAM,6EAClBkB,GAAclB,gBAAK,IAAM,6EACzBmB,GAAUnB,gBAAK,IAAM,sCACrBoB,GAAcpB,gBAAK,IAAM,6EACzBqB,GAAWrB,gBAAK,IAAM,8EACtBsB,GAAgBtB,gBAAK,IAAM,6EAC3BuB,GAAavB,gBAAK,IAAM,6EACxBwB,GAAexB,gBAAK,IAAM,6EAC1ByB,GAAyBzB,gBAAK,IAAM,6EACpC0B,GAAc1B,gBAAK,IAAM,6EACzB2B,GAAkB3B,gBAAK,IAAM,6EAC7B4B,GAAmB5B,gBAAK,IAAM,6EAC9B6B,GAAiB7B,gBAAK,IAAM,6EAC5B8B,GAAgB9B,gBAAK,IAAM,6EAC3B+B,GAAiB/B,gBAAK,IAAM,4EAC5BgC,GAAYhC,gBAAK,IAAM,6EACvBiC,GAAQjC,gBAAK,IAAM,6EACnBkC,GAAelC,gBAAK,IAAM,6EAC1BmC,GAAenC,gBAAK,IAAM,6EAC1BoC,GAAiBpC,gBAAK,IAAM,6EA8BnBqC,OA5BI,CACjBjC,KAAM,GACNjO,SAAU,CACR,CAAEkO,QAAUpL,cAACiM,GAAW,IAAMoB,OAAO,GACrC,CAAElC,KAAM,OAAQC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACgM,GAAI,IAAG,QAC9C,CAAEb,KAAM,OAAQC,QAASpL,cAACoM,GAAQ,KAClC,CAAEjB,KAAM,kBAAmBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAAC8M,GAAc,IAAG,QACnE,CAAE3B,KAAM,kBAAmBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAAC4M,GAAc,IAAG,QACnE,CAAEzB,KAAM,WAAYC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACkM,GAAO,IAAG,QACrD,CAAEf,KAAM,eAAgBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACmM,GAAW,IAAG,QAC7D,CAAEhB,KAAM,iBAAkBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACqM,GAAa,IAAG,QACjE,CAAElB,KAAM,UAAWC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACwM,GAAsB,IAAG,QACnE,CAAErB,KAAM,UAAWC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACyM,GAAW,IAAG,QACxD,CAAEtB,KAAM,UAAWC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAAC0M,GAAe,IAAG,QAC5D,CAAEvB,KAAM,mBAAoBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAAC2M,GAAgB,IAAG,QACtE,CAAExB,KAAM,oBAAqBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAAC+M,GAAS,IAAG,QAChE,CAAE5B,KAAM,cAAeC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACsM,GAAU,IAAG,QAC3D,CAAEnB,KAAM,QAASC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACgN,GAAK,IAAG,QAChD,CAAE7B,KAAM,iBAAkBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAAC6M,GAAa,IAAG,QACjE,CAAE1B,KAAM,eAAgBC,QAAU7M,eAACoN,GAAS,CAAAzO,SAAA,CAAC,IAAC8C,cAACuM,GAAY,IAAG,QAE9D,CAAEpB,KAAM,oBAAqBC,QAASpL,cAACiN,GAAY,KACnD,CAAE9B,KAAM,YAAaC,QAASpL,cAACkN,GAAY,KAC3C,CAAE/B,KAAM,gBAAiBC,QAASpL,cAACkN,GAAY,KAC/C,CAAE/B,KAAM,YAAaC,QAASpL,cAACmN,GAAc,OCpClC,SAASG,GAAUrQ,GAAgB,IAADsQ,EAAA,IAAd,SAAErQ,GAAUD,EAC7C,MAAM,gBAAEvB,EAAe,cAAEC,EAAa,KAAEC,GAASgP,cAEjD,OAAKjP,EAIAD,EAII,OAAJE,QAAI,IAAJA,GAAU,QAAN2R,EAAJ3R,EAAM4R,YAAI,IAAAD,GAAVA,EAAYE,SAAS,SAInBzN,cAAAM,WAAA,CAAApD,SAAGA,IAHD8C,cAAC6K,IAAQ,CAACrK,GAAG,MAJbR,cAAC6K,IAAQ,CAACrK,GAAG,gBAJbR,cAACqL,EAAa,GAYzB,CCzBA,MAAMqC,GAAa3C,gBAAK,IAAM,6EACxB4C,GAAa5C,gBAAK,IAAM,6EACxB6C,GAAkB7C,gBAAK,IAAM,6EAC7B8C,GAAkB9C,gBAAK,IAAM,6EAC7B+C,GAAS/C,gBAAK,IAAM,6EACpBgD,GAAgBhD,gBAAK,IAAM,6EAclBiD,OAZK,CAClB7C,KAAM,QACNjO,SAAU,CACR,CAAEiO,KAAM,aAAcC,QAASpL,cAAC2L,GAAS,CAAAzO,SAAC8C,cAACsN,GAAU,CAAApQ,SAAC8C,cAAC0N,GAAU,SACjE,CAAEvC,KAAM,cAAeC,QAASpL,cAAC2L,GAAS,CAAAzO,SAAC8C,cAACsN,GAAU,CAAApQ,SAAC8C,cAAC2N,GAAU,SAClE,CAAExC,KAAM,SAAUC,QAASpL,cAAC2L,GAAS,CAAAzO,SAAC8C,cAACsN,GAAU,CAAApQ,SAAC8C,cAAC8N,GAAM,SACzD,CAAE3C,KAAM,eAAgBC,QAASpL,cAAC2L,GAAS,CAAAzO,SAAC8C,cAACsN,GAAU,CAAApQ,SAAC8C,cAAC4N,GAAe,SACxE,CAAEzC,KAAM,oBAAqBC,QAASpL,cAAC2L,GAAS,CAAAzO,SAAC8C,cAACsN,GAAU,CAAApQ,SAAC8C,cAAC6N,GAAe,SAC7E,CAAE1C,KAAM,iBAAkBC,QAASpL,cAAC2L,GAAS,CAAAzO,SAAC8C,cAACsN,GAAU,CAAApQ,SAAC8C,cAAC+N,GAAa,W,UCf5E,MAAME,GAAWlD,gBAAK,IAAM,sEAWbmD,OATK,CAClB/C,KAAM,IACNC,QAASpL,cAACuC,KAAc,IACxBrF,SAAU,CACR,CAAEiO,KAAM,MAAOC,QAASpL,cAACiO,GAAQ,KACjC,CAAE9C,KAAM,IAAKC,QAASpL,cAAC6K,IAAQ,CAACrK,GAAG,OAAO2N,SAAO,OCLtC,SAASC,KACtB,OAAOC,YAAU,CAACnD,EAAYkC,GAAYY,GAAaE,IACzD,CCGeI,OATf,WACE,OACEtO,cAAA,UAAQsL,MAAO,CAAEC,QAAS,OAAQC,eAAgB,SAAUC,WAAY,SAAUpJ,QAAS,QAASnF,SAClG8C,cAAA,KAAGsL,MAAO,CAAEiD,OAAQ,UAAWrR,SAAC,4CAItC,ECKe,SAASsR,KACpB,MAAM,KAAEC,GAASC,cACXC,EAAO3T,aAAauC,QAAQ,aAAe,KAMjD,OAJAc,qBAAU,KACRoQ,EAAKG,eAAeD,EAAK,GACxB,CAACA,EAAMF,IAGNlQ,eAACgL,EAAa,CAAArM,SAAA,CAGV8C,cAACiK,EAAiB,CAAA/M,SACd8C,cAACoK,EAAmB,CAAAlN,SAKhB8C,cAAC6O,WAAQ,CAACC,SAAU9O,cAACqL,EAAa,IAAInO,SAClC8C,cAACoO,GAAM,UAMnBpO,cAACsO,GAAM,MAGnB,CC9BA,MAAMS,GAAcC,QACW,cAA7B1R,OAAO2R,SAASC,UAEe,UAA7B5R,OAAO2R,SAASC,UAEhB5R,OAAO2R,SAASC,SAASC,MAAM,2DAqCnC,SAASC,GAAgBC,EAAOC,GAC9BC,UAAUC,cACPC,SAASJ,GACTnM,MAAMwM,IACLA,EAAaC,cAAgB,KAC3B,MAAMC,EAAmBF,EAAaG,WACd,MAApBD,IAGJA,EAAiBE,cAAgB,KACA,cAA3BF,EAAiB7T,QACfwT,UAAUC,cAAcO,YAI1B5R,QAAQG,IACN,+GAKEgR,GAAUA,EAAOU,UACnBV,EAAOU,SAASN,KAMlBvR,QAAQG,IAAI,sCAGRgR,GAAUA,EAAOW,WACnBX,EAAOW,UAAUP,IAGvB,EACD,CACF,IAEFQ,OAAO9R,IACND,QAAQC,MAAM,4CAA6CA,EAAM,GAEvE,C,4CCzFAqQ,KAGK5L,IAAIsN,MAGJtN,IAAIuN,MAEJvN,IAAIwN,MAGJC,KAAK,CACFC,OAAO,EACPC,YAAa,KACbC,IAAK,KACLC,UAjBmB,CAAC,KAAM,KAAM,MAkBhCC,cAAe,CACXC,aAAa,GAGjBC,MAAO,CACHC,aAAa,KAIVrC,GAAI,ECNnBsC,IAASC,OACPhR,cAAChD,IAAY,CAAAE,SACX8C,cAACiR,IAAc,CAAA/T,SACb8C,cAACkR,IAAa,CAACC,MAAOA,IAAMjU,SAC1B8C,cAACoR,cAAW,CAACC,QAAS,KAAMC,UAAWA,IAAUpU,SAC/C8C,cAAC8D,EAAsB,CAAA5G,SACrB8C,cAACuR,IAAa,CAAArU,SACZ8C,cAACwO,GAAG,gBAOhBgD,SAASC,eAAe,SFpBnB,SAAkBnC,GACvB,GAA6C,kBAAmBC,UAAW,CAGzE,GADkB,IAAImC,IAAIC,GAAwBrU,OAAO2R,SAAS2C,MACpDC,SAAWvU,OAAO2R,SAAS4C,OAIvC,OAGFvU,OAAOwU,iBAAiB,QAAQ,KAC9B,MAAMzC,EAAK,GAAA9T,OAAMoW,GAAsB,sBAEnC5C,KAgEV,SAAiCM,EAAOC,GAEtCyC,MAAM1C,EAAO,CACXjU,QAAS,CAAE,iBAAkB,YAE5B8H,MAAMnF,IAEL,MAAMiU,EAAcjU,EAAS3C,QAAQ4C,IAAI,gBACjB,MAApBD,EAASe,QAAkC,MAAfkT,IAA8D,IAAvCA,EAAYC,QAAQ,cAEzE1C,UAAUC,cAAc0C,MAAMhP,MAAMwM,IAClCA,EAAayC,aAAajP,MAAK,KAC7B5F,OAAO2R,SAASmD,QAAQ,GACxB,IAIJhD,GAAgBC,EAAOC,EACzB,IAEDY,OAAM,KACL/R,QAAQG,IAAI,gEAAgE,GAElF,CArFQ+T,CAAwBhD,EAAOC,GAI/BC,UAAUC,cAAc0C,MAAMhP,MAAK,KACjC/E,QAAQG,IACN,0GAED,KAIH8Q,GAAgBC,EAAOC,EACzB,GAEJ,CACF,CETAgD,GAKAvP,G,iMC7CA,MAMMtC,EAAQC,YAAY,CACxBC,KAAM,MACNlF,aARmB,CACnBmF,WAAW,EACXxC,MAAO,KACPU,OAAQ,KACRyT,SAAS,MAKTzR,SAAU,CACRC,aAAahF,GACXA,EAAM6E,WAAY,CACpB,EACAI,SAASjF,EAAOC,GACdD,EAAMqC,MAAQpC,EAAOC,QACrBF,EAAM6E,WAAY,CACpB,EACA4R,aAAazW,EAAOC,GAClBD,EAAM6E,WAAY,EAClB7E,EAAM+C,OAAS9C,EAAOC,OACxB,EACAwW,YAAY1W,EAAMC,GAChBD,EAAMwW,SAAWvW,EAAOC,OAC1B,KAIWwE,QAAa,QACrB,MAAM,aACX+R,EAAY,YACZC,GACEhS,EAAMS,Q,aC3BV,MAAMwR,EAAoB,CACxB3Y,IAAK,OACL4Y,YACAC,UAAW,SACXlC,UAAW,CAAC,QAERmC,EAAcC,YAAgB,CAClCC,IAAKC,EACLC,aAAaC,MCRT/B,EAAQgC,YAAe,CAC3B7W,QAAS8W,YAAeV,EAAmBG,GAC3CQ,WAAaC,GACXA,EAAqB,CACnBC,mBAAmB,EACnBC,gBAAgB,MAIhBlC,EAAYmC,YAAatC,IAEzB,SAAEhU,GAAagU,EAEDuC,G,kCCpBpB,oBAiBe9I,IARCA,KACd,MAAM+I,EAAUC,qBAAWpX,KAE3B,IAAKmX,EAAS,MAAM,IAAIE,MAAM,gDAE9B,OAAOF,CAAO,C", "file": "static/js/main.d638d717.chunk.js", "sourcesContent": ["// @mui\nimport { useTheme } from '@mui/material/styles';\nimport useMediaQuery from '@mui/material/useMediaQuery';\n\n// ----------------------------------------------------------------------\n\nexport default function useResponsive(query, key, start, end) {\n  const theme = useTheme();\n\n  const mediaUp = useMediaQuery(theme.breakpoints.up(key));\n\n  const mediaDown = useMediaQuery(theme.breakpoints.down(key));\n\n  const mediaBetween = useMediaQuery(theme.breakpoints.between(start, end));\n\n  const mediaOnly = useMediaQuery(theme.breakpoints.only(key));\n\n  if (query === 'up') {\n    return mediaUp;\n  }\n\n  if (query === 'down') {\n    return mediaDown;\n  }\n\n  if (query === 'between') {\n    return mediaBetween;\n  }\n\n  if (query === 'only') {\n    return mediaOnly;\n  }\n  return null;\n}\n", "import jwtDecode from 'jwt-decode';\nimport { verify, sign } from 'jsonwebtoken';\n//\nimport axios from './axios';\n\n// ----------------------------------------------------------------------\n\nconst isValidToken = (accessToken) => {\n  if (!accessToken) {\n    return false;\n  }\n\n  // ----------------------------------------------------------------------\n\n  const decoded = jwtDecode(accessToken);\n  const currentTime = Date.now() / 1000;\n\n  return decoded.exp > currentTime;\n};\n\n//  const handleTokenExpired = (exp) => {\n//   let expiredTimer;\n\n//   window.clearTimeout(expiredTimer);\n//   const currentTime = Date.now();\n//   const timeLeft = exp * 1000 - currentTime;\n//   console.log(timeLeft);\n//   expiredTimer = window.setTimeout(() => {\n//     console.log('expired');\n//     // You can do what ever you want here, like show a notification\n//   }, timeLeft);\n// };\n\n// ----------------------------------------------------------------------\n\nconst setSession = (accessToken) => {\n  if (accessToken) {\n    localStorage.setItem('accessToken', accessToken);\n    axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;\n    // This function below will handle when token is expired\n    // const { exp } = jwtDecode(accessToken);\n    // handleTokenExpired(exp);\n  } else {\n    localStorage.removeItem('accessToken');\n    delete axios.defaults.headers.common.Authorization;\n  }\n};\n\nexport { isValidToken, setSession, verify, sign };\n", "import { createContext, useEffect, useReducer } from 'react';\nimport PropTypes from 'prop-types';\n\n// utils\nimport axios from '../utils/axios';\nimport { isValidToken, setSession } from '../utils/jwt';\n\n\n// const firebaseApp = initializeApp(FIREBASE_API);\n\n// const AUTH = getAuth(firebaseApp);\n\n// AUTH.settings.appVerificationDisabledForTesting  = true; \n// ----------------------------------------------------------------------\n\nconst initialState = {\n    isAuthenticated: false,\n    isInitialized: false,\n    user: null,\n};\n\nconst handlers = {\n    INITIALIZE: (state, action) => {\n        const { isAuthenticated, user } = action.payload;\n        return {\n            ...state,\n            isAuthenticated,\n            isInitialized: true,\n            user,\n        };\n    },\n\n    OTPFINAL: (state, action) => {\n        const { final } = action.payload;\n        return {\n            ...state,\n            isAuthenticated: false,\n            isInitialized: true,\n            final,\n            user: null,\n        }\n    },\n    LOGINED: (state, action) => {\n        const { user } = action.payload;\n\n        return {\n            ...state,\n            isAuthenticated: true,\n            user,\n        };\n    },\n    LOGOUT: (state) => ({\n        ...state,\n        isAuthenticated: false,\n        final: null,\n\n        user: null,\n    }),\n\n\n};\n\nconst reducer = (state, action) => (handlers[action.type] ? handlers[action.type](state, action) : state);\n\nconst AuthContext = createContext({\n    ...initialState,\n    method: 'jwt',\n    login: () => Promise.resolve(),\n    logout: () => Promise.resolve(),\n    initialize: () => Promise.resolve(),\n});\n\n// ----------------------------------------------------------------------\n\nAuthProvider.propTypes = {\n    children: PropTypes.node,\n};\n\nfunction AuthProvider({ children }) {\n    const [state, dispatch] = useReducer(reducer, initialState);\n    const initialize = async () => {\n        try {\n            const accessToken = window.localStorage.getItem('accessToken');\n            if (accessToken && isValidToken(accessToken)) {\n                setSession(accessToken);\n                const response = await axios.get('/api/auth/my-account');\n                const { user } = response.data;\n                dispatch({\n                    type: 'INITIALIZE',\n                    payload: {\n                        isAuthenticated: true,\n                        user,\n                    },\n                });\n            } else {\n                dispatch({\n                    type: 'INITIALIZE',\n                    payload: {\n                        isAuthenticated: false,\n                        user: null,\n                    },\n                });\n            }\n        } catch (err) {\n            console.error(err);\n            dispatch({\n                type: 'INITIALIZE',\n                payload: {\n                    isAuthenticated: false,\n                    user: null,\n                },\n            });\n        }\n    };\n\n    useEffect(() => {\n        console.log(\"--------------iniitalize passport-------------------\");\n        initialize();\n    }, []);\n\n    const otpVerify = async (otp, callback) => {\n        try {\n            const phoneNumber = window.phoneNumber;\n            const otpResult = await axios.post('/api/auth/verifyOtp', {\n                phoneNumber,\n                otp\n            });\n            const { success } = otpResult.data;\n            if (success) {\n                // register or update mongodb\n                const response = await axios.post('/api/auth/register', {\n                    phoneNumber,\n                });\n                if (response.status === 200) {\n                    const { token, user } = response.data;\n                    setSession(token);\n                    dispatch({\n                        type: 'LOGINED',\n                        payload: {\n                            user,\n                        },\n                    });\n                    callback({ success: true });\n                }\n            } else {\n                callback({ success: false, err: 'unmathed otpcode' });\n            }\n\n        } catch (err) {\n            callback({ success: false, err: 'otp response err' });\n        }\n    }\n\n    const codeVerify = async (phoneNumber, pinCode) => {\n        const response = await axios.post('/api/auth/pincode', { phoneNumber, pinCode });\n\n        const { token, user } = response.data;\n\n        // verify phoneNumber via firebase\n        if (!token) {\n            try {\n                window.phoneNumber = phoneNumber;\n                return { success: false, message: 'pin code verification error' };\n            } catch (err) {\n                return { success: false, message: 'pin code verification error' };\n            }\n        }\n        if (user.status === \"inactive\") {\n            return { success: false, message: 'Your account is inactive. Please contact with administrator' };\n        }\n        setSession(token);\n        dispatch({\n            type: 'LOGINED',\n            payload: {\n                user,\n            },\n        });\n        return { success: true, message: 'verification successfully' };\n\n    }\n\n    const login = async (phoneNumber) => {\n\n        const response = await axios.post('/api/auth/login', {\n            phoneNumber\n        });\n\n        const isPinCode = response.data.pinVerify\n\n        if (isPinCode) {\n            return 'pincode'\n        }\n        const { token, user } = response.data;\n\n        // verify phoneNumber via sms\n        if (!token) {\n            try {\n                window.phoneNumber = phoneNumber;\n                return 'otp';\n            } catch (err) {\n                console.log(err);\n                return 'otp';\n            }\n\n        } else {\n\n            if (user.status === \"inactive\") {\n                return \"inactive\";\n            }\n            setSession(token);\n            dispatch({\n                type: 'LOGINED',\n                payload: {\n                    user,\n                },\n            });\n            return 'navigate';\n\n        }\n\n    };\n\n\n    const logout = async () => {\n        try {\n            setSession(null);\n\n            dispatch({ type: 'LOGOUT' });\n            // signOut(AUTH);\n        } catch (err) {\n\n            console.log(err);\n        }\n\n    };\n\n    return (<AuthContext.Provider value={\n        {\n            ...state,\n            method: 'jwt',\n            login,\n            logout,\n            otpVerify,\n            codeVerify,\n            initialize,\n        }\n    } > {children} </AuthContext.Provider>);\n}\n\nexport { AuthContext, AuthProvider };", " export const HOST_API = \"https://www.aslaa.mn/\";\n// export const HOST_API = \"http://localhost:5001/\";\n", "export const HEADER = {\n    MOBILE_HEIGHT: 50,\n    MAIN_DESKTOP_HEIGHT: 70,\n};\n", "export const PUSHER_API = {\n    apiKey: \"92d8bd6d3400b94c11ca\",\n    cluster: \"ap1\",\n};\n", "export const PRICE_PER_MONTH = 5000;\nexport const OrderPrice = 2000;\n", "import PropTypes from 'prop-types';\nimport { Link as RouterLink } from 'react-router-dom';\n// @mui\nimport { Box } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nLogo.propTypes = {\n    disabledLink: PropTypes.bool,\n    sx: PropTypes.object,\n};\n\n\n\nexport default function Logo({ disabledLink = false, sx }) {\n\n    const logo = (<Box sx = {{ width: 60, height: 40, ...sx } } >\n       <img width =\"100%\" height=\"100%\" src = '/logo/logo.png' alt = \"logo\"/>\n       \n       </Box>);\n\n    if (disabledLink) {return  <> { logo } </>;}\n\n    return <RouterLink to = \"/home\"> { logo } </RouterLink>;\n    }", "import { createSlice } from \"@reduxjs/toolkit\";\n// utils\nimport axios from \"../../utils/axios\";\n//\nimport { dispatch } from \"../store\";\n\nconst initialState = {\n  isLoading: false,\n  error: null,\n  notifications: [],\n};\nconst slice = createSlice({\n  name: \"notification\",\n  initialState,\n  reducers: {\n    startLoading(state) {\n      state.isLoading = true;\n    },\n    hasError(state, action) {\n      state.error = action.payload;\n      state.isLoading = false;\n    },\n    setNotifications(state, action) {\n      state.isLoading = false;\n      state.notifications = action.payload;\n    },\n  },\n});\n\nexport default slice.reducer;\nexport const { setNotifications } = slice.actions;\n\nexport function getNotifications() {\n    // console.log(\"load #............\");\n  return async () => {\n    dispatch(slice.actions.startLoading());\n    try {\n      // console.log(\"load notifications............\");\n      const response = await axios.get(\"/api/log/sim-status\");\n      dispatch(slice.actions.setNotifications(response.data.data));\n    } catch (error) {\n      dispatch(slice.actions.hasError(error));\n    }\n  };\n}\n", "import { initializeApp } from 'firebase/app';\nimport { getMessaging, getToken } from 'firebase/messaging';\n\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyDA_x4YElOAVmT4rS3B-xcmCzhvefDTOrI\",\n  authDomain: \"rccdemo-41279.firebaseapp.com\",\n  projectId: \"rccdemo-41279\",\n  storageBucket: \"rccdemo-41279.appspot.com\",\n  messagingSenderId: \"963013875719\",\n  appId: \"1:963013875719:web:f9511f343bceb59b06f2a2\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Setup Messaging\nexport const messaging = getMessaging(app);\n\n// Optionally export getToken if you need it elsewhere\nexport { getToken };", "import { Outlet } from 'react-router-dom';\n// @mui\nimport { styled } from '@mui/material/styles';\n\n// components\nimport Logo from '../components/Logo';\n\n// ----------------------------------------------------------------------\n\nconst HeaderStyle = styled('header')(({ theme }) => ({\n  top: 0,\n  left: 0,\n  lineHeight: 0,\n  maxWidth: '600px',\n  position: 'absolute',\n  padding: theme.spacing(3, 3, 0),\n  [theme.breakpoints.up('sm')]: {\n    padding: theme.spacing(5, 5, 0)\n  }\n}));\n\n// ----------------------------------------------------------------------\n\nexport default function LogoOnlyLayout() {\n  return (\n    <>\n      <HeaderStyle>\n        <Logo disabledLink/>\n      </HeaderStyle>\n      <Outlet />\n    </>\n  );\n}\n", "import axios from 'axios';\n// config\nimport { HOST_API } from '../config';\n\n// ----------------------------------------------------------------------\n\nconst axiosInstance = axios.create({\n  baseURL: HOST_API,\n});\n\naxiosInstance.interceptors.response.use(\n  (response) => response,\n  (error) => Promise.reject((error.response && error.response.data) || 'Something went wrong')\n);\n\nexport default axiosInstance;\n", "const reportWebVitals = (onPerfEntry) => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n", "import PropTypes from 'prop-types';\nimport { createContext, useState, useEffect } from 'react';\n// hooks\nimport useResponsive from '../hooks/useResponsive';\n\n// ----------------------------------------------------------------------\n\nconst initialState = {\n  collapseClick: false,\n  collapseHover: false,\n  onToggleCollapse: () => {},\n  onHoverEnter: () => {},\n  onHoverLeave: () => {},\n};\n\nconst CollapseDrawerContext = createContext(initialState);\n\n// ----------------------------------------------------------------------\n\nCollapseDrawerProvider.propTypes = {\n  children: PropTypes.node,\n};\n\nfunction CollapseDrawerProvider({ children }) {\n  const isDesktop = useResponsive('up', 'lg');\n\n  const [collapse, setCollapse] = useState({\n    click: false,\n    hover: false,\n  });\n\n  useEffect(() => {\n    if (!isDesktop) {\n      setCollapse({\n        click: false,\n        hover: false,\n      });\n    }\n  }, [isDesktop]);\n\n  const handleToggleCollapse = () => {\n    setCollapse({ ...collapse, click: !collapse.click });\n  };\n\n  const handleHoverEnter = () => {\n    if (collapse.click) {\n      setCollapse({ ...collapse, hover: true });\n    }\n  };\n\n  const handleHoverLeave = () => {\n    setCollapse({ ...collapse, hover: false });\n  };\n\n  return (\n    <CollapseDrawerContext.Provider\n      value={{\n        isCollapse: collapse.click && !collapse.hover,\n        collapseClick: collapse.click,\n        collapseHover: collapse.hover,\n        onToggleCollapse: handleToggleCollapse,\n        onHoverEnter: handleHoverEnter,\n        onHoverLeave: handleHoverLeave,\n      }}\n    >\n      {children}\n    </CollapseDrawerContext.Provider>\n  );\n}\n\nexport { CollapseDrawerProvider, CollapseDrawerContext };\n", "import { alpha } from '@mui/material/styles';\n\n// ----------------------------------------------------------------------\n\nfunction createGradient(color1, color2) {\n  return `linear-gradient(to bottom, ${color1}, ${color2})`;\n}\n\n// SETUP COLORS\nconst PRIMARY = {\n  lighter: '#2ee7ff',\n  light: '#38e8ff',\n  main: '#33848f',\n  dark: '#01060a',\n  darker: '#00060a'\n};\nconst SECONDARY = {\n  lighter: '#D6E4FF',\n  light: '#84A9FF',\n  main: '#3366FF',\n  dark: '#1939B7',\n  darker: '#091A7A',\n}; \nconst INFO = {\n  lighter: '#D0F2FF',\n  light: '#74CAFF', \n  main: '#1890FF',\n  dark: '#0C53B7',\n  darker: '#04297A',\n};\nconst SUCCESS = {\n  lighter: '#E9FCD4',\n  light: '#AAF27F',\n  main: '#54D62C',\n  dark: '#229A16',\n  darker: '#08660D',\n};\nconst WARNING = {\n  lighter: '#FFF7CD',\n  light: '#FFE16A',\n  main: '#FFC107',\n  dark: '#B78103',\n  darker: '#7A4F01',\n};\nconst ERROR = {\n  lighter: '#FFE7D9',\n  light: '#FFA48D',\n  main: '#FF4842',\n  dark: '#B72136',\n  darker: '#7A0C2E',\n};\n\nconst GREY = {\n  0: '#FFFFFF',\n  100: '#38e8ff',\n  200: '#38B1FF',\n  300: '#D0F2FF',\n  400: '#004F99',\n  500: '#38e8ff',\n  600: '#061C2A',\n  700: '#0a1217',\n  800: '#040e16',\n  900: '#00060a',\n  500_8: alpha('#38e8ff', 0.08),\n  500_12: alpha('#38e8ff', 0.12),\n  500_16: alpha('#38e8ff', 0.16),\n  500_24: alpha('#38e8ff', 0.24),\n  500_32: alpha('#38e8ff', 0.32),\n  500_48: alpha('#38e8ff', 0.48),\n  500_56: alpha('#38e8ff', 0.56),\n  500_80: alpha('#38e8ff', 0.8),\n};\n\nconst GRADIENTS = {\n  primary: createGradient(PRIMARY.light, PRIMARY.main),\n  info: createGradient(INFO.light, INFO.main),\n  success: createGradient(SUCCESS.light, SUCCESS.main),\n  warning: createGradient(WARNING.light, WARNING.main),\n  error: createGradient(ERROR.light, ERROR.main),\n};\n\nconst COMMON = {\n  common: { black: '#000', white: '#f0f0f0' },\n  primary: { ...PRIMARY, contrastText: '#f0f0f0' },\n  secondary: { ...SECONDARY, contrastText: '#f0f0f0' },\n  info: { ...INFO, contrastText: '#f0f0f0' },\n  success: { ...SUCCESS, contrastText: GREY[800] },\n  warning: { ...WARNING, contrastText: GREY[800] },\n  error: { ...ERROR, contrastText: '#f0f0f0' },\n  grey: GREY,\n  gradients: GRADIENTS,\n  divider: GREY[500_24],\n  action: {\n    hover: GREY[700],\n    selected: GREY[700],\n    disabled: GREY[500_80],\n    disabledBackground: GREY[500_24],\n    focus: GREY[700],\n    hoverOpacity: 0.8,\n    disabledOpacity: 0.48,\n  },\n};\n\nconst palette = {\n  dark: {\n    ...COMMON,\n    mode: 'dark',\n    text: { primary: '#f0f0f0', secondary: '#f0f0f0', disabled: '#a3a3a3' },\n    background: {\n      paper: '#000000',    // Pure black for premium feel\n      default: '#000000',  // Pure black background\n      neutral: '#262626'   // Dark gray for neutral elements\n    },\n    action: { active: '#737373', ...COMMON.action },\n  },\n};\n\nexport default palette;\n", "// @mui\nimport { useTheme } from '@mui/material/styles';\n// hooks\nimport useResponsive from '../hooks/useResponsive';\n\n// ----------------------------------------------------------------------\n\nexport default function GetFontValue(variant) {\n  const theme = useTheme();\n  const breakpoints = useWidth();\n\n  const key = theme.breakpoints.up(breakpoints === 'xl' ? 'lg' : breakpoints);\n\n  const hasResponsive =\n    variant === 'h1' ||\n    variant === 'h2' ||\n    variant === 'h3' ||\n    variant === 'h4' ||\n    variant === 'h5' ||\n    variant === 'h6';\n\n  const getFont =\n    hasResponsive && theme.typography[variant][key] ? theme.typography[variant][key] : theme.typography[variant];\n\n  const fontSize = remToPx(getFont.fontSize);\n  const lineHeight = Number(theme.typography[variant].lineHeight) * fontSize;\n  const { fontWeight } = theme.typography[variant];\n  const { letterSpacing } = theme.typography[variant];\n\n  return { fontSize, lineHeight, fontWeight, letterSpacing };\n}\n\n// ----------------------------------------------------------------------\n\nexport function remToPx(value) {\n  return Math.round(parseFloat(value) * 16);\n}\n\nexport function pxToRem(value) {\n  return `${value / 16}rem`;\n}\n\nexport function responsiveFontSizes({ sm, md, lg }) {\n  return {\n    '@media (min-width:600px)': {\n      fontSize: pxToRem(sm),\n    },\n    '@media (min-width:900px)': {\n      fontSize: pxToRem(md),\n    },\n    '@media (min-width:1200px)': {\n      fontSize: pxToRem(lg),\n    },\n  };\n}\n\n// ----------------------------------------------------------------------\n\nfunction useWidth() {\n  const theme = useTheme();\n  const keys = [...theme.breakpoints.keys].reverse();\n  return (\n    keys.reduce((output, key) => {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      const matches = useResponsive('up', key);\n      return !output && matches ? key : output;\n    }, null) || 'xs'\n  );\n}\n", "import { pxToRem, responsiveFontSizes } from '../utils/getFontValue';\n\n// ----------------------------------------------------------------------\n\nconst FONT_PRIMARY = 'Public Sans, sans-serif'; // Google Font\n// const FONT_SECONDARY = 'CircularStd, sans-serif'; // Local Font\n\n// Car Control System Fonts - Standardized\nconst FONT_DIGITAL = 'Orbitron, monospace'; // Digital display font (speed, critical status)\nconst FONT_AUTOMOTIVE = 'Roboto Mono, monospace'; // Primary automotive interface font\nconst FONT_MONOSPACE = 'Roboto Mono, monospace'; // Unified monospace font\n\nconst typography = {\n  fontFamily: FONT_PRIMARY,\n  fontWeightRegular: 400,\n  fontWeightMedium: 600,\n  fontWeightBold: 700,\n  h1: {\n    fontWeight: 700,\n    lineHeight: 80 / 64,\n    fontSize: pxToRem(40),\n    letterSpacing: 2,\n    ...responsiveFontSizes({ sm: 52, md: 58, lg: 64 }),\n  },\n  h2: {\n    fontWeight: 700,\n    lineHeight: 64 / 48,\n    fontSize: pxToRem(32),\n    ...responsiveFontSizes({ sm: 40, md: 44, lg: 48 }),\n  },\n  h3: {\n    fontWeight: 700,\n    lineHeight: 1.5,\n    fontSize: pxToRem(24),\n    ...responsiveFontSizes({ sm: 26, md: 30, lg: 32 }),\n  },\n  h4: {\n    fontWeight: 700,\n    lineHeight: 1.5,\n    fontSize: pxToRem(20),\n    ...responsiveFontSizes({ sm: 20, md: 24, lg: 24 }),\n  },\n  h5: {\n    fontWeight: 700,\n    lineHeight: 1.5,\n    fontSize: pxToRem(18),\n    ...responsiveFontSizes({ sm: 19, md: 20, lg: 20 }),\n  },\n  h6: {\n    fontWeight: 700,\n    lineHeight: 28 / 18,\n    fontSize: pxToRem(17),\n    ...responsiveFontSizes({ sm: 18, md: 18, lg: 18 }),\n  },\n  subtitle1: {\n    fontWeight: 600,\n    lineHeight: 1.5,\n    fontSize: pxToRem(16),\n  },\n  subtitle2: {\n    fontWeight: 600,\n    lineHeight: 22 / 14,\n    fontSize: pxToRem(14),\n  },\n  body1: {\n    lineHeight: 1.5,\n    fontSize: pxToRem(16),\n  },\n  body2: {\n    lineHeight: 22 / 14,\n    fontSize: pxToRem(14),\n  },\n  caption: {\n    lineHeight: 1.5,\n    fontSize: pxToRem(12),\n  },\n  overline: {\n    fontWeight: 700,\n    lineHeight: 1.5,\n    fontSize: pxToRem(12),\n    textTransform: 'uppercase',\n  },\n  button: {\n    fontWeight: 700,\n    lineHeight: 24 / 14,\n    fontSize: pxToRem(14),\n    textTransform: 'capitalize',\n  },\n  // Car Control System Typography - Standardized\n  digitalDisplay: {\n    fontFamily: FONT_DIGITAL, // Reserved for speed and critical status\n    fontWeight: 600,\n    fontSize: pxToRem(16),\n    letterSpacing: '0.1em',\n    lineHeight: 1.4,\n  },\n  automotiveData: {\n    fontFamily: FONT_AUTOMOTIVE, // Primary font for all indicators\n    fontWeight: 500, // Values\n    fontSize: pxToRem(14),\n    letterSpacing: '0.05em', // Standardized spacing\n    lineHeight: 1.3,\n  },\n  automotiveLabel: {\n    fontFamily: FONT_AUTOMOTIVE, // Primary font for all indicators\n    fontWeight: 400, // Labels\n    fontSize: pxToRem(14),\n    letterSpacing: '0.05em', // Standardized spacing\n    lineHeight: 1.3,\n  },\n  technicalInfo: {\n    fontFamily: FONT_AUTOMOTIVE, // Unified with automotive font\n    fontWeight: 400,\n    fontSize: pxToRem(12),\n    letterSpacing: '0.05em', // Standardized spacing\n    lineHeight: 1.2,\n  },\n  carStatus: {\n    fontFamily: FONT_DIGITAL, // Reserved for critical status\n    fontWeight: 700,\n    fontSize: pxToRem(18),\n    letterSpacing: '0.15em',\n    lineHeight: 1.5,\n  },\n};\n\nexport default typography;\n", "// ----------------------------------------------------------------------\n\nconst breakpoints = {\n  values: {\n    xs: 350,\n    sm: 600,\n    md: 900,\n    lg: 1200,\n    xl: 1536\n  }\n};\n\nexport default breakpoints;\n", "import PropTypes from 'prop-types';\nimport { useMemo } from 'react';\n// @mui\nimport { CssBaseline } from '@mui/material';\nimport { createTheme, ThemeProvider as MUIThemeProvider, StyledEngineProvider } from '@mui/material/styles';\n// hooks \n//\nimport palette from './palette';\nimport typography from './typography';\nimport breakpoints from './breakpoints';\n\n// ----------------------------------------------------------------------\n\nThemeProvider.propTypes = {\n  children: PropTypes.node,\n};\n\nexport default function ThemeProvider({ children }) {\n  // const { themeMode, themeDirection } = useSettings();\n  // const isLight = themeMode === 'light';\n\n      const themeOptions = useMemo(\n        () => ({\n          palette: palette.dark,\n          typography,\n          breakpoints,\n          shape: { borderRadius: 8 },\n          // shadows:  shadows.dark,\n        }),\n        []\n      );\n\n  const theme = createTheme(themeOptions);\n  // theme.components = componentsOverride(theme);\n  return (\n    <StyledEngineProvider injectFirst>\n      <MUIThemeProvider theme={theme}>\n        <CssBaseline />\n        {children}\n      </MUIThemeProvider>\n    </StyledEngineProvider>\n  );\n}\n", "import React from 'react';\nimport { SnackbarProvider } from 'notistack';\n\nconst NotistackProvider = ({ children }) => {\n  return (\n    <SnackbarProvider maxSnack={3}>\n      {children}\n    </SnackbarProvider>\n  );\n};\n\nexport default NotistackProvider;\n", "import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst MotionLazyContainer = ({ children }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport default MotionLazyContainer;\n", "import PropTypes from 'prop-types';\nimport { Navigate } from 'react-router-dom';\n// hooks\nimport useAuth from '../hooks/useAuth';\n\n// ----------------------------------------------------------------------\n\nGuestGuard.propTypes = {\n  children: PropTypes.node\n};\n\nexport default function GuestGuard({ children }) {\n  const { isAuthenticated } = useAuth();\n\n  if (isAuthenticated) {\n    return <Navigate to=\"/home\" />;\n  }\n\n  return <>{children}</>;\n}\n", "import { lazy } from 'react';\nimport GuestGuard from '../guards/GuestGuard';\n\nconst Login = lazy(() => import('../pages/auth/Login'));\nconst VerifyCode = lazy(() => import('../pages/auth/VerifyCode'));\nconst ForgotPassword = lazy(() => import('../pages/auth/ForgotPassword'));\n\nconst authRoutes = {\n  path: 'auth',\n  children: [\n    {\n      path: 'login',\n      element: (\n        <GuestGuard>\n          <Login />\n        </GuestGuard>\n      ),\n    },\n    { path: 'verify', element: (<GuestGuard> <VerifyCode /></GuestGuard>) },\n    { path: 'forgot-password', element: (<GuestGuard> <ForgotPassword /></GuestGuard>) },\n  ],\n};\n\nexport default authRoutes;\n", "import React from 'react';\nimport CircularProgress from '@mui/material/CircularProgress';\n\nconst LoadingScreen = () => {\n  return (\n    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n      <CircularProgress />\n    </div>\n  );\n};\n\nexport default LoadingScreen;\n", "import PropTypes from 'prop-types';\nimport { useState } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\n// hooks\nimport useAuth from '../hooks/useAuth';\n// components\nimport LoadingScreen from '../components/LoadingScreen';\n\n// ----------------------------------------------------------------------\n\n\nAuthGuard.propTypes = {\n  children: PropTypes.node,\n};\n\nexport default function AuthGuard({ children }) {\n  const { isAuthenticated, isInitialized } = useAuth();\n  const { pathname } = useLocation();\n  const [requestedLocation, setRequestedLocation] = useState(null);\n\n  if (!isInitialized) {\n    return <LoadingScreen />;\n  } \n  if (!isAuthenticated) {\n    if (pathname !== requestedLocation) {\n      setRequestedLocation(pathname);\n    }\n    return <Navigate to={'/auth/login'} />;\n  }\n\n  if (requestedLocation && pathname !== requestedLocation) {\n    setRequestedLocation(null);\n    return <Navigate to={requestedLocation} />;\n  }\n\n  return <>{children}</>;\n}\n", "import { lazy } from 'react';\nimport AuthGuard from '../guards/AuthGuard';\n\n\nconst Home = lazy(() => import('../pages/Home'));\nconst LandingPage = lazy(() => import('../pages/Landing'));\nconst PinCode = lazy(() => import('../pages/PinCode'));\nconst TimeCommand = lazy(() => import('../pages/TimeCommand'));\nconst HelpPage = lazy(() => import('../pages/helpPage'));\nconst LogManagement = lazy(() => import('../pages/LogManagement'));\nconst LogLicense = lazy(() => import('../pages/InvoiceLog'));\nconst Notification = lazy(() => import('../pages/Notification'));\nconst CarLocationsWithRouter = lazy(() => import('../pages/CarLocationsWithRouter'));\nconst SimCardLogs = lazy(() => import('../pages/SimCardLog'));\nconst GpsHistoryOnMap = lazy(() => import('../pages/GpsHistoryOnMap'));\nconst ConfigureDrivers = lazy(() => import('../pages/ConfigureDrivers'));\nconst LicenseProfile = lazy(() => import('../pages/LicenseProfile'));\nconst DriverProfile = lazy(() => import('../pages/DriverProfile'));\nconst DeviceRegister = lazy(() => import('../pages/DeviceRegister'));\nconst ConfigGPS = lazy(() => import('../pages/ConfigGPS'));\nconst Order = lazy(() => import('../pages/Order'));\nconst DeviceConfig = lazy(() => import('../pages/DeviceConfig'));\nconst MqttTestPage = lazy(() => import('../pages/MqttTestPage'));\nconst PahoMqttConfig = lazy(() => import('../pages/PahoMqttConfig'));\n\nconst mainRoutes = {\n  path: '',\n  children: [\n    { element: (<LandingPage />), index: true },\n    { path: 'home', element: (<AuthGuard> <Home /> </AuthGuard>) },\n    { path: 'help', element: <HelpPage /> },\n    { path: 'device-register', element: (<AuthGuard> <DeviceRegister /> </AuthGuard>) },\n    { path: 'license-profile', element: (<AuthGuard> <LicenseProfile /> </AuthGuard>) },\n    { path: 'pin-code', element: (<AuthGuard> <PinCode /> </AuthGuard>) },\n    { path: 'time-command', element: (<AuthGuard> <TimeCommand /> </AuthGuard>) },\n    { path: 'log-management', element: (<AuthGuard> <LogManagement /> </AuthGuard>) },\n    { path: 'log-map', element: (<AuthGuard> <CarLocationsWithRouter /> </AuthGuard>) },\n    { path: 'log-sim', element: (<AuthGuard> <SimCardLogs /> </AuthGuard>) },\n    { path: 'log-gps', element: (<AuthGuard> <GpsHistoryOnMap /> </AuthGuard>) },\n    { path: 'configure-driver', element: (<AuthGuard> <ConfigureDrivers /> </AuthGuard>) },\n    { path: 'configure-gps/:id', element: (<AuthGuard> <ConfigGPS /> </AuthGuard>) },\n    { path: 'log-license', element: (<AuthGuard> <LogLicense /> </AuthGuard>) },\n    { path: 'Order', element: (<AuthGuard> <Order /> </AuthGuard>) },\n    { path: 'driver-profile', element: (<AuthGuard> <DriverProfile /> </AuthGuard>) },\n    { path: 'notification', element: (<AuthGuard> <Notification /> </AuthGuard>) },\n\n    { path: 'device-config/:id', element: <DeviceConfig /> },\n    { path: 'mqtt-test', element: <MqttTestPage /> },\n    { path: 'mqtt-tcp-test', element: <MqttTestPage /> },\n    { path: 'paho-mqtt', element: <PahoMqttConfig /> },\n  ]\n};\n\nexport default mainRoutes;\n", "import PropTypes from 'prop-types';\nimport { Navigate } from 'react-router-dom';\n// hooks\nimport useAuth from '../hooks/useAuth';\n// components\nimport LoadingScreen from '../components/LoadingScreen';\n\n// ----------------------------------------------------------------------\n\nAdminGuard.propTypes = {\n  children: PropTypes.node\n};\n\nexport default function AdminGuard({ children }) {\n  const { isAuthenticated, isInitialized, user } = useAuth();\n\n  if (!isInitialized) {\n    return <LoadingScreen />;\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/auth/login\" />;\n  }\n\n  if (!user?.role?.includes(\"admin\")) {\n    return <Navigate to=\"/\" />;\n  }\n\n  return <>{children}</>;\n}", "import { lazy } from 'react';\nimport AuthGuard from '../guards/AuthGuard';\nimport AdminGuard from '../guards/AdminGuard';\n\nconst DeviceEdit = lazy(() => import('../pages/admin/DeviceEdit'));\nconst UserManage = lazy(() => import('../pages/admin/UserManage'));\nconst TransactionLogs = lazy(() => import('../pages/admin/TransactionLogs'));\nconst WithdawRequests = lazy(() => import('../pages/admin/WithdrawLogs'));\nconst Orders = lazy(() => import('../pages/admin/OrderList'));\nconst AppManagement = lazy(() => import('../pages/admin/AppManagement'));\n\nconst adminRoutes = {\n  path: 'admin',\n  children: [\n    { path: 'device/:id', element: <AuthGuard><AdminGuard><DeviceEdit /></AdminGuard></AuthGuard> },\n    { path: 'user-manage', element: <AuthGuard><AdminGuard><UserManage /></AdminGuard></AuthGuard> },\n    { path: 'orders', element: <AuthGuard><AdminGuard><Orders /></AdminGuard></AuthGuard> },\n    { path: 'transactions', element: <AuthGuard><AdminGuard><TransactionLogs /></AdminGuard></AuthGuard> },\n    { path: 'withdraw-requests', element: <AuthGuard><AdminGuard><WithdawRequests /></AdminGuard></AuthGuard> },\n    { path: 'app-management', element: <AuthGuard><AdminGuard><AppManagement /></AdminGuard></AuthGuard> },\n  ],\n};\n\nexport default adminRoutes;\n", "import { lazy } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport LogoOnlyLayout from '../layout/LogoOnlyLayout';\n\nconst NotFound = lazy(() => import('../pages/Page404'));\n\nconst errorRoutes = {\n  path: '*',\n  element: <LogoOnlyLayout />,\n  children: [\n    { path: '404', element: <NotFound /> },\n    { path: '*', element: <Navigate to=\"/404\" replace /> },\n  ],\n};\n\nexport default errorRoutes;\n", "import { useRoutes } from 'react-router-dom';\nimport authRoutes from './authRoutes';\nimport mainRoutes from './mainRoutes';\nimport adminRoutes from './adminRoutes';\nimport errorRoutes from './errorRoutes';\n\nexport default function Router() {\n  return useRoutes([authRoutes, mainRoutes, adminRoutes, errorRoutes]);\n}\n", "import React from 'react';\n\nfunction Footer() {\n  return (\n    <footer style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '10px' }}>\n      <p style={{ margin: '0 10px' }}>&copy; 2025 Elec.mn All Rights Reserved.</p>\n\n    </footer>\n  );\n}\n\nexport default Footer;\n", "// src/App.js\n\nimport { useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\n// theme\nimport ThemeProvider from './theme';\nimport NotistackProvider from './components/NotistackProvider';\nimport MotionLazyContainer from './components/MotionLazyContainer';\nimport Router from './routes';\nimport { Suspense } from 'react';\nimport LoadingScreen from './components/LoadingScreen';\nimport Footer from './components/Footer';\n// ----------------------------------------------------------------------\n\nexport default function App() {\n    const { i18n } = useTranslation();\n    const lang = localStorage.getItem(\"language\") || \"en\";\n\n    useEffect(() => {\n      i18n.changeLanguage(lang);\n    }, [lang, i18n]);\n\n    return (\n        <ThemeProvider>\n            {/* <ThemeColorPresets> */}\n            {/* <RtlLayout> */}\n            <NotistackProvider>\n                <MotionLazyContainer>\n                    {/* <ProgressBarStyle /> */}\n\n                    {/* <Settings /> */}\n                    {/* <ScrollToTop /> */}\n                    <Suspense fallback={<LoadingScreen />}>\n                        <Router />\n                    </Suspense>\n                </MotionLazyContainer>\n            </NotistackProvider>\n            {/* </RtlLayout> */}\n            {/* </ThemeColorPresets> */}\n            <Footer />\n        </ThemeProvider>\n    );\n}\n", "// This optional code is used to register a service worker.\n// register() is not called by default.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://cra.link/PWA\n\nconst isLocalhost = Boolean(\n  window.location.hostname === 'localhost' ||\n    // [::1] is the IPv6 localhost address.\n    window.location.hostname === '[::1]' ||\n    // *********/8 are considered localhost for IPv4.\n    window.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)\n);\n\nexport function register(config) {\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\n    // The URL constructor is available in all browsers that support SW.\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\n    if (publicUrl.origin !== window.location.origin) {\n      // Our service worker won't work if PUBLIC_URL is on a different origin\n      // from what our page is served on. This might happen if a CDN is used to\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\n      return;\n    }\n\n    window.addEventListener('load', () => {\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\n\n      if (isLocalhost) {\n        // This is running on localhost. Let's check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, config);\n\n        // Add some additional logging to localhost, pointing developers to the\n        // service worker/PWA documentation.\n        navigator.serviceWorker.ready.then(() => {\n          console.log(\n            'This web app is being served cache-first by a service ' +\n              'worker. To learn more, visit https://cra.link/PWA'\n          );\n        });\n      } else {\n        // Is not localhost. Just register service worker\n        registerValidSW(swUrl, config);\n      }\n    });\n  }\n}\n\nfunction registerValidSW(swUrl, config) {\n  navigator.serviceWorker\n    .register(swUrl)\n    .then((registration) => {\n      registration.onupdatefound = () => {\n        const installingWorker = registration.installing;\n        if (installingWorker == null) {\n          return;\n        }\n        installingWorker.onstatechange = () => {\n          if (installingWorker.state === 'installed') {\n            if (navigator.serviceWorker.controller) {\n              // At this point, the updated precached content has been fetched,\n              // but the previous service worker will still serve the older\n              // content until all client tabs are closed.\n              console.log(\n                'New content is available and will be used when all ' +\n                  'tabs for this page are closed. See https://cra.link/PWA.'\n              );\n\n              // Execute callback\n              if (config && config.onUpdate) {\n                config.onUpdate(registration);\n              }\n            } else {\n              // At this point, everything has been precached.\n              // It's the perfect time to display a\n              // \"Content is cached for offline use.\" message.\n              console.log('Content is cached for offline use.');\n\n              // Execute callback\n              if (config && config.onSuccess) {\n                config.onSuccess(registration);\n              }\n            }\n          }\n        };\n      };\n    })\n    .catch((error) => {\n      console.error('Error during service worker registration:', error);\n    });\n}\n\nfunction checkValidServiceWorker(swUrl, config) {\n  // Check if the service worker can be found. If it can't reload the page.\n  fetch(swUrl, {\n    headers: { 'Service-Worker': 'script' }\n  })\n    .then((response) => {\n      // Ensure service worker exists, and that we really are getting a JS file.\n      const contentType = response.headers.get('content-type');\n      if (response.status === 404 || (contentType != null && contentType.indexOf('javascript') === -1)) {\n        // No service worker found. Probably a different app. Reload the page.\n        navigator.serviceWorker.ready.then((registration) => {\n          registration.unregister().then(() => {\n            window.location.reload();\n          });\n        });\n      } else {\n        // Service worker found. Proceed as normal.\n        registerValidSW(swUrl, config);\n      }\n    })\n    .catch(() => {\n      console.log('No internet connection found. App is running in offline mode.');\n    });\n}\n\nexport function unregister() {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready\n      .then((registration) => {\n        registration.unregister();\n      })\n      .catch((error) => {\n        console.error(error.message);\n      });\n  }\n}\n", "import i18n from \"i18next\";\nimport Backend from \"i18next-http-backend\";\nimport LanguageDetector from \"i18next-browser-languagedetector\";\nimport { initReactI18next } from \"react-i18next\";\n\nconst availableLanguages = [\"mn\", \"en\", \"ru\"];\n\ni18n\n// load translation using http -> see /public/locales\n// learn more: https://github.com/i18next/i18next-http-backend\n    .use(Backend)\n    // detect user language\n    // learn more: https://github.com/i18next/i18next-browser-languageDetector\n    .use(LanguageDetector)\n    // pass the i18n instance to react-i18next.\n    .use(initReactI18next)\n    // init i18next\n    // for all options read: https://www.i18next.com/overview/configuration-options\n    .init({\n        debug: false, // Disabled for production\n        fallbackLng: 'en',\n        lng: 'mn',\n        whitelist: availableLanguages,\n        interpolation: {\n            escapeValue: false // not needed for react as it escapes by default\n        },\n\n        react: {\n            useSuspense: false\n        }\n    });\n\nexport default i18n;", "// scroll bar\nimport 'simplebar/src/simplebar.css';\n\n// lazy image\nimport 'react-lazy-load-image-component/src/effects/blur.css';\nimport 'react-lazy-load-image-component/src/effects/opacity.css';\nimport 'react-lazy-load-image-component/src/effects/black-and-white.css';\nimport './swiper.min.css';\nimport ReactDOM from 'react-dom';\nimport { BrowserRouter } from 'react-router-dom';\nimport { HelmetProvider } from 'react-helmet-async';\nimport { Provider as ReduxProvider } from 'react-redux';\nimport { PersistGate } from 'redux-persist/lib/integration/react';\nimport reportWebVitals from './reportWebVitals';\nimport \"./index.css\";\nimport \"./styles/carControl.css\";\n// contexts\nimport { store, persistor } from './redux/store';\nimport { AuthProvider } from './contexts/JWTContext';\nimport { CollapseDrawerProvider } from './contexts/CollapseDrawerContext';\n//\nimport App from './App';\nimport * as serviceWorkerRegistration from './serviceWorkerRegistration';\nimport \"./i18n\";\n// ----------------------------------------------------------------------\n\nReactDOM.render(\n  <AuthProvider>\n    <HelmetProvider>\n      <ReduxProvider store={store}>\n        <PersistGate loading={null} persistor={persistor}>\n          <CollapseDrawerProvider>\n            <BrowserRouter>\n              <App />\n            </BrowserRouter>\n          </CollapseDrawerProvider>\n        </PersistGate>\n      </ReduxProvider>\n    </HelmetProvider>\n  </AuthProvider>,\n  document.getElementById('root')\n);\n\nserviceWorkerRegistration.register();\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "import { createSlice } from '@reduxjs/toolkit';\n\n\nconst initialState = {\n  isLoading: false,\n  error: null,\n  status: null, \n  routines:null,\n}\nconst slice = createSlice({\n  name: 'car',\n  initialState,\n  reducers: {\n    startLoading(state) {\n      state.isLoading = true;\n    }, \n    hasError(state, action) {\n      state.error = action.payload;\n      state.isLoading = false;\n    },\n    setCarStatus(state, action) {\n      state.isLoading = false;\n      state.status = action.payload;\n    }, \n    setRoutines(state,action){\n      state.routines = action.payload;\n    }\n  }\n});\n\nexport default slice.reducer;\nexport const {\n  setCarStatus,\n  setRoutines, \n} = slice.actions; ", "import { combineReducers } from 'redux';\nimport storage from 'redux-persist/lib/storage';\n// slices\nimport carReducer from './slices/car'; \nimport notificationReducer from './slices/notification';\n// ----------------------------------------------------------------------\n\nconst rootPersistConfig = {\n  key: 'root',\n  storage,\n  keyPrefix: 'redux-',\n  whitelist: ['car'],\n};\nconst rootReducer = combineReducers({\n  car: carReducer, \n  notification:notificationReducer,\n});\n\nexport { rootPersistConfig, rootReducer };\n", "import { configureStore } from '@reduxjs/toolkit';\nimport { useDispatch as useAppDispatch, useSelector as useAppSelector } from 'react-redux';\nimport { persistStore, persistReducer } from 'redux-persist';\nimport { rootPersistConfig, rootReducer } from './rootReducer';\n\n// ----------------------------------------------------------------------\n\nconst store = configureStore({\n  reducer: persistReducer(rootPersistConfig, rootReducer),\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: false,\n      immutableCheck: false,\n    }),\n});\n\nconst persistor = persistStore(store);\n\nconst { dispatch } = store;\n\nconst useSelector = useAppSelector;\n\nconst useDispatch = () => useAppDispatch();\n\nexport { store, persistor, dispatch, useSelector, useDispatch };\n", "import { useContext } from 'react';\n//\nimport { AuthContext } from '../contexts/JWTContext';\n// import { AuthContext } from '../contexts/FirebaseContext';\n// import { AuthContext } from '../contexts/Auth0Context';\n// import { AuthContext } from '../contexts/AwsCognitoContext';\n\n// ----------------------------------------------------------------------\n\nconst useAuth = () => {\n  const context = useContext(AuthContext);\n\n  if (!context) throw new Error('Auth context must be use inside AuthProvider');\n\n  return context;\n};\n\nexport default useAuth;\n"], "sourceRoot": ""}