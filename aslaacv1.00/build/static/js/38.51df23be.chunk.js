(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[38],{1281:function(t,e,a){"use strict";a.r(e),a.d(e,"default",(function(){return h}));var r=a(46),n=a(612),i=a(520),o=a(565),c=a(0),s=a(2);const p=Object(c.lazy)((()=>a.e(41).then(a.bind(null,739)))),l=Object(c.lazy)((()=>Promise.all([a.e(0),a.e(1),a.e(2),a.e(3),a.e(17)]).then(a.bind(null,1251)))),d=Object(r.a)("div")((t=>{let{theme:e}=t;return{maxWidth:480,margin:"auto",display:"flex",minHeight:"100vh",flexDirection:"column",justifyContent:"center",alignContent:"space-between",gap:3}}));function h(){return Object(s.jsx)(o.a,{title:"Forgot Password",children:Object(s.jsxs)(d,{children:[Object(s.jsx)(n.a,{variant:"h3",gutterBottom:!0,textAlign:"center",children:"\u0428\u0438\u043d\u044d \u043d\u0443\u0443\u0446 \u04af\u0433 \u04af\u04af\u0441\u0433\u044d\u0445"}),Object(s.jsx)(i.a,{width:"50%",sx:{mx:"auto",mb:3,mt:-3},children:Object(s.jsx)(p,{})}),Object(s.jsx)(n.a,{paragraph:!0,textAlign:"center",children:"\u0422\u0430\u043d\u044b \u043d\u0443\u0443\u0446 \u04af\u0433\u044d\u044d \u0441\u044d\u0440\u0433\u044d\u044d\u0445\u0438\u0439\u043d \u0442\u0443\u043b\u0434 \u0442\u04e9\u0445\u04e9\u04e9\u0440\u04e9\u043c\u0436\u0438\u0439\u043d \u0434\u0443\u0433\u0430\u0430\u0440\u044b\u043d \u0441\u04af\u04af\u043b\u0438\u0439\u043d 6 \u043e\u0440\u043e\u043d\u0433 \u043e\u0440\u0443\u0443\u043b\u043d\u0430 \u0443\u0443. \u0425\u044d\u0440\u044d\u0432 \u0442\u0430 \u0442\u04e9\u0445\u04e9\u04e9\u0440\u04e9\u043c\u0436\u0438\u0439\u043d \u0434\u0443\u0433\u0430\u0430\u0440\u044b\u0433 \u043c\u044d\u0434\u044d\u0445\u0433\u04af\u0439 \u0431\u043e\u043b \u0442\u0430 \u043c\u0430\u0448\u0438\u043d\u0440\u0443\u0443\u0433\u0430\u0430 \u043c\u0435\u0441\u0441\u0435\u0436\u044d\u044d\u0440 id \u0433\u044d\u0436 \u0431\u0438\u0447\u044d\u044d\u0434 \u0438\u043b\u0433\u044d\u044d\u0436 \u0430\u0432\u043d\u0430 \u0443\u0443. \u0416\u0438\u0447 \u0445\u0430\u0440\u0438\u0443 \u0430\u0432\u0430\u0445\u0430\u0434 \u043c\u0430\u0448\u0438\u043d\u0434 \u043d\u044d\u0433\u0436 \u0445\u043e\u043d\u043e\u0433 \u0431\u0430\u0439\u0445\u044b\u0433 \u0430\u043d\u0445\u0430\u0430\u0440\u043d\u0430 \u0443\u0443.        "}),Object(s.jsxs)(i.a,{width:"80%",mx:"auto",my:3,children:[Object(s.jsx)(l,{})," "]})]})})}},565:function(t,e,a){"use strict";var r=a(7),n=a.n(r),i=a(231),o=a(0),c=a(520),s=a(611),p=a(2);const l=Object(o.forwardRef)(((t,e)=>{let{children:a,title:r="",meta:n,...o}=t;return Object(p.jsxs)(p.Fragment,{children:[Object(p.jsxs)(i.a,{children:[Object(p.jsx)("title",{children:r}),n]}),Object(p.jsx)(c.a,{ref:e,...o,children:Object(p.jsx)(s.a,{children:a})})]})}));l.propTypes={children:n.a.node.isRequired,title:n.a.string,meta:n.a.node},e.a=l},566:function(t,e,a){"use strict";var r=a(179);const n=Object(r.a)();e.a=n},611:function(t,e,a){"use strict";var r=a(11),n=a(3),i=a(0),o=a(30),c=a(224),s=a(515),p=a(540),l=a(511),d=a(566),h=a(518),u=a(2);const b=["className","component","disableGutters","fixed","maxWidth","classes"],m=Object(h.a)(),g=Object(d.a)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,e["maxWidth".concat(Object(c.a)(String(a.maxWidth)))],a.fixed&&e.fixed,a.disableGutters&&e.disableGutters]}}),j=t=>Object(l.a)({props:t,name:"MuiContainer",defaultTheme:m}),x=(t,e)=>{const{classes:a,fixed:r,disableGutters:n,maxWidth:i}=t,o={root:["root",i&&"maxWidth".concat(Object(c.a)(String(i))),r&&"fixed",n&&"disableGutters"]};return Object(p.a)(o,(t=>Object(s.a)(e,t)),a)};var O=a(51),f=a(46),v=a(66);const y=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{createStyledComponent:e=g,useThemeProps:a=j,componentName:c="MuiContainer"}=t,s=e((t=>{let{theme:e,ownerState:a}=t;return Object(n.a)({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",display:"block"},!a.disableGutters&&{paddingLeft:e.spacing(2),paddingRight:e.spacing(2),[e.breakpoints.up("sm")]:{paddingLeft:e.spacing(3),paddingRight:e.spacing(3)}})}),(t=>{let{theme:e,ownerState:a}=t;return a.fixed&&Object.keys(e.breakpoints.values).reduce(((t,a)=>{const r=a,n=e.breakpoints.values[r];return 0!==n&&(t[e.breakpoints.up(r)]={maxWidth:"".concat(n).concat(e.breakpoints.unit)}),t}),{})}),(t=>{let{theme:e,ownerState:a}=t;return Object(n.a)({},"xs"===a.maxWidth&&{[e.breakpoints.up("xs")]:{maxWidth:Math.max(e.breakpoints.values.xs,444)}},a.maxWidth&&"xs"!==a.maxWidth&&{[e.breakpoints.up(a.maxWidth)]:{maxWidth:"".concat(e.breakpoints.values[a.maxWidth]).concat(e.breakpoints.unit)}})})),p=i.forwardRef((function(t,e){const i=a(t),{className:p,component:l="div",disableGutters:d=!1,fixed:h=!1,maxWidth:m="lg"}=i,g=Object(r.a)(i,b),j=Object(n.a)({},i,{component:l,disableGutters:d,fixed:h,maxWidth:m}),O=x(j,c);return Object(u.jsx)(s,Object(n.a)({as:l,ownerState:j,className:Object(o.a)(O.root,p),ref:e},g))}));return p}({createStyledComponent:Object(f.a)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,e["maxWidth".concat(Object(O.a)(String(a.maxWidth)))],a.fixed&&e.fixed,a.disableGutters&&e.disableGutters]}}),useThemeProps:t=>Object(v.a)({props:t,name:"MuiContainer"})});e.a=y},612:function(t,e,a){"use strict";var r=a(11),n=a(3),i=a(0),o=a(30),c=a(544),s=a(540),p=a(46),l=a(66),d=a(51),h=a(541),u=a(515);function b(t){return Object(u.a)("MuiTypography",t)}Object(h.a)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);var m=a(2);const g=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],j=Object(p.a)("span",{name:"MuiTypography",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,a.variant&&e[a.variant],"inherit"!==a.align&&e["align".concat(Object(d.a)(a.align))],a.noWrap&&e.noWrap,a.gutterBottom&&e.gutterBottom,a.paragraph&&e.paragraph]}})((t=>{let{theme:e,ownerState:a}=t;return Object(n.a)({margin:0},a.variant&&e.typography[a.variant],"inherit"!==a.align&&{textAlign:a.align},a.noWrap&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},a.gutterBottom&&{marginBottom:"0.35em"},a.paragraph&&{marginBottom:16})})),x={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},O={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},f=i.forwardRef((function(t,e){const a=Object(l.a)({props:t,name:"MuiTypography"}),i=(t=>O[t]||t)(a.color),p=Object(c.a)(Object(n.a)({},a,{color:i})),{align:h="inherit",className:u,component:f,gutterBottom:v=!1,noWrap:y=!1,paragraph:W=!1,variant:w="body1",variantMapping:S=x}=p,k=Object(r.a)(p,g),R=Object(n.a)({},p,{align:h,color:i,className:u,component:f,gutterBottom:v,noWrap:y,paragraph:W,variant:w,variantMapping:S}),M=f||(W?"p":S[w]||x[w])||"span",B=(t=>{const{align:e,gutterBottom:a,noWrap:r,paragraph:n,variant:i,classes:o}=t,c={root:["root",i,"inherit"!==t.align&&"align".concat(Object(d.a)(e)),a&&"gutterBottom",r&&"noWrap",n&&"paragraph"]};return Object(s.a)(c,b,o)})(R);return Object(m.jsx)(j,Object(n.a)({as:M,ref:e,ownerState:R,className:Object(o.a)(B.root,u)},k))}));e.a=f}}]);
//# sourceMappingURL=38.51df23be.chunk.js.map