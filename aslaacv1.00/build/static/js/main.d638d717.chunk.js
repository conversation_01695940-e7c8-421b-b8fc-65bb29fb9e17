(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[6],{151:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n(120),a=n(177);function c(e,t,n,c){const r=Object(i.a)(),o=Object(a.a)(r.breakpoints.up(t)),s=Object(a.a)(r.breakpoints.down(t)),l=Object(a.a)(r.breakpoints.between(n,c)),d=Object(a.a)(r.breakpoints.only(t));return"up"===e?o:"down"===e?s:"between"===e?l:"only"===e?d:null}},171:function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return h}));var i=n(0),a=n(47),c=n(326);n(184);const r=e=>{e?(localStorage.setItem("accessToken",e),a.a.defaults.headers.common.Authorization="Bearer ".concat(e)):(localStorage.removeItem("accessToken"),delete a.a.defaults.headers.common.Authorization)};var o=n(2);const s={isAuthenticated:!1,isInitialized:!1,user:null},l={INITIALIZE:(e,t)=>{const{isAuthenticated:n,user:i}=t.payload;return{...e,isAuthenticated:n,isInitialized:!0,user:i}},OTPFINAL:(e,t)=>{const{final:n}=t.payload;return{...e,isAuthenticated:!1,isInitialized:!0,final:n,user:null}},LOGINED:(e,t)=>{const{user:n}=t.payload;return{...e,isAuthenticated:!0,user:n}},LOGOUT:e=>({...e,isAuthenticated:!1,final:null,user:null})},d=(e,t)=>l[t.type]?l[t.type](e,t):e,u=Object(i.createContext)({...s,method:"jwt",login:()=>Promise.resolve(),logout:()=>Promise.resolve(),initialize:()=>Promise.resolve()});function h(e){let{children:t}=e;const[n,l]=Object(i.useReducer)(d,s),h=async()=>{try{const e=window.localStorage.getItem("accessToken");if(e&&(e=>{if(!e)return!1;const t=Object(c.a)(e),n=Date.now()/1e3;return t.exp>n})(e)){r(e);const t=await a.a.get("/api/auth/my-account"),{user:n}=t.data;l({type:"INITIALIZE",payload:{isAuthenticated:!0,user:n}})}else l({type:"INITIALIZE",payload:{isAuthenticated:!1,user:null}})}catch(e){console.error(e),l({type:"INITIALIZE",payload:{isAuthenticated:!1,user:null}})}};Object(i.useEffect)((()=>{console.log("--------------iniitalize passport-------------------"),h()}),[]);return Object(o.jsxs)(u.Provider,{value:{...n,method:"jwt",login:async e=>{const t=await a.a.post("/api/auth/login",{phoneNumber:e});if(t.data.pinVerify)return"pincode";const{token:n,user:i}=t.data;if(n)return"inactive"===i.status?"inactive":(r(n),l({type:"LOGINED",payload:{user:i}}),"navigate");try{return window.phoneNumber=e,"otp"}catch(c){return console.log(c),"otp"}},logout:async()=>{try{r(null),l({type:"LOGOUT"})}catch(e){console.log(e)}},otpVerify:async(e,t)=>{try{const n=window.phoneNumber,i=await a.a.post("/api/auth/verifyOtp",{phoneNumber:n,otp:e}),{success:c}=i.data;if(c){const e=await a.a.post("/api/auth/register",{phoneNumber:n});if(200===e.status){const{token:n,user:i}=e.data;r(n),l({type:"LOGINED",payload:{user:i}}),t({success:!0})}}else t({success:!1,err:"unmathed otpcode"})}catch(n){t({success:!1,err:"otp response err"})}},codeVerify:async(e,t)=>{const n=await a.a.post("/api/auth/pincode",{phoneNumber:e,pinCode:t}),{token:i,user:c}=n.data;if(!i)try{return window.phoneNumber=e,{success:!1,message:"pin code verification error"}}catch(o){return{success:!1,message:"pin code verification error"}}return"inactive"===c.status?{success:!1,message:"Your account is inactive. Please contact with administrator"}:(r(i),l({type:"LOGINED",payload:{user:c}}),{success:!0,message:"verification successfully"})},initialize:h},children:[" ",t," "]})}},232:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return c}));const i="https://www.aslaa.mn/",a={MOBILE_HEIGHT:50,MAIN_DESKTOP_HEIGHT:70};n(241);const c=5e3},236:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(52),a=n(520),c=n(2);function r(e){let{disabledLink:t=!1,sx:n}=e;const r=Object(c.jsx)(a.a,{sx:{width:60,height:40,...n},children:Object(c.jsx)("img",{width:"100%",height:"100%",src:"/logo/logo.png",alt:"logo"})});return t?Object(c.jsxs)(c.Fragment,{children:[" ",r," "]}):Object(c.jsxs)(i.b,{to:"/home",children:[" ",r," "]})}},240:function(e,t,n){"use strict";n.d(t,"b",(function(){return s}));var i=n(104),a=n(47),c=n(86);const r=Object(i.b)({name:"notification",initialState:{isLoading:!1,error:null,notifications:[]},reducers:{startLoading(e){e.isLoading=!0},hasError(e,t){e.error=t.payload,e.isLoading=!1},setNotifications(e,t){e.isLoading=!1,e.notifications=t.payload}}});t.a=r.reducer;const{setNotifications:o}=r.actions;function s(){return async()=>{Object(c.a)(r.actions.startLoading());try{const e=await a.a.get("/api/log/sim-status");Object(c.a)(r.actions.setNotifications(e.data.data))}catch(e){Object(c.a)(r.actions.hasError(e))}}}},241:function(e,t,n){"use strict";n.d(t,"b",(function(){return r}));var i=n(325),a=n(213);n.d(t,"a",(function(){return a.b}));const c=Object(i.a)({apiKey:"AIzaSyDA_x4YElOAVmT4rS3B-xcmCzhvefDTOrI",authDomain:"rccdemo-41279.firebaseapp.com",projectId:"rccdemo-41279",storageBucket:"rccdemo-41279.appspot.com",messagingSenderId:"963013875719",appId:"1:963013875719:web:f9511f343bceb59b06f2a2"}),r=Object(a.a)(c)},313:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n(5),a=n(46),c=n(236),r=n(2);const o=Object(a.a)("header")((e=>{let{theme:t}=e;return{top:0,left:0,lineHeight:0,maxWidth:"600px",position:"absolute",padding:t.spacing(3,3,0),[t.breakpoints.up("sm")]:{padding:t.spacing(5,5,0)}}}));function s(){return Object(r.jsxs)(r.Fragment,{children:[Object(r.jsx)(o,{children:Object(r.jsx)(c.a,{disabledLink:!0})}),Object(r.jsx)(i.b,{})]})}},350:function(e,t,n){},360:function(e,t,n){},361:function(e,t,n){},386:function(e,t){},388:function(e,t){},400:function(e,t){},402:function(e,t){},430:function(e,t){},432:function(e,t){},433:function(e,t){},438:function(e,t){},440:function(e,t){},446:function(e,t){},448:function(e,t){},467:function(e,t){},47:function(e,t,n){"use strict";var i=n(324),a=n.n(i),c=n(232);const r=a.a.create({baseURL:c.b});r.interceptors.response.use((e=>e),(e=>Promise.reject(e.response&&e.response.data||"Something went wrong"))),t.a=r},479:function(e,t){},482:function(e,t){},507:function(e,t,n){"use strict";n.r(t);n(346),n(347),n(348),n(349),n(350);var i=n(49),a=n.n(i),c=n(52),r=n(231),o=n(129),s=n(322);var l=e=>{e&&e instanceof Function&&n.e(45).then(n.bind(null,1273)).then((t=>{let{getCLS:n,getFID:i,getFCP:a,getLCP:c,getTTFB:r}=t;n(e),i(e),a(e),c(e),r(e)}))},d=(n(360),n(361),n(86)),u=n(171),h=n(0),j=n(151),b=n(2);const f={collapseClick:!1,collapseHover:!1,onToggleCollapse:()=>{},onHoverEnter:()=>{},onHoverLeave:()=>{}},m=Object(h.createContext)(f);function p(e){let{children:t}=e;const n=Object(j.a)("up","lg"),[i,a]=Object(h.useState)({click:!1,hover:!1});Object(h.useEffect)((()=>{n||a({click:!1,hover:!1})}),[n]);return Object(b.jsx)(m.Provider,{value:{isCollapse:i.click&&!i.hover,collapseClick:i.click,collapseHover:i.hover,onToggleCollapse:()=>{a({...i,click:!i.click})},onHoverEnter:()=>{i.click&&a({...i,hover:!0})},onHoverLeave:()=>{a({...i,hover:!1})}},children:t})}var g=n(546),O=n(539),x=n(336),y=n(500),v=n(545),w=n(538);function z(e,t){return"linear-gradient(to bottom, ".concat(e,", ").concat(t,")")}const k={lighter:"#2ee7ff",light:"#38e8ff",main:"#33848f",dark:"#01060a",darker:"#00060a"},F={lighter:"#D0F2FF",light:"#74CAFF",main:"#1890FF",dark:"#0C53B7",darker:"#04297A"},S={lighter:"#E9FCD4",light:"#AAF27F",main:"#54D62C",dark:"#229A16",darker:"#08660D"},P={lighter:"#FFF7CD",light:"#FFE16A",main:"#FFC107",dark:"#B78103",darker:"#7A4F01"},I={lighter:"#FFE7D9",light:"#FFA48D",main:"#FF4842",dark:"#B72136",darker:"#7A0C2E"},A={0:"#FFFFFF",100:"#38e8ff",200:"#38B1FF",300:"#D0F2FF",400:"#004F99",500:"#38e8ff",600:"#061C2A",700:"#0a1217",800:"#040e16",900:"#00060a",5008:Object(w.a)("#38e8ff",.08),50012:Object(w.a)("#38e8ff",.12),50016:Object(w.a)("#38e8ff",.16),50024:Object(w.a)("#38e8ff",.24),50032:Object(w.a)("#38e8ff",.32),50048:Object(w.a)("#38e8ff",.48),50056:Object(w.a)("#38e8ff",.56),50080:Object(w.a)("#38e8ff",.8)},L={primary:z(k.light,k.main),info:z(F.light,F.main),success:z(S.light,S.main),warning:z(P.light,P.main),error:z(I.light,I.main)},E={common:{black:"#000",white:"#f0f0f0"},primary:{...k,contrastText:"#f0f0f0"},secondary:{lighter:"#D6E4FF",light:"#84A9FF",main:"#3366FF",dark:"#1939B7",darker:"#091A7A",contrastText:"#f0f0f0"},info:{...F,contrastText:"#f0f0f0"},success:{...S,contrastText:A[800]},warning:{...P,contrastText:A[800]},error:{...I,contrastText:"#f0f0f0"},grey:A,gradients:L,divider:A[50024],action:{hover:A[700],selected:A[700],disabled:A[50080],disabledBackground:A[50024],focus:A[700],hoverOpacity:.8,disabledOpacity:.48}};var H={dark:{...E,mode:"dark",text:{primary:"#f0f0f0",secondary:"#f0f0f0",disabled:"#a3a3a3"},background:{paper:"#000000",default:"#000000",neutral:"#262626"},action:{active:"#737373",...E.action}}};n(120);function T(e){return"".concat(e/16,"rem")}function C(e){let{sm:t,md:n,lg:i}=e;return{"@media (min-width:600px)":{fontSize:T(t)},"@media (min-width:900px)":{fontSize:T(n)},"@media (min-width:1200px)":{fontSize:T(i)}}}const W="Orbitron, monospace",N="Roboto Mono, monospace";var D={fontFamily:"Public Sans, sans-serif",fontWeightRegular:400,fontWeightMedium:600,fontWeightBold:700,h1:{fontWeight:700,lineHeight:1.25,fontSize:T(40),letterSpacing:2,...C({sm:52,md:58,lg:64})},h2:{fontWeight:700,lineHeight:64/48,fontSize:T(32),...C({sm:40,md:44,lg:48})},h3:{fontWeight:700,lineHeight:1.5,fontSize:T(24),...C({sm:26,md:30,lg:32})},h4:{fontWeight:700,lineHeight:1.5,fontSize:T(20),...C({sm:20,md:24,lg:24})},h5:{fontWeight:700,lineHeight:1.5,fontSize:T(18),...C({sm:19,md:20,lg:20})},h6:{fontWeight:700,lineHeight:28/18,fontSize:T(17),...C({sm:18,md:18,lg:18})},subtitle1:{fontWeight:600,lineHeight:1.5,fontSize:T(16)},subtitle2:{fontWeight:600,lineHeight:22/14,fontSize:T(14)},body1:{lineHeight:1.5,fontSize:T(16)},body2:{lineHeight:22/14,fontSize:T(14)},caption:{lineHeight:1.5,fontSize:T(12)},overline:{fontWeight:700,lineHeight:1.5,fontSize:T(12),textTransform:"uppercase"},button:{fontWeight:700,lineHeight:24/14,fontSize:T(14),textTransform:"capitalize"},digitalDisplay:{fontFamily:W,fontWeight:600,fontSize:T(16),letterSpacing:"0.1em",lineHeight:1.4},automotiveData:{fontFamily:N,fontWeight:500,fontSize:T(14),letterSpacing:"0.05em",lineHeight:1.3},automotiveLabel:{fontFamily:N,fontWeight:400,fontSize:T(14),letterSpacing:"0.05em",lineHeight:1.3},technicalInfo:{fontFamily:N,fontWeight:400,fontSize:T(12),letterSpacing:"0.05em",lineHeight:1.2},carStatus:{fontFamily:W,fontWeight:700,fontSize:T(18),letterSpacing:"0.15em",lineHeight:1.5}};var B={values:{xs:350,sm:600,md:900,lg:1200,xl:1536}};function R(e){let{children:t}=e;const n=Object(h.useMemo)((()=>({palette:H.dark,typography:D,breakpoints:B,shape:{borderRadius:8}})),[]),i=Object(x.a)(n);return Object(b.jsx)(y.a,{injectFirst:!0,children:Object(b.jsxs)(v.a,{theme:i,children:[Object(b.jsx)(O.a,{}),t]})})}var G=n(229);var U=e=>{let{children:t}=e;return Object(b.jsx)(G.a,{maxSnack:3,children:t})},M=n(543);var V=e=>{let{children:t}=e;return Object(b.jsx)(M.a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:t})},q=n(5),Z=n(96);function _(e){let{children:t}=e;const{isAuthenticated:n}=Object(Z.a)();return n?Object(b.jsx)(q.a,{to:"/home"}):Object(b.jsx)(b.Fragment,{children:t})}const J=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(15)]).then(n.bind(null,1317)))),K=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(2),n.e(5),n.e(18),n.e(39)]).then(n.bind(null,1318)))),Y=Object(h.lazy)((()=>n.e(38).then(n.bind(null,1281))));var $={path:"auth",children:[{path:"login",element:Object(b.jsx)(_,{children:Object(b.jsx)(J,{})})},{path:"verify",element:Object(b.jsxs)(_,{children:[" ",Object(b.jsx)(K,{})]})},{path:"forgot-password",element:Object(b.jsxs)(_,{children:[" ",Object(b.jsx)(Y,{})]})}]},Q=n(547);var X=()=>Object(b.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:Object(b.jsx)(Q.a,{})});function ee(e){let{children:t}=e;const{isAuthenticated:n,isInitialized:i}=Object(Z.a)(),{pathname:a}=Object(q.j)(),[c,r]=Object(h.useState)(null);return i?n?c&&a!==c?(r(null),Object(b.jsx)(q.a,{to:c})):Object(b.jsx)(b.Fragment,{children:t}):(a!==c&&r(a),Object(b.jsx)(q.a,{to:"/auth/login"})):Object(b.jsx)(X,{})}const te=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(20)]).then(n.bind(null,1298)))),ne=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(5),n.e(36)]).then(n.bind(null,1302)))),ie=Object(h.lazy)((()=>n.e(43).then(n.t.bind(null,1282,7)))),ae=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(22)]).then(n.bind(null,1304)))),ce=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(33),n.e(40)]).then(n.bind(null,1283)))),re=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(16)]).then(n.bind(null,1284)))),oe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(11)]).then(n.bind(null,1319)))),se=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(19)]).then(n.bind(null,1300)))),le=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(34)]).then(n.bind(null,1285)))),de=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(30)]).then(n.bind(null,1286)))),ue=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(26)]).then(n.bind(null,1307)))),he=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(28)]).then(n.bind(null,1287)))),je=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(29)]).then(n.bind(null,1288)))),be=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(25)]).then(n.bind(null,1289)))),fe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(9)]).then(n.bind(null,1290)))),me=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(31)]).then(n.bind(null,1291)))),pe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(21)]).then(n.bind(null,1292)))),ge=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(23)]).then(n.bind(null,1293)))),Oe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(32)]).then(n.bind(null,1305)))),xe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(35)]).then(n.bind(null,1294))));var ye={path:"",children:[{element:Object(b.jsx)(ne,{}),index:!0},{path:"home",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(te,{})," "]})},{path:"help",element:Object(b.jsx)(ce,{})},{path:"device-register",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(fe,{})," "]})},{path:"license-profile",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(je,{})," "]})},{path:"pin-code",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(ie,{})," "]})},{path:"time-command",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(ae,{})," "]})},{path:"log-management",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(re,{})," "]})},{path:"log-map",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(le,{})," "]})},{path:"log-sim",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(de,{})," "]})},{path:"log-gps",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(ue,{})," "]})},{path:"configure-driver",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(he,{})," "]})},{path:"configure-gps/:id",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(me,{})," "]})},{path:"log-license",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(oe,{})," "]})},{path:"Order",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(pe,{})," "]})},{path:"driver-profile",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(be,{})," "]})},{path:"notification",element:Object(b.jsxs)(ee,{children:[" ",Object(b.jsx)(se,{})," "]})},{path:"device-config/:id",element:Object(b.jsx)(ge,{})},{path:"mqtt-test",element:Object(b.jsx)(Oe,{})},{path:"mqtt-tcp-test",element:Object(b.jsx)(Oe,{})},{path:"paho-mqtt",element:Object(b.jsx)(xe,{})}]};function ve(e){var t;let{children:n}=e;const{isAuthenticated:i,isInitialized:a,user:c}=Object(Z.a)();return a?i?null!==c&&void 0!==c&&null!==(t=c.role)&&void 0!==t&&t.includes("admin")?Object(b.jsx)(b.Fragment,{children:n}):Object(b.jsx)(q.a,{to:"/"}):Object(b.jsx)(q.a,{to:"/auth/login"}):Object(b.jsx)(X,{})}const we=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(10)]).then(n.bind(null,1308)))),ze=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(24)]).then(n.bind(null,1303)))),ke=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(12)]).then(n.bind(null,1295)))),Fe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(13)]).then(n.bind(null,1296)))),Se=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(14)]).then(n.bind(null,1320)))),Pe=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(27)]).then(n.bind(null,1309))));var Ie={path:"admin",children:[{path:"device/:id",element:Object(b.jsx)(ee,{children:Object(b.jsx)(ve,{children:Object(b.jsx)(we,{})})})},{path:"user-manage",element:Object(b.jsx)(ee,{children:Object(b.jsx)(ve,{children:Object(b.jsx)(ze,{})})})},{path:"orders",element:Object(b.jsx)(ee,{children:Object(b.jsx)(ve,{children:Object(b.jsx)(Se,{})})})},{path:"transactions",element:Object(b.jsx)(ee,{children:Object(b.jsx)(ve,{children:Object(b.jsx)(ke,{})})})},{path:"withdraw-requests",element:Object(b.jsx)(ee,{children:Object(b.jsx)(ve,{children:Object(b.jsx)(Fe,{})})})},{path:"app-management",element:Object(b.jsx)(ee,{children:Object(b.jsx)(ve,{children:Object(b.jsx)(Pe,{})})})}]},Ae=n(313);const Le=Object(h.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(37)]).then(n.bind(null,1299))));var Ee={path:"*",element:Object(b.jsx)(Ae.a,{}),children:[{path:"404",element:Object(b.jsx)(Le,{})},{path:"*",element:Object(b.jsx)(q.a,{to:"/404",replace:!0})}]};function He(){return Object(q.p)([$,ye,Ie,Ee])}var Te=function(){return Object(b.jsx)("footer",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"10px"},children:Object(b.jsx)("p",{style:{margin:"0 10px"},children:"\xa9 2025 Elec.mn All Rights Reserved."})})};function Ce(){const{i18n:e}=Object(g.a)(),t=localStorage.getItem("language")||"en";return Object(h.useEffect)((()=>{e.changeLanguage(t)}),[t,e]),Object(b.jsxs)(R,{children:[Object(b.jsx)(U,{children:Object(b.jsx)(V,{children:Object(b.jsx)(h.Suspense,{fallback:Object(b.jsx)(X,{}),children:Object(b.jsx)(He,{})})})}),Object(b.jsx)(Te,{})]})}const We=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function Ne(e,t){navigator.serviceWorker.register(e).then((e=>{e.onupdatefound=()=>{const n=e.installing;null!=n&&(n.onstatechange=()=>{"installed"===n.state&&(navigator.serviceWorker.controller?(console.log("New content is available and will be used when all tabs for this page are closed. See https://cra.link/PWA."),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Content is cached for offline use."),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((e=>{console.error("Error during service worker registration:",e)}))}var De=n(209),Be=n(333),Re=n(335),Ge=n(139);De.a.use(Be.a).use(Re.a).use(Ge.e).init({debug:!1,fallbackLng:"en",lng:"mn",whitelist:["mn","en","ru"],interpolation:{escapeValue:!1},react:{useSuspense:!1}});De.a;a.a.render(Object(b.jsx)(u.b,{children:Object(b.jsx)(r.b,{children:Object(b.jsx)(o.a,{store:d.c,children:Object(b.jsx)(s.PersistGate,{loading:null,persistor:d.b,children:Object(b.jsx)(p,{children:Object(b.jsx)(c.a,{children:Object(b.jsx)(Ce,{})})})})})})}),document.getElementById("root")),function(e){if("serviceWorker"in navigator){if(new URL("",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",(()=>{const t="".concat("","/service-worker.js");We?(!function(e,t){fetch(e,{headers:{"Service-Worker":"script"}}).then((n=>{const i=n.headers.get("content-type");404===n.status||null!=i&&-1===i.indexOf("javascript")?navigator.serviceWorker.ready.then((e=>{e.unregister().then((()=>{window.location.reload()}))})):Ne(e,t)})).catch((()=>{console.log("No internet connection found. App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((()=>{console.log("This web app is being served cache-first by a service worker. To learn more, visit https://cra.link/PWA")}))):Ne(t,e)}))}}(),l()},86:function(e,t,n){"use strict";n.d(t,"c",(function(){return m})),n.d(t,"b",(function(){return p})),n.d(t,"a",(function(){return g}));var i=n(104),a=n(129),c=n(223),r=n(28),o=n(323),s=n.n(o);const l=Object(i.b)({name:"car",initialState:{isLoading:!1,error:null,status:null,routines:null},reducers:{startLoading(e){e.isLoading=!0},hasError(e,t){e.error=t.payload,e.isLoading=!1},setCarStatus(e,t){e.isLoading=!1,e.status=t.payload},setRoutines(e,t){e.routines=t.payload}}});var d=l.reducer;const{setCarStatus:u,setRoutines:h}=l.actions;var j=n(240);const b={key:"root",storage:s.a,keyPrefix:"redux-",whitelist:["car"]},f=Object(r.b)({car:d,notification:j.a}),m=Object(i.a)({reducer:Object(c.a)(b,f),middleware:e=>e({serializableCheck:!1,immutableCheck:!1})}),p=Object(c.b)(m),{dispatch:g}=m;a.c},96:function(e,t,n){"use strict";var i=n(0),a=n(171);t.a=()=>{const e=Object(i.useContext)(a.a);if(!e)throw new Error("Auth context must be use inside AuthProvider");return e}}},[[507,7,8]]]);
//# sourceMappingURL=main.d638d717.chunk.js.map