(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[5],{1315:function(t,e,n){"use strict";var o=n(11),r=n(3),i=n(0),c=n(30),s=n(540),a=n(51),l=n(46),u=n(66),f=n(654),d=n(228),p=n(612),h=n(541),g=n(515);function b(t){return Object(g.a)("MuiLink",t)}var v=Object(h.a)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]),m=n(10),y=n(538);const j={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"};var x=t=>{let{theme:e,ownerState:n}=t;const o=(t=>j[t]||t)(n.color),r=Object(m.b)(e,"palette.".concat(o),!1)||n.color,i=Object(m.b)(e,"palette.".concat(o,"Channel"));return"vars"in e&&i?"rgba(".concat(i," / 0.4)"):Object(y.a)(r,.4)},w=n(2);const O=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],k=Object(l.a)(p.a,{name:"MuiLink",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:n}=t;return[e.root,e["underline".concat(Object(a.a)(n.underline))],"button"===n.component&&e.button]}})((t=>{let{theme:e,ownerState:n}=t;return Object(r.a)({},"none"===n.underline&&{textDecoration:"none"},"hover"===n.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===n.underline&&Object(r.a)({textDecoration:"underline"},"inherit"!==n.color&&{textDecorationColor:x({theme:e,ownerState:n})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===n.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(v.focusVisible)]:{outline:"auto"}})})),S=i.forwardRef((function(t,e){const n=Object(u.a)({props:t,name:"MuiLink"}),{className:l,color:p="primary",component:h="a",onBlur:g,onFocus:v,TypographyClasses:m,underline:y="always",variant:x="inherit",sx:S}=n,_=Object(o.a)(n,O),{isFocusVisibleRef:E,onBlur:I,onFocus:A,ref:M}=Object(f.a)(),[D,R]=i.useState(!1),F=Object(d.a)(e,M),T=Object(r.a)({},n,{color:p,component:h,focusVisible:D,underline:y,variant:x}),L=(t=>{const{classes:e,component:n,focusVisible:o,underline:r}=t,i={root:["root","underline".concat(Object(a.a)(r)),"button"===n&&"button",o&&"focusVisible"]};return Object(s.a)(i,b,e)})(T);return Object(w.jsx)(k,Object(r.a)({color:p,className:Object(c.a)(L.root,l),classes:m,component:h,onBlur:t=>{I(t),!1===E.current&&R(!1),g&&g(t)},onFocus:t=>{A(t),!0===E.current&&R(!0),v&&v(t)},ref:F,ownerState:T,variant:x,sx:[...Object.keys(j).includes(p)?[]:[{color:p}],...Array.isArray(S)?S:[S]]},_))}));e.a=S},567:function(t,e,n){"use strict";n.d(e,"a",(function(){return Lt}));var o=n(0);const r=/^[a-z0-9]+(-[a-z0-9]+)*$/,i=Object.freeze({left:0,top:0,width:16,height:16,rotate:0,vFlip:!1,hFlip:!1});function c(t){return{...i,...t}}const s=function(t,e,n){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";const r=t.split(":");if("@"===t.slice(0,1)){if(r.length<2||r.length>3)return null;o=r.shift().slice(1)}if(r.length>3||!r.length)return null;if(r.length>1){const t=r.pop(),n=r.pop(),i={provider:r.length>0?r[0]:o,prefix:n,name:t};return e&&!a(i)?null:i}const i=r[0],c=i.split("-");if(c.length>1){const t={provider:o,prefix:c.shift(),name:c.join("-")};return e&&!a(t)?null:t}if(n&&""===o){const t={provider:o,prefix:"",name:i};return e&&!a(t,n)?null:t}return null},a=(t,e)=>!!t&&!(""!==t.provider&&!t.provider.match(r)||!(e&&""===t.prefix||t.prefix.match(r))||!t.name.match(r));function l(t,e){const n={...t};for(const o in i){const t=o;if(void 0!==e[t]){const o=e[t];if(void 0===n[t]){n[t]=o;continue}switch(t){case"rotate":n[t]=(n[t]+o)%4;break;case"hFlip":case"vFlip":n[t]=o!==n[t];break;default:n[t]=o}}}return n}function u(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function o(e,n){if(void 0!==t.icons[e])return Object.assign({},t.icons[e]);if(n>5)return null;const r=t.aliases;if(r&&void 0!==r[e]){const t=r[e],i=o(t.parent,n+1);return i?l(i,t):i}const i=t.chars;return!n&&i&&void 0!==i[e]?o(i[e],n+1):null}const r=o(e,0);if(r)for(const c in i)void 0===r[c]&&void 0!==t[c]&&(r[c]=t[c]);return r&&n?c(r):r}function f(t,e,n){n=n||{};const o=[];if("object"!==typeof t||"object"!==typeof t.icons)return o;t.not_found instanceof Array&&t.not_found.forEach((t=>{e(t,null),o.push(t)}));const r=t.icons;Object.keys(r).forEach((n=>{const r=u(t,n,!0);r&&(e(n,r),o.push(n))}));const c=n.aliases||"all";if("none"!==c&&"object"===typeof t.aliases){const n=t.aliases;Object.keys(n).forEach((r=>{if("variations"===c&&function(t){for(const e in i)if(void 0!==t[e])return!0;return!1}(n[r]))return;const s=u(t,r,!0);s&&(e(r,s),o.push(r))}))}return o}const d={provider:"string",aliases:"object",not_found:"object"};for(const Ut in i)d[Ut]=typeof i[Ut];function p(t){if("object"!==typeof t||null===t)return null;const e=t;if("string"!==typeof e.prefix||!t.icons||"object"!==typeof t.icons)return null;for(const r in d)if(void 0!==t[r]&&typeof t[r]!==d[r])return null;const n=e.icons;for(const c in n){const t=n[c];if(!c.match(r)||"string"!==typeof t.body)return null;for(const e in i)if(void 0!==t[e]&&typeof t[e]!==typeof i[e])return null}const o=e.aliases;if(o)for(const c in o){const t=o[c],e=t.parent;if(!c.match(r)||"string"!==typeof e||!n[e]&&!o[e])return null;for(const n in i)if(void 0!==t[n]&&typeof t[n]!==typeof i[n])return null}return e}let h=Object.create(null);try{const t=window||self;t&&1===t._iconifyStorage.version&&(h=t._iconifyStorage.storage)}catch(Nt){}function g(t,e){void 0===h[t]&&(h[t]=Object.create(null));const n=h[t];return void 0===n[e]&&(n[e]=function(t,e){return{provider:t,prefix:e,icons:Object.create(null),missing:Object.create(null)}}(t,e)),n[e]}function b(t,e){if(!p(e))return[];const n=Date.now();return f(e,((e,o)=>{o?t.icons[e]=o:t.missing[e]=n}))}function v(t,e){const n=t.icons[e];return void 0===n?null:n}let m=!1;function y(t){return"boolean"===typeof t&&(m=t),m}function j(t){const e="string"===typeof t?s(t,!0,m):t;return e?v(g(e.provider,e.prefix),e.name):null}function x(t,e){const n=s(t,!0,m);if(!n)return!1;return function(t,e,n){try{if("string"===typeof n.body)return t.icons[e]=Object.freeze(c(n)),!0}catch(Nt){}return!1}(g(n.provider,n.prefix),n.name,e)}const w=Object.freeze({inline:!1,width:null,height:null,hAlign:"center",vAlign:"middle",slice:!1,hFlip:!1,vFlip:!1,rotate:0});function O(t,e){const n={};for(const o in t){const r=o;if(n[r]=t[r],void 0===e[r])continue;const i=e[r];switch(r){case"inline":case"slice":"boolean"===typeof i&&(n[r]=i);break;case"hFlip":case"vFlip":!0===i&&(n[r]=!n[r]);break;case"hAlign":case"vAlign":"string"===typeof i&&""!==i&&(n[r]=i);break;case"width":case"height":("string"===typeof i&&""!==i||"number"===typeof i&&i||null===i)&&(n[r]=i);break;case"rotate":"number"===typeof i&&(n[r]+=i)}}return n}const k=/(-?[0-9.]*[0-9]+[0-9.]*)/g,S=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function _(t,e,n){if(1===e)return t;if(n=void 0===n?100:n,"number"===typeof t)return Math.ceil(t*e*n)/n;if("string"!==typeof t)return t;const o=t.split(k);if(null===o||!o.length)return t;const r=[];let i=o.shift(),c=S.test(i);for(;;){if(c){const t=parseFloat(i);isNaN(t)?r.push(i):r.push(Math.ceil(t*e*n)/n)}else r.push(i);if(i=o.shift(),void 0===i)return r.join("");c=!c}}function E(t){let e="";switch(t.hAlign){case"left":e+="xMin";break;case"right":e+="xMax";break;default:e+="xMid"}switch(t.vAlign){case"top":e+="YMin";break;case"bottom":e+="YMax";break;default:e+="YMid"}return e+=t.slice?" slice":" meet",e}function I(t,e){const n={left:t.left,top:t.top,width:t.width,height:t.height};let o,r,i=t.body;[t,e].forEach((t=>{const e=[],o=t.hFlip,r=t.vFlip;let c,s=t.rotate;switch(o?r?s+=2:(e.push("translate("+(n.width+n.left).toString()+" "+(0-n.top).toString()+")"),e.push("scale(-1 1)"),n.top=n.left=0):r&&(e.push("translate("+(0-n.left).toString()+" "+(n.height+n.top).toString()+")"),e.push("scale(1 -1)"),n.top=n.left=0),s<0&&(s-=4*Math.floor(s/4)),s%=4,s){case 1:c=n.height/2+n.top,e.unshift("rotate(90 "+c.toString()+" "+c.toString()+")");break;case 2:e.unshift("rotate(180 "+(n.width/2+n.left).toString()+" "+(n.height/2+n.top).toString()+")");break;case 3:c=n.width/2+n.left,e.unshift("rotate(-90 "+c.toString()+" "+c.toString()+")")}s%2===1&&(0===n.left&&0===n.top||(c=n.left,n.left=n.top,n.top=c),n.width!==n.height&&(c=n.width,n.width=n.height,n.height=c)),e.length&&(i='<g transform="'+e.join(" ")+'">'+i+"</g>")})),null===e.width&&null===e.height?(r="1em",o=_(r,n.width/n.height)):null!==e.width&&null!==e.height?(o=e.width,r=e.height):null!==e.height?(r=e.height,o=_(r,n.width/n.height)):(o=e.width,r=_(o,n.height/n.width)),"auto"===o&&(o=n.width),"auto"===r&&(r=n.height),o="string"===typeof o?o:o.toString()+"",r="string"===typeof r?r:r.toString()+"";const c={attributes:{width:o,height:r,preserveAspectRatio:E(e),viewBox:n.left.toString()+" "+n.top.toString()+" "+n.width.toString()+" "+n.height.toString()},body:i};return e.inline&&(c.inline=!0),c}const A=/\sid="(\S+)"/g,M="IconifyId"+Date.now().toString(16)+(16777216*Math.random()|0).toString(16);let D=0;function R(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:M;const n=[];let o;for(;o=A.exec(t);)n.push(o[1]);return n.length?(n.forEach((n=>{const o="function"===typeof e?e(n):e+(D++).toString(),r=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+r+')([")]|\\.[a-z])',"g"),"$1"+o+"$3")})),t):t}const F=Object.create(null);function T(t,e){F[t]=e}function L(t){return F[t]||F[""]}function N(t){let e;if("string"===typeof t.resources)e=[t.resources];else if(e=t.resources,!(e instanceof Array)||!e.length)return null;return{resources:e,path:void 0===t.path?"/":t.path,maxURL:t.maxURL?t.maxURL:500,rotate:t.rotate?t.rotate:750,timeout:t.timeout?t.timeout:5e3,random:!0===t.random,index:t.index?t.index:0,dataAfterTimeout:!1!==t.dataAfterTimeout}}const C=Object.create(null),U=["https://api.simplesvg.com","https://api.unisvg.com"],z=[];for(;U.length>0;)1===U.length||Math.random()>.5?z.push(U.shift()):z.push(U.pop());function P(t,e){const n=N(e);return null!==n&&(C[t]=n,!0)}function B(t){return C[t]}C[""]=N({resources:["https://api.iconify.design"].concat(z)});const V=(t,e)=>{let n=t,o=-1!==n.indexOf("?");return Object.keys(e).forEach((t=>{let r;try{r=function(t){switch(typeof t){case"boolean":return t?"true":"false";case"number":case"string":return encodeURIComponent(t);default:throw new Error("Invalid parameter")}}(e[t])}catch(Nt){return}n+=(o?"&":"?")+encodeURIComponent(t)+"="+r,o=!0})),n},$={},q={};let H=(()=>{let t;try{if(t=fetch,"function"===typeof t)return t}catch(Nt){}return null})();const J={prepare:(t,e,n)=>{const o=[];let r=$[e];void 0===r&&(r=function(t,e){const n=B(t);if(!n)return 0;let o;if(n.maxURL){let t=0;n.resources.forEach((e=>{const n=e;t=Math.max(t,n.length)}));const r=V(e+".json",{icons:""});o=n.maxURL-t-n.path.length-r.length}else o=0;const r=t+":"+e;return q[t]=n.path,$[r]=o,o}(t,e));const i="icons";let c={type:i,provider:t,prefix:e,icons:[]},s=0;return n.forEach(((n,a)=>{s+=n.length+1,s>=r&&a>0&&(o.push(c),c={type:i,provider:t,prefix:e,icons:[]},s=n.length),c.icons.push(n)})),o.push(c),o},send:(t,e,n)=>{if(!H)return void n("abort",424);let o=function(t){if("string"===typeof t){if(void 0===q[t]){const e=B(t);if(!e)return"/";q[t]=e.path}return q[t]}return"/"}(e.provider);switch(e.type){case"icons":{const t=e.prefix,n=e.icons.join(",");o+=V(t+".json",{icons:n});break}case"custom":{const t=e.uri;o+="/"===t.slice(0,1)?t.slice(1):t;break}default:return void n("abort",400)}let r=503;H(t+o).then((t=>{const e=t.status;if(200===e)return r=501,t.json();setTimeout((()=>{n(function(t){return 404===t}(e)?"abort":"next",e)}))})).then((t=>{"object"===typeof t&&null!==t?setTimeout((()=>{n("success",t)})):setTimeout((()=>{n("next",r)}))})).catch((()=>{n("next",r)}))}};const W=Object.create(null),Y=Object.create(null);function X(t,e){t.forEach((t=>{const n=t.provider;if(void 0===W[n])return;const o=W[n],r=t.prefix,i=o[r];i&&(o[r]=i.filter((t=>t.id!==e)))}))}let G=0;var K={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Q(t,e,n,o){const r=t.resources.length,i=t.random?Math.floor(Math.random()*r):t.index;let c;if(t.random){let e=t.resources.slice(0);for(c=[];e.length>1;){const t=Math.floor(Math.random()*e.length);c.push(e[t]),e=e.slice(0,t).concat(e.slice(t+1))}c=c.concat(e)}else c=t.resources.slice(i).concat(t.resources.slice(0,i));const s=Date.now();let a,l="pending",u=0,f=null,d=[],p=[];function h(){f&&(clearTimeout(f),f=null)}function g(){"pending"===l&&(l="aborted"),h(),d.forEach((t=>{"pending"===t.status&&(t.status="aborted")})),d=[]}function b(t,e){e&&(p=[]),"function"===typeof t&&p.push(t)}function v(){l="failed",p.forEach((t=>{t(void 0,a)}))}function m(){d.forEach((t=>{"pending"===t.status&&(t.status="aborted")})),d=[]}function y(){if("pending"!==l)return;h();const o=c.shift();if(void 0===o)return d.length?void(f=setTimeout((()=>{h(),"pending"===l&&(m(),v())}),t.timeout)):void v();const r={status:"pending",resource:o,callback:(e,n)=>{!function(e,n,o){const r="success"!==n;switch(d=d.filter((t=>t!==e)),l){case"pending":break;case"failed":if(r||!t.dataAfterTimeout)return;break;default:return}if("abort"===n)return a=o,void v();if(r)return a=o,void(d.length||(c.length?y():v()));if(h(),m(),!t.random){const n=t.resources.indexOf(e.resource);-1!==n&&n!==t.index&&(t.index=n)}l="completed",p.forEach((t=>{t(o)}))}(r,e,n)}};d.push(r),u++,f=setTimeout(y,t.rotate),n(o,e,r.callback)}return"function"===typeof o&&p.push(o),setTimeout(y),function(){return{startTime:s,payload:e,status:l,queriesSent:u,queriesPending:d.length,subscribe:b,abort:g}}}function Z(t){const e=function(t){if("object"!==typeof t||"object"!==typeof t.resources||!(t.resources instanceof Array)||!t.resources.length)throw new Error("Invalid Reduncancy configuration");const e=Object.create(null);let n;for(n in K)void 0!==t[n]?e[n]=t[n]:e[n]=K[n];return e}(t);let n=[];function o(){n=n.filter((t=>"pending"===t().status))}return{query:function(t,r,i){const c=Q(e,t,r,((t,e)=>{o(),i&&i(t,e)}));return n.push(c),c},find:function(t){const e=n.find((e=>t(e)));return void 0!==e?e:null},setIndex:t=>{e.index=t},getIndex:()=>e.index,cleanup:o}}function tt(){}const et=Object.create(null);function nt(t,e,n){let o,r;if("string"===typeof t){const e=L(t);if(!e)return n(void 0,424),tt;r=e.send;const i=function(t){if(void 0===et[t]){const e=B(t);if(!e)return;const n={config:e,redundancy:Z(e)};et[t]=n}return et[t]}(t);i&&(o=i.redundancy)}else{const e=N(t);if(e){o=Z(e);const n=L(t.resources?t.resources[0]:"");n&&(r=n.send)}}return o&&r?o.query(e,r,n)().abort:(n(void 0,424),tt)}const ot={};function rt(){}const it=Object.create(null),ct=Object.create(null),st=Object.create(null),at=Object.create(null);function lt(t,e){void 0===st[t]&&(st[t]=Object.create(null));const n=st[t];n[e]||(n[e]=!0,setTimeout((()=>{n[e]=!1,function(t,e){void 0===Y[t]&&(Y[t]=Object.create(null));const n=Y[t];n[e]||(n[e]=!0,setTimeout((()=>{if(n[e]=!1,void 0===W[t]||void 0===W[t][e])return;const o=W[t][e].slice(0);if(!o.length)return;const r=g(t,e);let i=!1;o.forEach((n=>{const o=n.icons,c=o.pending.length;o.pending=o.pending.filter((n=>{if(n.prefix!==e)return!0;const c=n.name;if(void 0!==r.icons[c])o.loaded.push({provider:t,prefix:e,name:c});else{if(void 0===r.missing[c])return i=!0,!0;o.missing.push({provider:t,prefix:e,name:c})}return!1})),o.pending.length!==c&&(i||X([{provider:t,prefix:e}],n.id),n.callback(o.loaded.slice(0),o.missing.slice(0),o.pending.slice(0),n.abort))}))})))}(t,e)})))}const ut=Object.create(null);function ft(t,e,n){void 0===ct[t]&&(ct[t]=Object.create(null));const o=ct[t];void 0===at[t]&&(at[t]=Object.create(null));const r=at[t];void 0===it[t]&&(it[t]=Object.create(null));const i=it[t];void 0===o[e]?o[e]=n:o[e]=o[e].concat(n).sort(),r[e]||(r[e]=!0,setTimeout((()=>{r[e]=!1;const n=o[e];delete o[e];const c=L(t);if(!c)return void function(){const n=(""===t?"":"@"+t+":")+e,o=Math.floor(Date.now()/6e4);ut[n]<o&&(ut[n]=o,console.error('Unable to retrieve icons for "'+n+'" because API is not configured properly.'))}();c.prepare(t,e,n).forEach((n=>{nt(t,n,((o,r)=>{const c=g(t,e);if("object"!==typeof o){if(404!==r)return;const t=Date.now();n.icons.forEach((e=>{c.missing[e]=t}))}else try{const n=b(c,o);if(!n.length)return;const r=i[e];n.forEach((t=>{delete r[t]})),ot.store&&ot.store(t,o)}catch(s){console.error(s)}lt(t,e)}))}))})))}const dt=(t,e)=>{const n=function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=[];return t.forEach((t=>{const r="string"===typeof t?s(t,!1,n):t;e&&!a(r,n)||o.push({provider:r.provider,prefix:r.prefix,name:r.name})})),o}(t,!0,y()),o=function(t){const e={loaded:[],missing:[],pending:[]},n=Object.create(null);t.sort(((t,e)=>t.provider!==e.provider?t.provider.localeCompare(e.provider):t.prefix!==e.prefix?t.prefix.localeCompare(e.prefix):t.name.localeCompare(e.name)));let o={provider:"",prefix:"",name:""};return t.forEach((t=>{if(o.name===t.name&&o.prefix===t.prefix&&o.provider===t.provider)return;o=t;const r=t.provider,i=t.prefix,c=t.name;void 0===n[r]&&(n[r]=Object.create(null));const s=n[r];void 0===s[i]&&(s[i]=g(r,i));const a=s[i];let l;l=void 0!==a.icons[c]?e.loaded:""===i||void 0!==a.missing[c]?e.missing:e.pending;const u={provider:r,prefix:i,name:c};l.push(u)})),e}(n);if(!o.pending.length){let t=!0;return e&&setTimeout((()=>{t&&e(o.loaded,o.missing,o.pending,rt)})),()=>{t=!1}}const r=Object.create(null),i=[];let c,l;o.pending.forEach((t=>{const e=t.provider,n=t.prefix;if(n===l&&e===c)return;c=e,l=n,i.push({provider:e,prefix:n}),void 0===it[e]&&(it[e]=Object.create(null));const o=it[e];void 0===o[n]&&(o[n]=Object.create(null)),void 0===r[e]&&(r[e]=Object.create(null));const s=r[e];void 0===s[n]&&(s[n]=[])}));const u=Date.now();return o.pending.forEach((t=>{const e=t.provider,n=t.prefix,o=t.name,i=it[e][n];void 0===i[o]&&(i[o]=u,r[e][n].push(o))})),i.forEach((t=>{const e=t.provider,n=t.prefix;r[e][n].length&&ft(e,n,r[e][n])})),e?function(t,e,n){const o=G++,r=X.bind(null,n,o);if(!e.pending.length)return r;const i={id:o,icons:e,callback:t,abort:r};return n.forEach((t=>{const e=t.provider,n=t.prefix;void 0===W[e]&&(W[e]=Object.create(null));const o=W[e];void 0===o[n]&&(o[n]=[]),o[n].push(i)})),r}(e,o,i):rt},pt="iconify2",ht="iconify",gt=ht+"-count",bt=ht+"-version",vt=36e5,mt={local:!0,session:!0};let yt=!1;const jt={local:0,session:0},xt={local:[],session:[]};let wt="undefined"===typeof window?{}:window;function Ot(t){const e=t+"Storage";try{if(wt&&wt[e]&&"number"===typeof wt[e].length)return wt[e]}catch(Nt){}return mt[t]=!1,null}function kt(t,e,n){try{return t.setItem(gt,n.toString()),jt[e]=n,!0}catch(Nt){return!1}}function St(t){const e=t.getItem(gt);if(e){const t=parseInt(e);return t||0}return 0}const _t=()=>{if(yt)return;yt=!0;const t=Math.floor(Date.now()/vt)-168;function e(e){const n=Ot(e);if(!n)return;const o=e=>{const o=ht+e.toString(),r=n.getItem(o);if("string"!==typeof r)return!1;let i=!0;try{const e=JSON.parse(r);if("object"!==typeof e||"number"!==typeof e.cached||e.cached<t||"string"!==typeof e.provider||"object"!==typeof e.data||"string"!==typeof e.data.prefix)i=!1;else{const t=e.provider,n=e.data.prefix;i=b(g(t,n),e.data).length>0}}catch(Nt){i=!1}return i||n.removeItem(o),i};try{const t=n.getItem(bt);if(t!==pt)return t&&function(t){try{const e=St(t);for(let n=0;n<e;n++)t.removeItem(ht+n.toString())}catch(Nt){}}(n),void function(t,e){try{t.setItem(bt,pt)}catch(Nt){}kt(t,e,0)}(n,e);let r=St(n);for(let n=r-1;n>=0;n--)o(n)||(n===r-1?r--:xt[e].push(n));kt(n,e,r)}catch(Nt){}}for(const n in mt)e(n)},Et=(t,e)=>{function n(n){if(!mt[n])return!1;const o=Ot(n);if(!o)return!1;let r=xt[n].shift();if(void 0===r&&(r=jt[n],!kt(o,n,r+1)))return!1;try{const n={cached:Math.floor(Date.now()/vt),provider:t,data:e};o.setItem(ht+r.toString(),JSON.stringify(n))}catch(Nt){return!1}return!0}yt||_t(),Object.keys(e.icons).length&&(e.not_found&&delete(e=Object.assign({},e)).not_found,n("local")||n("session"))};const It=/[\s,]+/;function At(t,e){e.split(It).forEach((e=>{switch(e.trim()){case"horizontal":t.hFlip=!0;break;case"vertical":t.vFlip=!0}}))}function Mt(t,e){e.split(It).forEach((e=>{const n=e.trim();switch(n){case"left":case"center":case"right":t.hAlign=n;break;case"top":case"middle":case"bottom":t.vAlign=n;break;case"slice":case"crop":t.slice=!0;break;case"meet":t.slice=!1}}))}function Dt(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=t.replace(/^-?[0-9.]*/,"");function o(t){for(;t<0;)t+=4;return t%4}if(""===n){const e=parseInt(t);return isNaN(e)?0:o(e)}if(n!==t){let e=0;switch(n){case"%":e=25;break;case"deg":e=90}if(e){let r=parseFloat(t.slice(0,t.length-n.length));return isNaN(r)?0:(r/=e,r%1===0?o(r):0)}}return e}const Rt={xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img",style:{}},Ft={...w,inline:!0};if(y(!0),T("",J),"undefined"!==typeof document&&"undefined"!==typeof window){ot.store=Et,_t();const t=window;if(void 0!==t.IconifyPreload){const e=t.IconifyPreload,n="Invalid IconifyPreload syntax.";"object"===typeof e&&null!==e&&(e instanceof Array?e:[e]).forEach((t=>{try{("object"!==typeof t||null===t||t instanceof Array||"object"!==typeof t.icons||"string"!==typeof t.prefix||!function(t,e){if("object"!==typeof t)return!1;if("string"!==typeof e&&(e="string"===typeof t.provider?t.provider:""),m&&""===e&&("string"!==typeof t.prefix||""===t.prefix)){let e=!1;return p(t)&&(t.prefix="",f(t,((t,n)=>{n&&x(t,n)&&(e=!0)}))),e}return!("string"!==typeof t.prefix||!a({provider:e,prefix:t.prefix,name:"a"}))&&!!b(g(e,t.prefix),t)}(t))&&console.error(n)}catch(e){console.error(n)}}))}if(void 0!==t.IconifyProviders){const e=t.IconifyProviders;if("object"===typeof e&&null!==e)for(let t in e){const n="IconifyProviders["+t+"] is invalid.";try{const o=e[t];if("object"!==typeof o||!o||void 0===o.resources)continue;P(t,o)||console.error(n)}catch(Ct){console.error(n)}}}}class Tt extends o.Component{constructor(t){super(t),this.state={icon:null}}_abortLoading(){this._loading&&(this._loading.abort(),this._loading=null)}_setData(t){this.state.icon!==t&&this.setState({icon:t})}_checkIcon(t){const e=this.state,n=this.props.icon;if("object"===typeof n&&null!==n&&"string"===typeof n.body)return this._icon="",this._abortLoading(),void((t||null===e.icon)&&this._setData({data:c(n)}));let o;if("string"!==typeof n||null===(o=s(n,!1,!0)))return this._abortLoading(),void this._setData(null);const r=j(o);if(null!==r){if(this._icon!==n||null===e.icon){this._abortLoading(),this._icon=n;const t=["iconify"];""!==o.prefix&&t.push("iconify--"+o.prefix),""!==o.provider&&t.push("iconify--"+o.provider),this._setData({data:r,classes:t}),this.props.onLoad&&this.props.onLoad(n)}}else this._loading&&this._loading.name===n||(this._abortLoading(),this._icon="",this._setData(null),this._loading={name:n,abort:dt([o],this._checkIcon.bind(this,!1))})}componentDidMount(){this._checkIcon(!1)}componentDidUpdate(t){t.icon!==this.props.icon&&this._checkIcon(!0)}componentWillUnmount(){this._abortLoading()}render(){const t=this.props,e=this.state.icon;if(null===e)return t.children?t.children:o.createElement("span",{});let n=t;return e.classes&&(n={...t,className:("string"===typeof t.className?t.className+" ":"")+e.classes.join(" ")}),((t,e,n,r)=>{const i=n?Ft:w,c=O(i,e),s="object"===typeof e.style&&null!==e.style?e.style:{},a={...Rt,ref:r,style:s};for(let o in e){const t=e[o];if(void 0!==t)switch(o){case"icon":case"style":case"children":case"onLoad":case"_ref":case"_inline":break;case"inline":case"hFlip":case"vFlip":c[o]=!0===t||"true"===t||1===t;break;case"flip":"string"===typeof t&&At(c,t);break;case"align":"string"===typeof t&&Mt(c,t);break;case"color":s.color=t;break;case"rotate":"string"===typeof t?c[o]=Dt(t):"number"===typeof t&&(c[o]=t);break;case"ariaHidden":case"aria-hidden":!0!==t&&"true"!==t&&delete a["aria-hidden"];break;default:void 0===i[o]&&(a[o]=t)}}const l=I(t,c);let u=0,f=e.id;"string"===typeof f&&(f=f.replace(/-/g,"_")),a.dangerouslySetInnerHTML={__html:R(l.body,f?()=>f+"ID"+u++:"iconifyReact")};for(let o in l.attributes)a[o]=l.attributes[o];return l.inline&&void 0===s.verticalAlign&&(s.verticalAlign="-0.125em"),o.createElement("svg",a)})(e.data,n,t._inline,t._ref)}}const Lt=o.forwardRef((function(t,e){const n={...t,_ref:e,_inline:!1};return o.createElement(Tt,n)}));o.forwardRef((function(t,e){const n={...t,_ref:e,_inline:!0};return o.createElement(Tt,n)}))},620:function(t,e,n){"use strict";var o=n(11),r=n(3),i=n(0),c=n(26),s=n(6),a=n(544),l=n(225),u=n(46),f=n(66),d=n(2);const p=["component","direction","spacing","divider","children"];function h(t,e){const n=i.Children.toArray(t).filter(Boolean);return n.reduce(((t,o,r)=>(t.push(o),r<n.length-1&&t.push(i.cloneElement(e,{key:"separator-".concat(r)})),t)),[])}const g=Object(u.a)("div",{name:"MuiStack",slot:"Root",overridesResolver:(t,e)=>[e.root]})((t=>{let{ownerState:e,theme:n}=t,o=Object(r.a)({display:"flex",flexDirection:"column"},Object(c.b)({theme:n},Object(c.e)({values:e.direction,breakpoints:n.breakpoints.values}),(t=>({flexDirection:t}))));if(e.spacing){const t=Object(s.a)(n),r=Object.keys(n.breakpoints.values).reduce(((t,n)=>(("object"===typeof e.spacing&&null!=e.spacing[n]||"object"===typeof e.direction&&null!=e.direction[n])&&(t[n]=!0),t)),{}),i=Object(c.e)({values:e.direction,base:r}),a=Object(c.e)({values:e.spacing,base:r});"object"===typeof i&&Object.keys(i).forEach(((t,e,n)=>{if(!i[t]){const o=e>0?i[n[e-1]]:"column";i[t]=o}}));const u=(n,o)=>{return{"& > :not(style) + :not(style)":{margin:0,["margin".concat((r=o?i[o]:e.direction,{row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"}[r]))]:Object(s.c)(t,n)}};var r};o=Object(l.a)(o,Object(c.b)({theme:n},a,u))}return o=Object(c.c)(n.breakpoints,o),o})),b=i.forwardRef((function(t,e){const n=Object(f.a)({props:t,name:"MuiStack"}),i=Object(a.a)(n),{component:c="div",direction:s="column",spacing:l=0,divider:u,children:b}=i,v=Object(o.a)(i,p),m={direction:s,spacing:l};return Object(d.jsx)(g,Object(r.a)({as:c,ownerState:m,ref:e},v,{children:u?h(b,u):b}))}));e.a=b}}]);
//# sourceMappingURL=5.7dfff338.chunk.js.map