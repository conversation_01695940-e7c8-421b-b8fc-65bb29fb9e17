<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Browser Test</title>
    <script src="https://unpkg.com/mqtt/dist/mqtt.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background-color: #f8f9fa;
            padding: 16px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
        .log-entry {
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MQTT Browser Test</h1>
        
        <div class="card">
            <h2>Connection Settings</h2>
            <div class="form-group">
                <label for="broker">Broker Address:</label>
                <input type="text" id="broker" value="*************">
            </div>
            <div class="form-group">
                <label for="port">Port:</label>
                <select id="port">
                    <option value="8083">8083 (WebSocket)</option>
                    <option value="8084">8084 (WebSocket)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="public">
            </div>
            <div class="form-group">
                <label for="clientId">Client ID:</label>
                <input type="text" id="clientId" value="">
            </div>
            <button id="connect">Connect</button>
            <button id="disconnect" disabled>Disconnect</button>
            <div id="status" class="status disconnected">Disconnected</div>
        </div>
        
        <div class="card">
            <h2>Publish Message</h2>
            <div class="form-group">
                <label for="topic">Topic:</label>
                <input type="text" id="topic" value="aslaa/test">
            </div>
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" rows="4">{"test": "Hello from browser"}</textarea>
            </div>
            <button id="publish" disabled>Publish</button>
        </div>
        
        <div class="card">
            <h2>Subscribe to Topic</h2>
            <div class="form-group">
                <label for="subscribeTopic">Topic:</label>
                <input type="text" id="subscribeTopic" value="aslaa/test">
            </div>
            <button id="subscribe" disabled>Subscribe</button>
            <button id="unsubscribe" disabled>Unsubscribe</button>
        </div>
        
        <div class="card">
            <h2>Log</h2>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Generate a random client ID if not provided
        document.getElementById('clientId').value = `browser_test_${Math.random().toString(16).substring(2, 10)}`;
        
        let client = null;
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        
        // Log function
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Connect button
        document.getElementById('connect').addEventListener('click', () => {
            const broker = document.getElementById('broker').value;
            const port = document.getElementById('port').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const clientId = document.getElementById('clientId').value;
            
            // Construct broker URL
            const brokerUrl = `ws://${broker}:${port}/mqtt`;
            
            // Connection options
            const options = {
                clientId: clientId,
                username: username,
                password: password,
                clean: true,
                connectTimeout: 30000,
                reconnectPeriod: 1000,
                keepalive: 60
            };
            
            log(`Connecting to ${brokerUrl}...`);
            
            try {
                // Connect to the broker
                client = mqtt.connect(brokerUrl, options);
                
                // Set up event handlers
                client.on('connect', () => {
                    log('Connected to MQTT broker!', 'success');
                    statusElement.textContent = 'Connected';
                    statusElement.className = 'status connected';
                    
                    // Enable/disable buttons
                    document.getElementById('connect').disabled = true;
                    document.getElementById('disconnect').disabled = false;
                    document.getElementById('publish').disabled = false;
                    document.getElementById('subscribe').disabled = false;
                });
                
                client.on('error', (error) => {
                    log(`Error: ${error.message}`, 'error');
                });
                
                client.on('offline', () => {
                    log('Client is offline', 'error');
                    statusElement.textContent = 'Offline';
                    statusElement.className = 'status disconnected';
                });
                
                client.on('disconnect', () => {
                    log('Disconnected from broker', 'error');
                    statusElement.textContent = 'Disconnected';
                    statusElement.className = 'status disconnected';
                });
                
                client.on('message', (topic, message) => {
                    const msg = message.toString();
                    log(`Received message on ${topic}: ${msg}`, 'success');
                    
                    // Try to parse as JSON for prettier display
                    try {
                        const jsonMsg = JSON.parse(msg);
                        log(`Parsed JSON: ${JSON.stringify(jsonMsg, null, 2)}`, 'success');
                    } catch (e) {
                        // Not JSON, that's fine
                    }
                });
            } catch (error) {
                log(`Connection error: ${error.message}`, 'error');
            }
        });
        
        // Disconnect button
        document.getElementById('disconnect').addEventListener('click', () => {
            if (client) {
                client.end();
                client = null;
                
                log('Disconnected from broker');
                statusElement.textContent = 'Disconnected';
                statusElement.className = 'status disconnected';
                
                // Enable/disable buttons
                document.getElementById('connect').disabled = false;
                document.getElementById('disconnect').disabled = true;
                document.getElementById('publish').disabled = true;
                document.getElementById('subscribe').disabled = true;
                document.getElementById('unsubscribe').disabled = true;
            }
        });
        
        // Publish button
        document.getElementById('publish').addEventListener('click', () => {
            if (!client) {
                log('Not connected to broker', 'error');
                return;
            }
            
            const topic = document.getElementById('topic').value;
            const message = document.getElementById('message').value;
            
            log(`Publishing to ${topic}: ${message}`);
            client.publish(topic, message);
        });
        
        // Subscribe button
        document.getElementById('subscribe').addEventListener('click', () => {
            if (!client) {
                log('Not connected to broker', 'error');
                return;
            }
            
            const topic = document.getElementById('subscribeTopic').value;
            
            log(`Subscribing to ${topic}`);
            client.subscribe(topic, (error) => {
                if (error) {
                    log(`Error subscribing to ${topic}: ${error.message}`, 'error');
                } else {
                    log(`Subscribed to ${topic}`, 'success');
                    document.getElementById('unsubscribe').disabled = false;
                }
            });
        });
        
        // Unsubscribe button
        document.getElementById('unsubscribe').addEventListener('click', () => {
            if (!client) {
                log('Not connected to broker', 'error');
                return;
            }
            
            const topic = document.getElementById('subscribeTopic').value;
            
            log(`Unsubscribing from ${topic}`);
            client.unsubscribe(topic, (error) => {
                if (error) {
                    log(`Error unsubscribing from ${topic}: ${error.message}`, 'error');
                } else {
                    log(`Unsubscribed from ${topic}`, 'success');
                    document.getElementById('unsubscribe').disabled = true;
                }
            });
        });
    </script>
</body>
</html>
