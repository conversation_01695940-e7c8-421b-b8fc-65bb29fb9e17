{"name": "remotecarcontrol", "version": "1.0.0", "description": "This is for online remote car control", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "devDependencies": {"nodemon": "^2.0.15"}, "dependencies": {"aws-sdk": "^2.1292.0", "axios": "^0.26.0", "bcryptjs": "^2.4.3", "body-parser": "^1.19.1", "concurrently": "^6.5.1", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.18.2", "firebase-admin": "^11.5.0", "form-data": "^4.0.0", "is-empty": "^1.2.0", "jsonwebtoken": "^8.5.1", "license-key-generator": "^1.1.2", "moment": "^2.29.1", "mongoose": "^6.1.4", "mqtt": "^4.3.7", "multer": "^1.4.4", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "npm": "^9.6.2", "numeral": "^2.0.6", "otp-generator": "^4.0.0", "passport": "^0.5.2", "passport-jwt": "^4.0.0", "path": "^0.12.7", "pusher": "^5.0.1", "request": "^2.88.2", "socket.io": "^4.4.1", "validator": "^13.7.0", "web-vitals": "^2.1.2"}, "repository": {"type": "git", "url": "git+https://github.com/alexidr9116/remotecarcontrol.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/alexidr9116/remotecarcontrol.git/issues"}, "homepage": "https://github.com/alexidr9116/remotecarcontrol.git#readme"}