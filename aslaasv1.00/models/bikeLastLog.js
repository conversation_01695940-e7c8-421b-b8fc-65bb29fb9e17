const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const BikeLastLogSchema = new Schema({
    bike: {
        type: Schema.Types.ObjectId,
        ref: "Bike"
    },
    timestamp: {
        type: Date,
        default: Date.now()
    },
    latitude: {
        type: String,
        required: true
    },
    longitude: {
        type: String,
        required: true
    }
});

module.exports = BikeMessageLog = mongoose.model("BikeLastLog", BikeLastLogSchema);