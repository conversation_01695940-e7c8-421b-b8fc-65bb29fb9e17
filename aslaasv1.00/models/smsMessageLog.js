const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const SmsLogSchema = new Schema({
    user: {
        type: String,
    },
    deviceNumber: {
        type: String,
        default: ""
    },
    command: {
        type: String,
        default: ""
    },
    sent: {
        type: String,
        default: "no"
    },
    sentTime: {
        type: Date,
    },
    createdAt: {
        type: Date,
        default: Date.now,
    }
}, { capped: { size: 102400, max: 100000, autoIndexId: true } });
module.exports = SmsLog = mongoose.model("smsMessageLog", SmsLogSchema);