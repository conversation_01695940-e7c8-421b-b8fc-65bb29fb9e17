const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const DriverSchema = new Schema({
    phoneNumber: {
        type: String,
        default: ""
    },
    index: {
        type: String,
        default: '1st' //   1st, 2nd,3rd
    },
    type: {
        type: String,
        default: "4g",
    },
    owner: {
        type: mongoose.Types.ObjectId,

    },
    createdAt: {
        type: Date,
        default: Date.now()
    },
    status: {
        type: String,
        default: "active",
    },
    deviceNumber:{
        type:String,
        default:''
    }
});
module.exports = Driver = mongoose.model("driver", DriverSchema);