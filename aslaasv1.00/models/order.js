const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const OrderSchema = new Schema({
    phoneNumber: {
        type: String,
        default: ""
    },
    CarModel: {
        type: String,
        default: ""
    },
    AvialableTime: {
        type: String,
        default: "",
    },
    address: {
        type: String,
        default: "",
    },

    createdAt: {
        type: Date,
        default: Date.now
    },
    isSpareKey: {
        type: Boolean,
        default: false,
    },
    isInstalled: {
        type: Boolean,
        default: false,
    },
    invoiceId:{
        type:String,
        defalut:''
    },
    realInvoiceId:{
        type:String,
        defalut:''
    },
    paid:{
        type:Boolean,
        default:false
    }

});
module.exports = Order = mongoose.model("order", OrderSchema);