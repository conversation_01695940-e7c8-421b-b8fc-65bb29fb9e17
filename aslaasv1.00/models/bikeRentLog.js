const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const BikeRentLogSchema = new Schema({
    bike: {
        type: Schema.Types.ObjectId,
        ref: 'Bike'
    },
    renter: {
        type: Schema.Types.ObjectId,
        ref: 'User'
    },
    from: {
        type: Number,
        default: 0,
    },
    to: {
        type: Number,
        default: 0,
    },
    mode: {
        type: String,
        default: 'minute'
    },
    created: {
        type: Number,
        default: Date.now()
    }
});

module.exports = BikeRentLog = mongoose.model("BikeRentLog", BikeRentLogSchema);