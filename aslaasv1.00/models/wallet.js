const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const WalletSchema = new Schema({
    user: {
        type: String,
        default: ""
    },
    currentBalance:{
        type:Number,
        default:0
    },
    bankAccount:{
        type:String,
        default:''
    },
    bankName:{
        type:String,
        default:'',

    },
    transactions:{
        type:Array,
    },
    requests:{
        type:Array,
    },
    created:{
        type:Date,
        default:Date.now
    }
});

module.exports = Wallet = mongoose.model("Wallet", WalletSchema);