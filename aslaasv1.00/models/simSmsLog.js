const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const SimcardSmsLogSchema = new Schema({
    deviceNumber: {
        type: String,
        default: ""
    },
    content: {
        type: "String",
        default: "",
    },
    read: {
        type: Boolean,
        default: false,
    },
    received: {
        type: Date,
        default: Date.now
    },
    expired:{
        type:Date,
        
    },
    balance:{
        type:Number
    },
    capacity:{
        type:Number,
    }
});
module.exports = SimcardSmsLog = mongoose.model("simcardSmsLog", SimcardSmsLogSchema);