const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const GpsLogSchema = new Schema({
    
    imei: {
        type: String,
        default: ""
    },
    
    createdAt: {
        type: Date,
        default: Date.now
    },
    payload:{
        type:String,
        default:''
    },
    lat:{
        type:Number,
    },
    lng:{
        type:Number,
    },
    battery:{
        type:Number,
    },
    bles:{
        type:Array,

    }
});
module.exports = GpsLog = mongoose.model("gpslog", GpsLogSchema);