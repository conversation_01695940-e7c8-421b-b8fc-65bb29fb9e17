const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const DeviceSchema = new Schema({
    deviceName: {
        type: String,
        default: ''
    },

    phoneNumber: {
        type: String,
        default: ""
    },
    deviceNumber: {
        type: String,
        default: ""
    },
    type: {
        type: String,
        default: "4g",
    },
    uix: {
        type: String,
        default: "CarV1.2",
    },
    createdAt: {
        type: Date,
        default: Date.now()
    },
    status: {
        type: String,
        default: "active",
    },
    isDefault: {
        type: Boolean,
        default: true,
    },
    interval: {
        type: Number,
        default: 10 * 60
    },
    bles: {
        type: Array,
    },
    subscribed: {
        type: Boolean,
        default: false,
    },
    rentable: {
        type: Boolean,
        default: false,
    },
    rented: {
        type: Boolean,
        default: false,
    },
    renter: {
        type: String,
        default: "",
    },
    fmctoken: {
        type: String,
        default: 0,
    }
});

module.exports = Device = mongoose.model("device", DeviceSchema);