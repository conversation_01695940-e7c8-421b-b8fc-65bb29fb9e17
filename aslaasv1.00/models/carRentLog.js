const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const CarRentLogSchema = new Schema({
    deviceNumber: {
        type: String,
        default: ""
    },
    deviceName:{
        type:String,
        default:''
    },
    renter:{
        type: String,
        default:''
    },
    from:{
        type:Number,
        default:0,
    },
    to:{
        type:Number,
        default:0,
    },
    mode:{
        type:String,
        default:'minute'
    },
    created:{
        type:Number,
        default:Date.now()
    }
});

module.exports = CarRentLog = mongoose.model("CarRentLog", CarRentLogSchema);