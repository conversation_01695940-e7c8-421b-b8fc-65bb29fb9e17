const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const CarRentDetailLogSchema = new Schema({
    deviceNumber: {
        type: String,
        default: ""
    },
    renter:{
        type: mongoose.Types.ObjectId,
    },
    status: {
        type: String,
        default: "success",
    },
    command:{
        type:String,
        default:""
    },
    sendTime:{
        type:Number,
        default:Date.now()
    },
    receiveTime:{
        type:Number,
        default:0,
    },
    response:{
        type:String,
        default:''
    },
    responseType:{
        type:String,
        default:""
    },
    
    
});

module.exports = CarRentDetailLog = mongoose.model("CarRentDetailLog", CarRentDetailLogSchema);