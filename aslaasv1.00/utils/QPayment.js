const axios = require('axios');
const QPAY_API_URL = process.env.QPAY_API_URL;
const ResponseData = require("./ResponseData");
const FormData = require('form-data');

const INVOICE_VS_REAL_INVOICE_ID = [];


const QPay = () => {
    const _getEbarimt = async(token, payment_id) => {

        const formData = new FormData();
        formData.append("payment_id", payment_id);
        formData.append("ebarimt_receiver_type", "CITIZEN");
        const response = await axios.post(`${QPAY_API_URL}ebarimt/create`,
            formData, {
                headers: {
                    ...formData.getHeaders(),
                    "Content-Length": formData.getLengthSync(),
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': '"multipart/form-data"'
                }
            });
        try {
            return { status: 200, data: response.data }
        } catch (err) {
            console.log(err);
            return { status: 500 }
        }
    }
    const _paymentCheck = async(token, invoice_id) => {
        try {

            const checkParam = {
                "object_type": "INVOICE",
                "object_id": invoice_id,
                "offset": {
                    "page_number": 1,
                    "page_limit": 100
                }
            }
            const response = await axios.post(`${QPAY_API_URL}payment/check`,
                checkParam, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            if (response.status === 200) {
                return { result: response.data, status: 200 };
            } else {
                return { result: response.data, status: 201 };
            }
        } catch (err) {
            console.log(err)
            return { status: 500, result: err };
        }
    }
    const _payWithToken = async(invoice, token) => {
        const response = await axios.post(`${QPAY_API_URL}invoice`,
            invoice, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
        if (response.status === 200) {
            //  console.log(response.data)
            return response.data;
        } else {
            return null;
        }

    }
    const _getAuthToken = async() => {
        const config = {
            auth: {
                username: process.env.QPAY_AUTH_TOKEN_USERNAME,
                password: process.env.QPAY_AUTH_TOKEN_PASSWORD
            }
        };

        const response = await axios.post(
            `${QPAY_API_URL}auth/token`, {}, config
        );

        if (response.status == 200 && response.data.access_token) {

            return response.data.access_token;
        }
        return null;
    }
    return {
        getAuthToken: _getAuthToken,
        payWithToken: _payWithToken,
        paymentCheck: _paymentCheck,
        getEbarimt: _getEbarimt,
    }
}
const getBankList = async(req, res, user) => {
    const { page } = req.body;

    const userid = (typeof user == 'string' ? user : ((req.user._id)));
    try {
        let days = 0;
        let pricePerMonth = process.env.PRICE_PER_MONTH || 5000;
        let totalCost = req.body.totalCost;
        if (page && page == 'order') {
            totalCost = 20000;
        } 
        else if(page && page == 'balance'){
            
        }
        else
            days = parseFloat(req.body.totalCost) / parseFloat(pricePerMonth);

        const token = await QPay().getAuthToken();
        if (token == null) {
            if (page && page == 'order') {
                return { success: false, message: 'can not get qpay token' };
            }
            if (page && page == 'balance') {
                return { success: false, message: 'can not get qpay token' };
            }
            return ResponseData.error(res, "Can not get QPay API Token");
        }
        const invoice_number = Date.now();

        const lines = [{
            line_description: `Order No ${invoice_number} ${totalCost}`,
            line_quantity: 1.00,
            line_unit_price: totalCost,
            taxes: [{
                tax_code: "VAT",
                description: "ebarimt",
                amount: 0,
                note: "ebarimt"
            }]
        }]
        const phone_number = (page && (page == 'order' || page == 'balance')) ? (req.body.phoneNumber) : '0000';
        const invoice = {
            invoice_code: 'ELEC_MN_INVOICE',
            sender_invoice_no: `INV-${invoice_number}`,
            invoice_receiver_code: `REC-${invoice_number}`,
            invoice_description: `${(page&&page=='order')?'Ordering Prepayment Amount':'Payment fee for use system'}`,
            sender_branch_code: process.env.QPAY_BRANCH_CODE,
            amount: totalCost,
            callback_url: `${process.env.HOST_PAYMENT_HOOK_URL}/${page?page:'license'}/INV-${invoice_number}/${totalCost}/${userid}/${phone_number}`,
            lines,

        }
        console.log(invoice);
        const bankList = await QPay().payWithToken(invoice, token);
        if (bankList != null) {
            INVOICE_VS_REAL_INVOICE_ID[invoice.sender_invoice_no] = bankList.invoice_id;
            if (page == 'order') {
                return { success: true, invoice: invoice.sender_invoice_no, bankList };
            }
            return ResponseData.ok(res, "Success got bank list", { invoice: invoice.sender_invoice_no, bankList });
        } else {
            if (page == 'order') {
                return { success: false, message: 'Error while connect QPay API' };
            }
            return ResponseData.error(res, "Error while connect QPay API", {});
        }
    } catch (err) {
        console.log(err);
        if (page == 'order') {
            return { success: false, message: 'Server Error' };
        }
        return ResponseData.error(res, "Server err", { err });
    }
}
const getEbarimt = async(req, res) => {
    try {
        const token = await QPay().getAuthToken();
        const paymentCheck = await QPay().paymentCheck(token, req.body.invoice_id);
        // const ebarimt = await QPay().getEbarimt(token, req.body.invoice_id);
        if (paymentCheck.status === 200) {

            const payment_id = paymentCheck.result.rows[0].payment_id;
            const ebarimt = await QPay().getEbarimt(token, payment_id);
            if (ebarimt.status === 200) {
                return ResponseData.ok(res, "", { ebarimt });
            } else {
                return ResponseData.error(res, "Can not get Ebarimt data", { ebarimt });
            }
        } else {
            return ResponseData.error(res, "Can not check payment API", { paymentCheck });
        }

    } catch (err) {
        return ResponseData.error(res, "Unknown error", { err });
    }

}

module.exports = { QPay, getEbarimt, getBankList, INVOICE_VS_REAL_INVOICE_ID }