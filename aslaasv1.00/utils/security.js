const crypto = require('crypto');

// Encryption key from environment variable
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || crypto.randomBytes(32);
const ALGORITHM = 'aes-256-gcm';

/**
 * Encrypt sensitive data like 2FA secrets
 * @param {string} text - Text to encrypt
 * @returns {string} - Encrypted text with IV and auth tag
 */
const encrypt = (text) => {
    try {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
        cipher.setAAD(Buffer.from('2fa-secret'));
        
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        // Combine IV, auth tag, and encrypted data
        return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    } catch (error) {
        console.error('Encryption error:', error);
        throw new Error('Failed to encrypt data');
    }
};

/**
 * Decrypt sensitive data
 * @param {string} encryptedData - Encrypted data with IV and auth tag
 * @returns {string} - Decrypted text
 */
const decrypt = (encryptedData) => {
    try {
        const parts = encryptedData.split(':');
        if (parts.length !== 3) {
            throw new Error('Invalid encrypted data format');
        }
        
        const iv = Buffer.from(parts[0], 'hex');
        const authTag = Buffer.from(parts[1], 'hex');
        const encrypted = parts[2];
        
        const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
        decipher.setAAD(Buffer.from('2fa-secret'));
        decipher.setAuthTag(authTag);
        
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    } catch (error) {
        console.error('Decryption error:', error);
        throw new Error('Failed to decrypt data');
    }
};

/**
 * Hash backup codes for secure storage
 * @param {string} code - Backup code to hash
 * @returns {string} - Hashed code
 */
const hashBackupCode = (code) => {
    return crypto.createHash('sha256').update(code + process.env.BACKUP_CODE_SALT || 'default-salt').digest('hex');
};

/**
 * Verify backup code against hash
 * @param {string} code - Plain backup code
 * @param {string} hash - Stored hash
 * @returns {boolean} - Whether code matches hash
 */
const verifyBackupCode = (code, hash) => {
    const codeHash = hashBackupCode(code);
    return crypto.timingSafeEqual(Buffer.from(codeHash), Buffer.from(hash));
};

/**
 * Generate secure random string for backup codes
 * @param {number} length - Length of the string
 * @returns {string} - Random string
 */
const generateSecureRandom = (length = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    const randomBytes = crypto.randomBytes(length);
    
    for (let i = 0; i < length; i++) {
        result += chars[randomBytes[i] % chars.length];
    }
    
    return result;
};

/**
 * Create audit log entry for 2FA events
 * @param {string} userId - User ID
 * @param {string} action - Action performed
 * @param {string} result - Result of action
 * @param {string} ipAddress - IP address
 * @param {string} userAgent - User agent
 * @param {Object} metadata - Additional metadata
 */
const createAuditLog = async (userId, action, result, ipAddress, userAgent, metadata = {}) => {
    try {
        const logEntry = {
            userId,
            action,
            result,
            ipAddress,
            userAgent,
            metadata,
            timestamp: new Date(),
            sessionId: metadata.sessionId || null
        };
        
        // In a production environment, you might want to store this in a separate audit log collection
        console.log('AUDIT LOG:', JSON.stringify(logEntry, null, 2));
        
        // You could also send this to an external logging service
        // await sendToAuditService(logEntry);
        
        return logEntry;
    } catch (error) {
        console.error('Failed to create audit log:', error);
    }
};

/**
 * Rate limiting helper - check if action is allowed
 * @param {string} key - Unique key for rate limiting
 * @param {number} maxAttempts - Maximum attempts allowed
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Object} - Rate limit status
 */
const checkRateLimit = (key, maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    // This is a simple in-memory rate limiter
    // In production, you should use Redis or a proper rate limiting service
    
    if (!global.rateLimitStore) {
        global.rateLimitStore = new Map();
    }
    
    const now = Date.now();
    const record = global.rateLimitStore.get(key);
    
    if (!record) {
        global.rateLimitStore.set(key, {
            attempts: 1,
            resetTime: now + windowMs
        });
        return { allowed: true, remaining: maxAttempts - 1, resetTime: now + windowMs };
    }
    
    if (now > record.resetTime) {
        // Reset the window
        global.rateLimitStore.set(key, {
            attempts: 1,
            resetTime: now + windowMs
        });
        return { allowed: true, remaining: maxAttempts - 1, resetTime: now + windowMs };
    }
    
    if (record.attempts >= maxAttempts) {
        return { 
            allowed: false, 
            remaining: 0, 
            resetTime: record.resetTime,
            retryAfter: record.resetTime - now
        };
    }
    
    record.attempts++;
    global.rateLimitStore.set(key, record);
    
    return { 
        allowed: true, 
        remaining: maxAttempts - record.attempts, 
        resetTime: record.resetTime 
    };
};

/**
 * Clean up expired rate limit entries
 */
const cleanupRateLimit = () => {
    if (!global.rateLimitStore) return;
    
    const now = Date.now();
    for (const [key, record] of global.rateLimitStore.entries()) {
        if (now > record.resetTime) {
            global.rateLimitStore.delete(key);
        }
    }
};

// Clean up rate limit store every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000);

/**
 * Validate TOTP token format
 * @param {string} token - TOTP token
 * @returns {boolean} - Whether token format is valid
 */
const validateTOTPFormat = (token) => {
    return /^\d{6}$/.test(token);
};

/**
 * Validate backup code format
 * @param {string} code - Backup code
 * @returns {boolean} - Whether code format is valid
 */
const validateBackupCodeFormat = (code) => {
    return /^[A-F0-9]{8}$/.test(code);
};

/**
 * Generate session token for 2FA verification
 * @returns {string} - Session token
 */
const generateSessionToken = () => {
    return crypto.randomBytes(32).toString('hex');
};

module.exports = {
    encrypt,
    decrypt,
    hashBackupCode,
    verifyBackupCode,
    generateSecureRandom,
    createAuditLog,
    checkRateLimit,
    cleanupRateLimit,
    validateTOTPFormat,
    validateBackupCodeFormat,
    generateSessionToken
};
