const Validator = require("validator");
const isEmpty = require("is-empty");
module.exports = function validatePinCodeInput(data) {
  let errors = {};
// Convert empty fields to an empty string so we can use validator functions
  data.pinCode = !isEmpty(data.pinCode) ? data.pinCode : "";  
// Password checks
  if (Validator.isEmpty(data.pinCode)) {
    errors.pinCode = "Pin code field is required";
  }
return {
    errors,
    isValid: isEmpty(errors)
  };
};