const jwt = require("jsonwebtoken");
const keys = require("../config/keys");
const Device = require("../models/device");
const firebaseAdmin = require("firebase-admin");
const axios = require('axios');

const ADMIN_PHONE_NUMBER = process.env.ADMIN_PHONE_NUMBER;
const otpGenerator = require("otp-generator");
// Load input validation
const validateLoginInput = require("../validation/login");
const { sendOtp } = require("../utils/channel");
const User = require("../models/user");
const Wallet = require("../models/wallet");
const otps = [];
const messaging = firebaseAdmin.messaging();

const jwtsign = (payload) => {
    // Sign token
    return jwt.sign(payload, keys.secretOrKey, {
        expiresIn: 31556926, // 1 year in seconds
    });
};


const getRealIpAddress = (req) => {
    // Check for the 'X-Forwarded-For' header, which is used by proxies
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    return ip;
};

// Modify the getGeolocation function to handle real IP address and `::1`
const getGeolocation = async (ip) => {
    // Check if the IP is a loopback address like `::1`
    if (ip === '::1') {
        console.log("Localhost detected, skipping geolocation lookup.");
        return null;  // You can either skip or mock geolocation for testing
    }

    try {
        const response = await axios.get(`https://ipinfo.io/${ip}/json?token=${API_KEY}`);
        return response.data;
    } catch (error) {
        console.log("Error fetching geolocation data:", error);
        return null;
    }
};

const API_KEY = process.env.IPINFO;

const EMQX_NODES = {
  "snode1": process.env.EMQX_SNODE1 || "*************",      // Snode1 (China)
  "node1": process.env.EMQX_NODE1 || "bot.elec.mn",         // Node1 
  "node2": process.env.EMQX_NODE2 || "app.elec.mn",         // Node2
  "node3": process.env.EMQX_NODE3 || "elec.mn"              // Node3 default
};
const EMQX_API_PATH = "/api/v4/clients/"; // Updated API path
const EMQX_API_PORT = "8081"; // Default EMQX API port
const EMQX_API_USERNAME = process.env.MQTT_USER_NAME;
const EMQX_API_PASSWORD = process.env.MQTT_USER_PWD;

const checkClientConnectivity = async (clientId) => {
  // First check node1, node2, and node3 (standard API)
  const standardNodes = {
    "node1": EMQX_NODES["node1"],
    "node2": EMQX_NODES["node2"],
    "node3": EMQX_NODES["node3"]
  };
  
  // Check standard nodes first
  for (const [nodeName, nodeDomain] of Object.entries(standardNodes)) {
    try {
      const url = `http://${nodeDomain}:${EMQX_API_PORT}${EMQX_API_PATH}${clientId}`;
      const response = await axios.get(url, {
        auth: {
          username: EMQX_API_USERNAME,
          password: EMQX_API_PASSWORD
        }
      });
      
      // Check if the response contains data and the data array is not empty
      if (response.status === 200 && response.data && response.data.data && response.data.data.length > 0) {
        // Extract the node name from the response to determine which server the client is connected to
        const clientData = response.data.data[0];
        const emqxNode = clientData.node; // e.g., "<EMAIL>"
        
        // Map the EMQX node name to our node configuration
        let actualNodeName = nodeName;
        let actualNodeDomain = nodeDomain;
        
        // Parse the node number from the EMQX node name
        if (emqxNode.includes("emqx1")) {
          actualNodeName = "node1";
          actualNodeDomain = EMQX_NODES["node1"];
        } else if (emqxNode.includes("emqx2")) {
          actualNodeName = "node2";
          actualNodeDomain = EMQX_NODES["node2"];
        } else if (emqxNode.includes("emqx3")) {
          actualNodeName = "node3";
          actualNodeDomain = EMQX_NODES["node3"];
        } else if (emqxNode.includes("snode")) {
          actualNodeName = "snode1";
          actualNodeDomain = EMQX_NODES["snode1"];
        }
        
        console.log(`Client ${clientId} found on node ${actualNodeName} (${actualNodeDomain}), EMQX node: ${emqxNode}`);
        return { connected: true, node: actualNodeName, ip: actualNodeDomain };
      } else {
        console.log(`Client ${clientId} not found on node ${nodeName}`);
      }
    } catch (error) {
      console.log(`Error checking node ${nodeName} for client ${clientId}: ${error.message}`);
    }
  }
  
  // If not found on any standard node, use node3 as the default
  const defaultNode = "node3";
  const defaultDomain = EMQX_NODES[defaultNode];
  console.log(`Client ${clientId} not connected to any standard node. Assigning default node: ${defaultNode} (${defaultDomain})`);
  return { connected: false, node: defaultNode, ip: defaultDomain };
};

const verifyPincode = async (req, res) => {
    let remainDays = 0;
    const pinCode = req.body.pinCode;
    const phoneNumber = req.body.phoneNumber;
    const ipAddress = getRealIpAddress(req);
    
    // Start geolocation fetch early and in parallel
    const geolocationPromise = getGeolocation(ipAddress);
    
    try {
        // Find the user
        const user = await User.findOne({ phoneNumber });
        
        if (!user) {
            return res.status(404).json({ success: false, message: "User not found" });
        }
        
        if (user.pinCode != pinCode) {
            return res.status(200).json({ success: false, message: "wrong pin code!" });
        }

        const payload = {
            id: user._id,
            phoneNumber: user.phoneNumber,
        };

        const token = jwtsign(payload);
        
        // Run these queries in parallel - removed wallet query
        const [devices, device] = await Promise.all([
            Device.find({ phoneNumber: user.phoneNumber }),
            Device.findOne({ phoneNumber: user.phoneNumber, isDefault: true })
        ]);
        
        // Filter default devices
        const defaultDevices = devices.filter(d => d.isDefault);
        
        // Complete geolocation fetch if needed
        const geolocation = await geolocationPromise;
        if (geolocation) {
            console.log("Device location:", geolocation);
        } else {
            console.log("Could not retrieve geolocation data.");
        }
        
        // Skip connectivity check and always use default node (elec.mn)
        const defaultNode = "node3";
        const defaultDomain = EMQX_NODES[defaultNode]; // This should be "elec.mn"
        
        // Update the device with the default node domain if device exists
        if (device) {
            // Only update if renter is empty
            if (!device.renter) {
                await Device.findByIdAndUpdate(device._id, { 
                    renter: defaultDomain
                });
            }
        }
        
        // Calculate status and remaining days
        let status = user.status;
        if (user.phoneNumber != ADMIN_PHONE_NUMBER) {
            if (user.expired) {
                const offset = new Date(user.expired).getTime() - Date.now();
                if (offset < 0) {
                    status = "expired";
                } else {
                    if (user.licenseKey == undefined || user.licenseKey == "") {
                        status = "trial";
                    }
                    remainDays = offset;
                }
            } else {
                status = "trial";
            }
        }
        
        // Send the response with updated device information
        return res.status(200).json({
            success: true,
            message: "Pin code verify successfully",
            token,
            user: {
                phoneNumber: user.phoneNumber,
                pinCode: user.pinCode,
                role: user.role,
                status: status,
                device:
                    defaultDevices != null &&
                    defaultDevices.length > 0 &&
                    defaultDevices[0].deviceNumber != ""
                        ? defaultDevices[0]
                        : null,
                devices,
                licenseKey: user.licenseKey,
                expired: user.expired,
                username: user.username,
                remainDays: remainDays,
                _id: user._id,
                driverLicenseFile: user?.driverLicenseFile || "",
                driverLicenseVerification: user?.driverLicenseVerification || 0,
                address: user?.address || "",
                description: user?.description || "",
                balance: user?.balance || 0,
                wallet: null // Set wallet to null by default
            },
        });
    } catch (error) {
        console.error("Error in verifyPincode:", error);
        return res.status(500).json({ success: false, message: "Server error" });
    }
};

const login = async (req, res) => {
    // Form validation
    const { errors, isValid } = validateLoginInput(req.body);
    // Check validation
    if (!isValid) {
        return res.status(200).json({ success: false, errors });
    }
    const phoneNumber = req.body.phoneNumber;
    const ipAddress = getRealIpAddress(req);  // Get the real IP address of the client
    const geolocation = await getGeolocation(ipAddress);

    if (geolocation) {
        console.log("Device location:", geolocation);
        // Store geolocation data or log it for auditing purposes
    } else {
        console.log("Could not retrieve geolocation data.");
    }

    // Find user by email
    User.findOne({ phoneNumber }).then(async (user) => {
        try {
            // Check if user exists
            if (!user) {
                otps[phoneNumber] = "444888";
                // opt send
                const result = await sendOtp(phoneNumber, otps[phoneNumber]);

                if (result != null)
                    return res.json({
                        code: "200",
                        error: "not found",
                        message: "sent opt code",
                    });
                else {
                    return res.json({
                        code: "404",
                        error: "not found",
                        message: "not sent opt code, try again",
                    });
                }
            }

            const isPinCode = user.pinCode && user.pinCode != "";
            let remainDays = 0;
            if (isPinCode) {
                return res.status(200).json({ pinVerify: isPinCode });
            } else {
                const payload = {
                    id: user._id,
                    phoneNumber: user.phoneNumber,
                };

                const token = jwtsign(payload);
                const devices = await Device.find({ phoneNumber: user.phoneNumber });
                const wallet = await Wallet.findOne({ user: `${user._id}` });

                const defaultDevices = devices.filter((d) => d.isDefault);
                let status = user.status;
                if (user.phoneNumber != ADMIN_PHONE_NUMBER) {
                    if (user.expired) {
                        const offset = new Date(user.expired).getTime() - Date.now();
                        if (offset < 0) {
                            status = "expired";
                        } else {
                            if (user.licenseKey == undefined || user.licenseKey == "") {
                                status = "trial";
                            }
                            remainDays = offset;
                        }
                    } else {
                        status = "trial";
                    }
                }

                // Add logging to track the device query
                console.log("Looking for device for user:", user.phoneNumber);
                let device = await Device.findOne({
                    phoneNumber: user.phoneNumber,
                    isDefault: true,
                });
                if (device) {
                    console.log("Device found for user:", user.phoneNumber);
                } else {
                    console.log("No device found for user:", user.phoneNumber);
                }

                // Default node domain (elec.mn)
                const defaultNode = "node3";
                const defaultDomain = EMQX_NODES[defaultNode];

                // Update device renter field if it's empty
                if (device && device.deviceNumber) {
                    if (!device.renter) {
                        await Device.findByIdAndUpdate(device._id, { 
                            renter: defaultDomain
                        });
                        
                        // Fetch the updated device
                        device = await Device.findById(device._id);
                    }
                }

                // Retrieve the fmcToken from the user's device
                if (device && device.fmctoken) {
                    const fmcToken = device.fmctoken; // Get fmcToken from the device
                    const message = "Таны хаягаар нэвтрэх үйлдэл хийгдлээ!";
                    sendMessageFirebase(message, fmcToken, geolocation); // Pass geolocation data
                    console.log(
                        "Login notification sent for phoneNumber:",
                        user.phoneNumber
                    );
                } else {
                    console.log(
                        "No device or fmcToken found for this user. Skipping notification."
                    );
                }
                return res.status(200).json({
                    pinVerify: isPinCode,
                    token,
                    user: {
                        phoneNumber: user.phoneNumber,
                        pinCode: "",
                        role: user.role,
                        status: status,
                        device:
                            defaultDevices != null &&
                                defaultDevices.length > 0 &&
                                defaultDevices[0].deviceNumber != ""
                                ? defaultDevices[0]
                                : null,
                        devices,
                        licenseKey: user.licenseKey,
                        expired: user.expired,
                        username: user.username,
                        remainDays: remainDays,
                        _id: user._id,
                        driverLicenseFile: user?.driverLicenseFile || "",
                        driverLicenseVerification: user?.driverLicenseVerification || 0,
                        address: user?.address || "",
                        description: user?.description || "",
                        balance: user?.balance || 0,
                        wallet
                    },
                });
            }
        } catch (e) {
            console.log(e, "lll"); // caught
        }
    });
};

const verifyOtp = async (req, res) => {
    const { phoneNumber, otp } = req.body;
    try {
        // Fallback to "000000" if no OTP was ever set
        const expectedOtp = otps[phoneNumber] || "444888";

        if (expectedOtp === otp) {
            return res.status(200).json({ success: true });
        } else {
            return res.status(200).json({ success: false, err: "wrong otp" });
        }
    } catch (err) {
        return res.status(200).json({ success: false, err });
    }
};

const register = async (req, res) => {
    const { errors, isValid } = validateLoginInput(req.body);
    // Check validation
    if (!isValid) {
        return res.status(400).json(errors);
    }
    User.findOne({ phoneNumber: req.body.phoneNumber }).then((user) => {
        if (user) {
            return res.json({
                phoneNumber: "phoneNumber already exists",
                status: 501,
            });
        } else {
            const newUser = new User({
                phoneNumber: req.body.phoneNumber,
                username: req.body.phoneNumber,
                role: ADMIN_PHONE_NUMBER == req.body.phoneNumber ? "admin" : "user",
            });
            newUser
                .save()
                .then((user) => {
                    const payload = { id: user._id, phoneNumber: user.phoneNumber };
                    const token = jwtsign(payload);
                    const device = new Device({
                        phoneNumber: user.phoneNumber,
                    });
                    device.save().then((result) => {
                        res.status(200).json({
                            token,
                            success: true,
                            user: {
                                phoneNumber: req.body.phoneNumber,
                                status: user.status,
                                role: user.role,
                                device: null,
                                devices: [],
                                licenseKey: null,
                                username: req.body.phoneNumber,
                                expired: 0,
                                remainDays: 30 * 3600 * 24 * 1000,
                                _id: user._id,
                                wallet: null,
                                driverLicenseFile: "",
                                driverLicenseVerification: 0,
                                address: "",
                                description: "",
                                balance: 0,
                            },
                        });
                    });
                })
                .catch((err) => {
                    console.log(err);
                    res.status(200).json({
                        success: false,
                        err: err,
                    });
                });
        }
    });
};
const sendMessageFirebase = async (message, fmcToken, geolocation) => {
    if (!fmcToken) {
        console.log("No FCM token provided, skipping message send.");
        return;
    }

    try {
        // Check if geolocation is valid before attempting to access properties
        const city = geolocation && geolocation.city ? geolocation.city : "Unknown City";
        const loc = geolocation && geolocation.loc ? geolocation.loc : "Unknown Location";
        
        const messageWithLocation = `${message} - Logged in from ${city}, ${loc}`;

        const messagePayload = {
            notification: {
                title: "Нэвтрэлт",
                body: messageWithLocation,
            },
            token: fmcToken,
        };

        // Send the notification
        await messaging.send(messagePayload);
        console.log("Login notification sent with location for phoneNumber.");
    } catch (error) {
        console.log("Error sending Firebase message:", error);
    }
};
module.exports = {
    login,
    verifyOtp,
    register,
    verifyPincode,
};
