const express = require('express');
const router = express.Router();
const Bike = require('../models/bike');
const BikeLastLog = require('../models/bikeLastLog');
const BikeRentLog = require('../models/bikeRentLog');
const User = require('../models/user');
const mongoose = require('mongoose');
const ObjectId = require('mongodb').ObjectId;
const WalletModel = require("../models/wallet");

const {
    getBikeRentPrice,
} = require("../utils/rentService");

// Create a new bike
const createBike = async(req, res) => {
    try {
        const bike = new Bike(req.body);
        await bike.save();
        res.status(201).send(bike);
    } catch (err) {
        res.status(400).send(err);
    }
};

// Get all bikes
const getAllBikes = async(req, res) => {
    try {
        const bikes = await Bike.find({});
        res.send(bikes);
    } catch (err) {
        res.status(500).send(err);
    }
};

// Get a bike by ID
const getBikeById = async(req, res) => {
    try {
        const bike = await Bike.findById(req.params.id);
        if (!bike) {
            res.status(404).send();
        }
        res.send(bike);
    } catch (err) {
        res.status(500).send(err);
    }
};

// Update a bike by ID
const updateBikeById = async(req, res) => {
    try {
        const bike = await Bike.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true,
        });
        if (!bike) {
            res.status(404).send();
        }
        res.send(bike);
    } catch (err) {
        res.status(400).send(err);
    }
};

// Delete a bike by ID
const deleteBikeById = async(req, res) => {
    try {
        const bike = await Bike.findByIdAndDelete(req.params.id);
        if (!bike) {
            res.status(404).send();
        }
        res.send(bike);
    } catch (err) {
        res.status(500).send(err);
    }
};

// Create a new bike message log
const createBikeMessageLog = async(req, res) => {
    try {
        const bike = await Bike.findById(req.params.id);
        if (!bike) {
            res.status(404).send();
        }
        const log = new BikeMessageLog({
            bike: bike._id,
            payload: req.body.payload,
        });
        await log.save();
        res.status(201).send(log);
    } catch (err) {
        res.status(400).send(err);
    }
};

// Get all bike message logs for a bike by ID
const getAllBikeMessageLogsById = async(req, res) => {
    try {
        const bike = await Bike.findById(req.params.id);
        if (!bike) {
            res.status(404).send();
        }
        const logs = await BikeMessageLog.find({ bike: bike._id });
        res.send(logs);
    } catch (err) {
        res.status(500).send(err);
    }
};

const searchRentBikes = async(req, res) => {
    try {
        const rentableBikes = await Bike.find({ rentable: true });
        const ids = rentableBikes.map((bike) => bike._id);
        const logs = await BikeRentLog.aggregate([{
                $match: {
                    renter: mongoose.Types.ObjectId(req.user._id),
                    to: 0,
                    from: { $gt: 0 }, // filter out logs that have already ended
                },
            },
            {
                $sort: { from: -1 },
            },
            {
                $group: {
                    _id: "$renter",
                    log: { $first: "$$ROOT" },
                },
            },
            {
                $lookup: {
                    from: "bikes",
                    localField: "log.bike",
                    foreignField: "_id",
                    as: "bike",
                },
            },
            {
                $unwind: "$bike"
            },
            {
                $project: {
                    _id: 0,
                    offset: {
                        $cond: {
                            if: {
                                $and: [{ $eq: ["$log.to", 0] }, { $gt: ["$log.from", 0] }],
                            },
                            then: { $subtract: [Date.now(), "$log.from"] },
                            else: 0,
                        },
                    },
                    rentBike: "$bike.Bikemac",
                    payMode: "$log.mode",
                },
            },
        ]);
        const rentable = logs.length > 0;
        return res.status(200).json({
            success: true,
            bikeLogs: [],
            offset: rentable ? logs[0].offset : 0,
            rentBike: rentable ? logs[0].rentBike : "",
            payMode: rentable ? logs[0].payMode : "minute",
        });
    } catch (err) {
        console.error(err);
        return res.status(500).json({ success: false, bikeLogs: [] });
    }
};




const requestRentBike = async (req, res) => {
    try {
      const { Bikemac } = req.body;
      console.log(Bikemac);
  
      const bike = await Bike.findOne({ Bikemac });
      console.log(bike);
      console.log(req.user);
      if (bike != null && !bike.rented) {
        // Bike is available for rent
        // Perform the rent operation
  
        bike.renter = req.user._id;
        bike.rented = true;
        await bike.save();
  
        const BikeRentLogInstance = new BikeRentLog({
          renter: req.user._id,
          from: Date.now(),
          bike: bike._id,
          mode: "minute",
        });
        await BikeRentLogInstance.save();
  
        res.status(200).json({ success: true });
      } else {
        // Bike is already rented or not found
        res.status(200).json({ success: false, err: "Already rented by someone" });
      }
    } catch (err) {
      console.log(err.message);
      res.status(500).json({ success: false, err: err.message });
    }
  };
  






const finishRent = async (req, res) => {
    try {
        const { bikeMac, latitude, longitude } = req.body;
        const isValidObjectId = mongoose.Types.ObjectId.isValid(req.user._id);
        if (!isValidObjectId) {
            return res.status(400).json({ success: false, err: 'Invalid user ID' });
        }
        const bike = await Bike.findOne({
            Bikemac: bikeMac,
            renter: req.user._id,
        });
        if (bike) {
            bike.renter = null;
            bike.rented = false;
            await bike.save();
            res.json({ success: true });
            // Find the corresponding BikeRentLog document and set its 'to' field
            const rentLog = await BikeRentLog.findOne({
                renter: req.user._id,
                bike: bike._id,
                to: 0, // Filter out any documents that already have a non-zero 'to' field
            });
            if (rentLog) {
                rentLog.to = Date.now();
                await rentLog.save();
                // Calculate price and update wallet
                const mode = rentLog.mode;
                const from = rentLog.from;
                const to = rentLog.to;
                const time = to - from;
                let t = 0;
                if (mode === 'minute') {
                    t = time / 1000 / 60;
                    t = Math.floor(t) + (time % (1000 * 60) > 0 ? 1 : 0);
                } else if (mode === 'hour') {
                    t = time / 1000 / 60 / 60;
                    t = Math.floor(t) + (time % (1000 * 60 * 60) > 0 ? 1 : 0);
                } else if (mode === 'daily') {
                    t = time / 1000 / 60 / 60 / 24;
                    t = Math.floor(t) + (time % (1000 * 60 * 60 * 24) > 0 ? 1 : 0);
                }
                const price = getBikeRentPrice(mode) * t;

                const user = await User.findById(req.user._id);
                if (user) {
                    console.log('price:', price);
                    console.log('user.balance before:', user.balance);
                    user.balance = (user?.balance || 0) - price;
                    console.log('user.balance after:', user.balance);
                    await user.save();

                    const description = `${t} ${mode} ashiglasan hugatsaani`;

                    try {
                        await BikeLastLog.create({
                            bike: bike._id,
                            latitude: latitude,
                            longitude: longitude,
                        });
                    } catch (err) {
                        console.error(err);
                        // Handle the error here, e.g., return an error response
                        return res.status(500).json({ success: false, err: 'Failed to create BikeLastLog' });
                    }

                    await WalletModel.findOneAndUpdate(
                        { user: req.user._id },
                        {
                            $set: { currentBalance: user.balance },
                            $push: {
                                transactions: {
                                    mode: 'withdraw',
                                    description,
                                    before: price + user.balance,
                                    amount: price,
                                    ts: new Date(),
                                },
                            },
                        },
                        {
                            upsert: true,
                        }
                    );
                }
            }
        } else {
            res.json({ success: false, err: 'Bike not found' });
        }
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, err });
    }
};




module.exports = {
    createBike,
    getAllBikes,
    getBikeById,
    updateBikeById,
    deleteBikeById,
    createBikeMessageLog,
    searchRentBikes,
    requestRentBike,
    finishRent
};