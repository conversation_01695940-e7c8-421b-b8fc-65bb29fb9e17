const licenseModel = require('../models/license');
const { getBankList } = require('../utils/QPayment');
const ResponseData = require('../utils/ResponseData');
const ObjectId = require('mongoose').Types.ObjectId



 const extendLicense = getBankList;
 const deleteLicense =  async(req, res) => {
    try {
        await licenseModel.deleteMany({
            _id: { $in: req.body.ids }
        });
        res.status(200).json({ success: true });
    } catch (err) {
        res.status(500).json({ success: false });
    }
}
 const getLicenses =  async(req, res) => {
    try {
        const logs = await licenseModel.find({ owner: ObjectId(req.user._id) }).sort({ createdAt: -1 });
        ResponseData.ok(res, "Got License Logs", { logs });
    } catch (err) {
        console.log(err)
        ResponseData.error(res, "Did not get License Logs", {});
    }


}

module.exports = {
    extendLicense,
    deleteLicense,
    getLicenses
}