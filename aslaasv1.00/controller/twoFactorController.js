const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const crypto = require('crypto');
const User = require('../models/user');
const ResponseData = require('../utils/ResponseData');

// Generate a secure random backup code
const generateBackupCode = () => {
    return crypto.randomBytes(4).toString('hex').toUpperCase();
};

// Generate multiple backup codes
const generateBackupCodes = (count = 10) => {
    const codes = [];
    for (let i = 0; i < count; i++) {
        codes.push({
            code: generateBackupCode(),
            used: false,
            usedAt: null
        });
    }
    return codes;
};

// Setup 2FA - Generate secret and QR code
const setup2FA = async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await User.findById(userId);

        if (!user) {
            return ResponseData.error(res, "User not found", {});
        }

        if (user.twoFactorEnabled) {
            return ResponseData.error(res, "2FA is already enabled for this account", {});
        }

        // Generate a secret for the user
        const secret = speakeasy.generateSecret({
            name: `ASLAA (${user.phoneNumber})`,
            issuer: 'ASLAA Car Control System',
            length: 32
        });

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

        // Store the secret temporarily (not enabled yet)
        user.twoFactorSecret = secret.base32;
        await user.save();

        ResponseData.success(res, "2FA setup initiated", {
            secret: secret.base32,
            qrCode: qrCodeUrl,
            manualEntryKey: secret.base32
        });

    } catch (error) {
        console.error('Setup 2FA error:', error);
        ResponseData.error(res, "Failed to setup 2FA", {});
    }
};

// Verify and enable 2FA
const enable2FA = async (req, res) => {
    try {
        const { token } = req.body;
        const userId = req.user.id;

        if (!token) {
            return ResponseData.error(res, "TOTP token is required", {});
        }

        const user = await User.findById(userId);

        if (!user) {
            return ResponseData.error(res, "User not found", {});
        }

        if (user.twoFactorEnabled) {
            return ResponseData.error(res, "2FA is already enabled", {});
        }

        if (!user.twoFactorSecret) {
            return ResponseData.error(res, "2FA setup not initiated. Please setup 2FA first", {});
        }

        // Verify the token
        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: 'base32',
            token: token,
            window: 2 // Allow 2 time steps (60 seconds) tolerance
        });

        if (!verified) {
            return ResponseData.error(res, "Invalid TOTP token", {});
        }

        // Generate backup codes
        const backupCodes = generateBackupCodes();

        // Enable 2FA
        user.twoFactorEnabled = true;
        user.twoFactorEnabledAt = new Date();
        user.backupCodes = backupCodes;
        user.lastTotpUsed = token; // Prevent immediate reuse
        await user.save();

        ResponseData.success(res, "2FA enabled successfully", {
            backupCodes: backupCodes.map(bc => bc.code),
            message: "Please save these backup codes in a secure location. They can be used to access your account if you lose your authenticator device."
        });

    } catch (error) {
        console.error('Enable 2FA error:', error);
        ResponseData.error(res, "Failed to enable 2FA", {});
    }
};

// Verify TOTP token during login
const verify2FA = async (req, res) => {
    try {
        const { token, phoneNumber, isBackupCode = false } = req.body;

        if (!token || !phoneNumber) {
            return ResponseData.error(res, "Token and phone number are required", {});
        }

        const user = await User.findOne({ phoneNumber });

        if (!user) {
            return ResponseData.error(res, "User not found", {});
        }

        if (!user.twoFactorEnabled) {
            return ResponseData.error(res, "2FA is not enabled for this account", {});
        }

        let verified = false;

        if (isBackupCode) {
            // Verify backup code
            const backupCode = user.backupCodes.find(bc => 
                bc.code === token.toUpperCase() && !bc.used
            );

            if (backupCode) {
                backupCode.used = true;
                backupCode.usedAt = new Date();
                await user.save();
                verified = true;
            }
        } else {
            // Verify TOTP token
            if (user.lastTotpUsed === token) {
                return ResponseData.error(res, "This token has already been used", {});
            }

            verified = speakeasy.totp.verify({
                secret: user.twoFactorSecret,
                encoding: 'base32',
                token: token,
                window: 2
            });

            if (verified) {
                user.lastTotpUsed = token;
                await user.save();
            }
        }

        if (verified) {
            ResponseData.success(res, "2FA verification successful", {
                verified: true
            });
        } else {
            ResponseData.error(res, "Invalid token", {});
        }

    } catch (error) {
        console.error('Verify 2FA error:', error);
        ResponseData.error(res, "Failed to verify 2FA", {});
    }
};

// Disable 2FA
const disable2FA = async (req, res) => {
    try {
        const { token } = req.body;
        const userId = req.user.id;

        if (!token) {
            return ResponseData.error(res, "TOTP token is required to disable 2FA", {});
        }

        const user = await User.findById(userId);

        if (!user) {
            return ResponseData.error(res, "User not found", {});
        }

        if (!user.twoFactorEnabled) {
            return ResponseData.error(res, "2FA is not enabled", {});
        }

        // Verify current token before disabling
        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: 'base32',
            token: token,
            window: 2
        });

        if (!verified) {
            return ResponseData.error(res, "Invalid TOTP token", {});
        }

        // Disable 2FA
        user.twoFactorEnabled = false;
        user.twoFactorSecret = null;
        user.backupCodes = [];
        user.lastTotpUsed = null;
        user.twoFactorEnabledAt = null;
        await user.save();

        ResponseData.success(res, "2FA disabled successfully", {});

    } catch (error) {
        console.error('Disable 2FA error:', error);
        ResponseData.error(res, "Failed to disable 2FA", {});
    }
};

// Get 2FA status
const get2FAStatus = async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await User.findById(userId);

        if (!user) {
            return ResponseData.error(res, "User not found", {});
        }

        const unusedBackupCodes = user.backupCodes ? 
            user.backupCodes.filter(bc => !bc.used).length : 0;

        ResponseData.success(res, "2FA status retrieved", {
            twoFactorEnabled: user.twoFactorEnabled,
            twoFactorEnabledAt: user.twoFactorEnabledAt,
            unusedBackupCodes: unusedBackupCodes,
            hasSecret: !!user.twoFactorSecret
        });

    } catch (error) {
        console.error('Get 2FA status error:', error);
        ResponseData.error(res, "Failed to get 2FA status", {});
    }
};

// Generate new backup codes
const generateNewBackupCodes = async (req, res) => {
    try {
        const { token } = req.body;
        const userId = req.user.id;

        if (!token) {
            return ResponseData.error(res, "TOTP token is required", {});
        }

        const user = await User.findById(userId);

        if (!user) {
            return ResponseData.error(res, "User not found", {});
        }

        if (!user.twoFactorEnabled) {
            return ResponseData.error(res, "2FA is not enabled", {});
        }

        // Verify current token
        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: 'base32',
            token: token,
            window: 2
        });

        if (!verified) {
            return ResponseData.error(res, "Invalid TOTP token", {});
        }

        // Generate new backup codes
        const newBackupCodes = generateBackupCodes();
        user.backupCodes = newBackupCodes;
        await user.save();

        ResponseData.success(res, "New backup codes generated", {
            backupCodes: newBackupCodes.map(bc => bc.code),
            message: "Please save these new backup codes. Your old backup codes are no longer valid."
        });

    } catch (error) {
        console.error('Generate backup codes error:', error);
        ResponseData.error(res, "Failed to generate backup codes", {});
    }
};

module.exports = {
    setup2FA,
    enable2FA,
    verify2FA,
    disable2FA,
    get2FAStatus,
    generateNewBackupCodes
};
