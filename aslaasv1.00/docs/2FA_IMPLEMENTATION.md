# Two-Factor Authentication (2FA) Implementation

## Overview

This document describes the implementation of Time-based One-Time Password (TOTP) two-factor authentication for the ASLAA Car Control System. The implementation uses Google Authenticator-compatible TOTP tokens to provide an additional layer of security.

## Features

- **TOTP Authentication**: Compatible with Google Authenticator, <PERSON>thy, and other TOTP apps
- **QR Code Setup**: Easy setup via QR code scanning
- **Backup Codes**: 10 single-use backup codes for account recovery
- **Rate Limiting**: Protection against brute force attacks
- **Audit Logging**: Comprehensive logging of 2FA events
- **Encryption**: Secure storage of 2FA secrets
- **Replay Protection**: Prevention of token reuse

## Architecture

### Backend Components

1. **Models** (`models/user.js`)
   - Extended User model with 2FA fields
   - Encrypted secret storage
   - Backup codes with usage tracking

2. **Controllers** (`controller/twoFactorController.js`)
   - 2FA setup and management
   - Token verification
   - Backup code generation

3. **Middleware** (`middleware/`)
   - Rate limiting for 2FA operations
   - Enhanced authentication middleware
   - Security utilities

4. **Routes** (`routes/api/twoFactor.js`)
   - RESTful API endpoints for 2FA operations

### Frontend Components

1. **Setup Component** (`components/TwoFactorSetup.js`)
   - QR code display
   - Manual key entry
   - Verification flow

2. **Verification Component** (`components/TwoFactorVerification.js`)
   - TOTP token input
   - Backup code option
   - Error handling

3. **Settings Component** (`components/TwoFactorSettings.js`)
   - Enable/disable 2FA
   - Backup code management
   - Status display

## API Endpoints

### GET /api/2fa/status
Get current 2FA status for authenticated user.

**Response:**
```json
{
  "success": true,
  "data": {
    "twoFactorEnabled": true,
    "twoFactorEnabledAt": "2024-01-15T10:30:00Z",
    "unusedBackupCodes": 8,
    "hasSecret": true
  }
}
```

### POST /api/2fa/setup
Initiate 2FA setup by generating secret and QR code.

**Response:**
```json
{
  "success": true,
  "data": {
    "secret": "JBSWY3DPEHPK3PXP",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "manualEntryKey": "JBSWY3DPEHPK3PXP"
  }
}
```

### POST /api/2fa/enable
Enable 2FA after verifying TOTP token.

**Request:**
```json
{
  "token": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "backupCodes": ["ABCD1234", "EFGH5678", ...],
    "message": "Please save these backup codes..."
  }
}
```

### POST /api/2fa/verify
Verify TOTP token or backup code during login.

**Request:**
```json
{
  "phoneNumber": "99887766",
  "token": "123456",
  "isBackupCode": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "verified": true
  }
}
```

### POST /api/2fa/disable
Disable 2FA (requires current TOTP token).

**Request:**
```json
{
  "token": "123456"
}
```

### POST /api/2fa/backup-codes
Generate new backup codes (requires current TOTP token).

**Request:**
```json
{
  "token": "123456"
}
```

## Security Features

### Encryption
- 2FA secrets are encrypted using AES-256-GCM
- Encryption key stored in environment variables
- Additional authenticated data (AAD) for integrity

### Rate Limiting
- 5 attempts per 15 minutes for TOTP verification
- 3 attempts per hour for 2FA setup
- 2 attempts per day for backup code generation

### Audit Logging
All 2FA events are logged with:
- User ID and phone number
- Action performed
- Result (success/failure)
- IP address and user agent
- Timestamp and metadata

### Replay Protection
- TOTP tokens can only be used once
- Last used token stored to prevent reuse
- Time window validation (±60 seconds)

## Database Schema

### User Model Extensions
```javascript
{
  // Existing fields...
  
  // 2FA fields
  twoFactorSecret: String,        // Encrypted TOTP secret
  twoFactorEnabled: Boolean,      // Whether 2FA is enabled
  backupCodes: [{
    code: String,                 // Backup code
    used: Boolean,                // Whether code has been used
    usedAt: Date                  // When code was used
  }],
  lastTotpUsed: String,          // Last TOTP token used (replay protection)
  twoFactorEnabledAt: Date       // When 2FA was enabled
}
```

## Environment Variables

Add these to your `.env` file:

```bash
# 2FA Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key-here
BACKUP_CODE_SALT=your-backup-code-salt-here

# Rate Limiting (optional, defaults provided)
TOTP_RATE_LIMIT_WINDOW=900000    # 15 minutes in ms
TOTP_RATE_LIMIT_MAX=5            # Max attempts per window
```

## Installation and Setup

1. **Install Dependencies**
   ```bash
   # Backend
   cd aslaasv1.00
   npm install speakeasy qrcode express-rate-limit

   # Frontend
   cd aslaacv1.00
   npm install qrcode.react react-otp-input
   ```

2. **Environment Configuration**
   ```bash
   # Generate encryption key
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   
   # Add to .env file
   echo "ENCRYPTION_KEY=your-generated-key" >> .env
   echo "BACKUP_CODE_SALT=your-random-salt" >> .env
   ```

3. **Database Migration**
   The User model will automatically include new 2FA fields. Existing users will have 2FA disabled by default.

## Usage Flow

### Setup Flow
1. User navigates to account settings
2. Clicks "Enable 2FA"
3. System generates secret and QR code
4. User scans QR code with authenticator app
5. User enters verification code
6. System enables 2FA and provides backup codes
7. User saves backup codes securely

### Login Flow
1. User enters phone number and PIN
2. If 2FA enabled, system prompts for TOTP
3. User enters 6-digit code from authenticator app
4. System verifies code and completes login
5. Alternative: User can use backup code if needed

## Testing

Run the test suite:
```bash
cd aslaasv1.00
npm test tests/twoFactor.test.js
```

Tests cover:
- 2FA setup and enable flow
- TOTP verification
- Backup code functionality
- Rate limiting
- Security utilities
- Error handling

## Troubleshooting

### Common Issues

1. **"Invalid TOTP token"**
   - Check device time synchronization
   - Verify secret was entered correctly
   - Ensure token hasn't been used already

2. **"Rate limit exceeded"**
   - Wait for rate limit window to reset
   - Check for multiple failed attempts

3. **"2FA setup not initiated"**
   - Complete setup flow before enabling
   - Check if user already has 2FA enabled

### Debug Mode
Enable debug logging:
```bash
DEBUG=2fa:* npm start
```

## Security Considerations

1. **Backup Codes**: Store securely, each can only be used once
2. **Secret Storage**: Never log or expose 2FA secrets
3. **Time Sync**: Ensure server time is synchronized
4. **HTTPS**: Always use HTTPS in production
5. **Rate Limiting**: Monitor for brute force attempts

## Future Enhancements

- SMS backup option
- Hardware token support (U2F/WebAuthn)
- Admin override capabilities
- Bulk 2FA management
- Advanced audit reporting

---

# User Guide: Two-Factor Authentication

## What is Two-Factor Authentication?

Two-Factor Authentication (2FA) adds an extra layer of security to your ASLAA account. In addition to your phone number and PIN, you'll need to enter a 6-digit code from your authenticator app to log in.

## Setting Up 2FA

### Step 1: Install an Authenticator App
Download one of these apps on your smartphone:
- **Google Authenticator** (recommended)
- **Authy**
- **Microsoft Authenticator**
- **1Password**

### Step 2: Enable 2FA in Your Account
1. Log in to your ASLAA account
2. Go to Account Settings
3. Find the "Two-Factor Authentication" section
4. Click "Enable 2FA"

### Step 3: Scan the QR Code
1. Open your authenticator app
2. Tap "Add Account" or "+"
3. Scan the QR code displayed on screen
4. If you can't scan, manually enter the provided key

### Step 4: Verify Setup
1. Enter the 6-digit code from your authenticator app
2. Click "Verify & Enable"
3. **Important**: Save your backup codes in a secure location

## Logging In with 2FA

1. Enter your phone number as usual
2. Enter your PIN code
3. When prompted, open your authenticator app
4. Enter the current 6-digit code
5. Click "Verify" to complete login

## Backup Codes

### What are Backup Codes?
Backup codes are 8-character codes that can be used instead of your authenticator app. Each code can only be used once.

### When to Use Backup Codes
- Lost or broken phone
- Authenticator app not working
- Can't access your device

### Using a Backup Code
1. On the 2FA verification screen, click "Use backup code"
2. Enter one of your saved backup codes
3. Click "Verify"

### Generating New Backup Codes
1. Go to Account Settings
2. Find "Two-Factor Authentication"
3. Click "Generate New Backup Codes"
4. Enter your current authenticator code
5. Save the new codes (old codes will no longer work)

## Troubleshooting

### "Invalid Code" Error
- **Check time**: Ensure your phone's time is correct
- **Try next code**: Wait for a new code to generate (codes change every 30 seconds)
- **Use backup code**: If authenticator isn't working

### Lost Phone or Authenticator App
1. Use one of your backup codes to log in
2. Go to Account Settings
3. Disable 2FA temporarily
4. Set up 2FA again with your new device

### Locked Out of Account
If you've lost both your phone and backup codes:
1. Contact support with your account details
2. Provide identity verification
3. Support can help reset your 2FA

## Security Tips

### Do's
✅ Save backup codes in a secure location (password manager, safe)
✅ Keep your authenticator app updated
✅ Enable 2FA on other important accounts
✅ Use a unique PIN for your ASLAA account

### Don'ts
❌ Share your backup codes with anyone
❌ Screenshot or email your backup codes
❌ Use the same PIN for multiple accounts
❌ Ignore security notifications

## Disabling 2FA

**Warning**: Disabling 2FA makes your account less secure.

1. Go to Account Settings
2. Find "Two-Factor Authentication"
3. Toggle off "Enable 2FA"
4. Enter your current authenticator code to confirm
5. Your 2FA will be disabled and backup codes deleted

## Getting Help

If you need assistance with 2FA:
- Check this guide first
- Contact support through the app
- Email: <EMAIL>
- Include your phone number and describe the issue

Remember: Support will never ask for your authenticator codes or backup codes!
