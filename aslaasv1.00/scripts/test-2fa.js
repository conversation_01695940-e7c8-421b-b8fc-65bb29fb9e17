#!/usr/bin/env node

/**
 * Simple test script to verify 2FA implementation
 * Run with: node scripts/test-2fa.js
 */

const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const { encrypt, decrypt, generateSecureRandom } = require('../utils/security');

console.log('🔐 Testing 2FA Implementation...\n');

// Test 1: Secret Generation
console.log('1. Testing secret generation...');
const secret = speakeasy.generateSecret({
    name: 'ASLAA Test User',
    issuer: 'ASLAA Car Control System',
    length: 32
});

console.log('✅ Secret generated successfully');
console.log(`   Base32: ${secret.base32}`);
console.log(`   OTP URL: ${secret.otpauth_url}\n`);

// Test 2: QR Code Generation
console.log('2. Testing QR code generation...');
QRCode.toDataURL(secret.otpauth_url)
    .then(qrCodeUrl => {
        console.log('✅ QR code generated successfully');
        console.log(`   Length: ${qrCodeUrl.length} characters\n`);
        
        // Test 3: TOTP Generation and Verification
        console.log('3. Testing TOTP generation and verification...');
        const token = speakeasy.totp({
            secret: secret.base32,
            encoding: 'base32'
        });
        
        console.log(`   Generated token: ${token}`);
        
        const verified = speakeasy.totp.verify({
            secret: secret.base32,
            encoding: 'base32',
            token: token,
            window: 2
        });
        
        if (verified) {
            console.log('✅ TOTP verification successful\n');
        } else {
            console.log('❌ TOTP verification failed\n');
        }
        
        // Test 4: Encryption/Decryption
        console.log('4. Testing encryption/decryption...');
        try {
            const encrypted = encrypt(secret.base32);
            const decrypted = decrypt(encrypted);
            
            if (decrypted === secret.base32) {
                console.log('✅ Encryption/decryption successful');
                console.log(`   Original: ${secret.base32}`);
                console.log(`   Encrypted: ${encrypted}`);
                console.log(`   Decrypted: ${decrypted}\n`);
            } else {
                console.log('❌ Encryption/decryption failed - data mismatch\n');
            }
        } catch (error) {
            console.log(`❌ Encryption/decryption failed: ${error.message}\n`);
        }
        
        // Test 5: Backup Code Generation
        console.log('5. Testing backup code generation...');
        const backupCodes = [];
        for (let i = 0; i < 10; i++) {
            backupCodes.push(generateSecureRandom(8));
        }
        
        console.log('✅ Backup codes generated successfully');
        console.log('   Sample codes:', backupCodes.slice(0, 3).join(', '), '...\n');
        
        // Test 6: Time-based Token Validation
        console.log('6. Testing time-based validation...');
        
        // Generate token for current time
        const currentToken = speakeasy.totp({
            secret: secret.base32,
            encoding: 'base32'
        });
        
        // Generate token for 30 seconds ago (should still be valid with window)
        const pastToken = speakeasy.totp({
            secret: secret.base32,
            encoding: 'base32',
            time: Math.floor(Date.now() / 1000) - 30
        });
        
        const currentValid = speakeasy.totp.verify({
            secret: secret.base32,
            encoding: 'base32',
            token: currentToken,
            window: 2
        });
        
        const pastValid = speakeasy.totp.verify({
            secret: secret.base32,
            encoding: 'base32',
            token: pastToken,
            window: 2
        });
        
        console.log(`   Current token (${currentToken}): ${currentValid ? '✅ Valid' : '❌ Invalid'}`);
        console.log(`   Past token (${pastToken}): ${pastValid ? '✅ Valid' : '❌ Invalid'}\n`);
        
        // Test 7: Invalid Token Rejection
        console.log('7. Testing invalid token rejection...');
        const invalidToken = '123456';
        const invalidValid = speakeasy.totp.verify({
            secret: secret.base32,
            encoding: 'base32',
            token: invalidToken,
            window: 2
        });
        
        console.log(`   Invalid token (${invalidToken}): ${invalidValid ? '❌ Incorrectly accepted' : '✅ Correctly rejected'}\n`);
        
        // Summary
        console.log('🎉 2FA Implementation Test Complete!');
        console.log('\nNext steps:');
        console.log('1. Start your server: npm start');
        console.log('2. Test the API endpoints with a tool like Postman');
        console.log('3. Test the frontend components in your browser');
        console.log('4. Run the full test suite: npm test tests/twoFactor.test.js');
        
        console.log('\nAPI Endpoints to test:');
        console.log('- POST /api/2fa/setup (requires auth)');
        console.log('- POST /api/2fa/enable (requires auth)');
        console.log('- POST /api/2fa/verify');
        console.log('- GET /api/2fa/status (requires auth)');
        console.log('- POST /api/2fa/disable (requires auth)');
        console.log('- POST /api/2fa/backup-codes (requires auth)');
        
    })
    .catch(error => {
        console.log(`❌ QR code generation failed: ${error.message}\n`);
    });

// Test environment variables
console.log('\n🔧 Environment Check:');
console.log(`ENCRYPTION_KEY: ${process.env.ENCRYPTION_KEY ? '✅ Set' : '❌ Not set (using default)'}`);
console.log(`BACKUP_CODE_SALT: ${process.env.BACKUP_CODE_SALT ? '✅ Set' : '❌ Not set (using default)'}`);

if (!process.env.ENCRYPTION_KEY) {
    console.log('\n⚠️  Warning: ENCRYPTION_KEY not set in environment variables');
    console.log('   Add this to your .env file:');
    console.log(`   ENCRYPTION_KEY=${require('crypto').randomBytes(32).toString('hex')}`);
}

if (!process.env.BACKUP_CODE_SALT) {
    console.log('\n⚠️  Warning: BACKUP_CODE_SALT not set in environment variables');
    console.log('   Add this to your .env file:');
    console.log(`   BACKUP_CODE_SALT=${require('crypto').randomBytes(16).toString('hex')}`);
}
