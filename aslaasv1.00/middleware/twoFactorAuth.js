const passport = require('passport');
const User = require('../models/user');

// Standard authentication middleware (existing)
const auth = passport.authenticate('jwt', { session: false });

// Middleware to check if user has completed 2FA verification
const require2FA = async (req, res, next) => {
    try {
        // First check if user is authenticated
        passport.authenticate('jwt', { session: false }, async (err, user, info) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Authentication error' 
                });
            }

            if (!user) {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Unauthorized' 
                });
            }

            // Attach user to request
            req.user = user;

            // Check if user has 2FA enabled
            if (user.twoFactorEnabled) {
                // Check if 2FA has been verified in this session
                const twoFactorVerified = req.session?.twoFactorVerified;
                const sessionUserId = req.session?.userId;

                if (!twoFactorVerified || sessionUserId !== user.id) {
                    return res.status(403).json({
                        success: false,
                        message: '2FA verification required',
                        requiresTwoFactor: true,
                        phoneNumber: user.phoneNumber
                    });
                }
            }

            next();
        })(req, res, next);

    } catch (error) {
        console.error('2FA middleware error:', error);
        return res.status(500).json({ 
            success: false, 
            message: 'Server error' 
        });
    }
};

// Middleware to mark 2FA as verified in session
const mark2FAVerified = (req, res, next) => {
    if (req.user) {
        if (!req.session) {
            req.session = {};
        }
        req.session.twoFactorVerified = true;
        req.session.userId = req.user.id;
        req.session.twoFactorVerifiedAt = new Date();
    }
    next();
};

// Middleware to clear 2FA verification from session
const clear2FAVerification = (req, res, next) => {
    if (req.session) {
        req.session.twoFactorVerified = false;
        req.session.userId = null;
        req.session.twoFactorVerifiedAt = null;
    }
    next();
};

// Check if 2FA verification is still valid (optional timeout)
const check2FATimeout = (timeoutMinutes = 60) => {
    return (req, res, next) => {
        if (req.session?.twoFactorVerified && req.session?.twoFactorVerifiedAt) {
            const verifiedAt = new Date(req.session.twoFactorVerifiedAt);
            const now = new Date();
            const diffMinutes = (now - verifiedAt) / (1000 * 60);

            if (diffMinutes > timeoutMinutes) {
                req.session.twoFactorVerified = false;
                req.session.userId = null;
                req.session.twoFactorVerifiedAt = null;

                return res.status(403).json({
                    success: false,
                    message: '2FA verification expired. Please verify again.',
                    requiresTwoFactor: true
                });
            }
        }
        next();
    };
};

module.exports = {
    auth, // Standard auth middleware
    require2FA, // Requires 2FA verification if enabled
    mark2FAVerified,
    clear2FAVerification,
    check2FATimeout
};
