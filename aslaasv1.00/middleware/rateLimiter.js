const rateLimit = require('express-rate-limit');

// Rate limiter for 2FA verification attempts
const twoFactorRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 requests per windowMs
    message: {
        success: false,
        message: 'Too many 2FA verification attempts. Please try again in 15 minutes.',
        error: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    keyGenerator: (req) => {
        // Use combination of IP and phone number for more specific rate limiting
        const phoneNumber = req.body.phoneNumber || req.user?.phoneNumber || '';
        return `${req.ip}-${phoneNumber}`;
    },
    skip: (req) => {
        // Skip rate limiting for admin users if needed
        return req.user && req.user.role === 'admin';
    }
});

// Rate limiter for 2FA setup attempts
const twoFactorSetupRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes (reduced from 1 hour for development)
    max: 10, // Increased from 3 to 10 attempts for development
    message: {
        success: false,
        message: 'Too many 2FA setup attempts. Please try again in 15 minutes.',
        error: 'SETUP_RATE_LIMIT_EXCEEDED'
    },
    keyGenerator: (req) => {
        return req.user ? req.user.id : req.ip;
    },
    skip: (req) => {
        // Skip rate limiting in development environment
        return process.env.NODE_ENV === 'development';
    }
});

// Rate limiter for backup code generation
const backupCodeRateLimit = rateLimit({
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    max: 2, // Limit each user to 2 backup code generations per day
    message: {
        success: false,
        message: 'Too many backup code generation attempts. Please try again tomorrow.',
        error: 'BACKUP_CODE_RATE_LIMIT_EXCEEDED'
    },
    keyGenerator: (req) => {
        return req.user ? req.user.id : req.ip;
    }
});

// General rate limiter for authentication endpoints
const authRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 requests per windowMs
    message: {
        success: false,
        message: 'Too many authentication attempts. Please try again later.',
        error: 'AUTH_RATE_LIMIT_EXCEEDED'
    }
});

module.exports = {
    twoFactorRateLimit,
    twoFactorSetupRateLimit,
    backupCodeRateLimit,
    authRateLimit
};
