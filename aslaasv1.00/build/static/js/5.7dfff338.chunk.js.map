{"version": 3, "sources": ["../node_modules/@mui/material/Link/linkClasses.js", "../node_modules/@mui/material/Link/getTextDecoration.js", "../node_modules/@mui/material/Link/Link.js", "../node_modules/@iconify/react/dist/iconify.mjs", "../node_modules/@mui/material/Stack/Stack.js"], "names": ["getLinkUtilityClass", "slot", "generateUtilityClass", "linkClasses", "generateUtilityClasses", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "error", "getTextDecoration", "_ref", "theme", "ownerState", "transformedColor", "color", "transformDeprecatedColors", "<PERSON><PERSON><PERSON>", "concat", "channelColor", "alpha", "_excluded", "LinkRoot", "styled", "Typography", "name", "overridesResolver", "props", "styles", "root", "capitalize", "underline", "component", "button", "_extends", "textDecoration", "textDecorationColor", "position", "WebkitTapHighlightColor", "backgroundColor", "outline", "border", "margin", "borderRadius", "padding", "cursor", "userSelect", "verticalAlign", "MozAppearance", "WebkitAppearance", "borderStyle", "focusVisible", "Link", "React", "inProps", "ref", "useThemeProps", "className", "onBlur", "onFocus", "TypographyClasses", "variant", "sx", "other", "_objectWithoutPropertiesLoose", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "useIsFocusVisible", "setFocusVisible", "handler<PERSON>ef", "useForkRef", "classes", "slots", "composeClasses", "useUtilityClasses", "_jsx", "clsx", "event", "current", "Object", "keys", "includes", "Array", "isArray", "matchName", "iconDefaults", "freeze", "left", "top", "width", "height", "rotate", "vFlip", "hFlip", "fullIcon", "data", "stringToIcon", "value", "validate", "allowSimpleName", "provider", "arguments", "length", "undefined", "colonSeparated", "split", "slice", "shift", "name2", "pop", "prefix", "result", "validateIcon", "dashSeparated", "join", "icon", "match", "mergeIconData", "alias", "key", "prop", "getIconData$1", "full", "getIcon", "iteration", "icons", "assign", "aliases", "item", "result2", "parent", "chars", "parseIconSet", "callback", "options", "names", "not_found", "for<PERSON>ach", "push", "iconData", "parseAliases", "isVariation", "optionalProperties", "quicklyValidateIconSet", "obj", "body", "storage$1", "create", "w", "window", "self", "_iconifyStorage", "version", "storage", "err", "getStorage", "providerStorage", "missing", "newStorage", "addIconSet", "storage2", "t", "Date", "now", "getIconFromStorage", "simpleNames", "allowSimpleNames", "allow", "getIconData", "addIcon", "addIconToStorage", "defaults", "inline", "hAlign", "vAlign", "mergeCustomisations", "defaults2", "attr", "unitsSplit", "unitsTest", "calculateSize", "size", "ratio", "precision", "Math", "ceil", "oldParts", "newParts", "code", "isNumber", "test", "num", "parseFloat", "isNaN", "preserveAspectRatio", "iconToSVG", "customisations", "box", "transformations", "tempValue", "rotation", "toString", "floor", "unshift", "attributes", "viewBox", "regex", "randomPrefix", "random", "counter", "replaceIDs", "ids", "exec", "id", "newID", "escapedID", "replace", "RegExp", "setAPIModule", "getAPIModule", "createAPIConfig", "source", "resources", "path", "maxURL", "timeout", "index", "dataAfterTimeout", "configStorage", "fallBackAPISources", "fallBackAPI", "addAPIProvider", "customConfig", "config", "getAPIConfig", "mergeParams", "base", "params", "hasParams", "indexOf", "encodeURIComponent", "Error", "paramToString", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathCache", "fetchModule", "detectFetch", "fetch", "fetchAPIModule", "prepare", "results", "max<PERSON><PERSON><PERSON>", "maxHost<PERSON><PERSON><PERSON>", "host", "max", "url", "cache<PERSON>ey", "calculateMaxLength", "type", "send", "iconsList", "uri", "defaultError", "then", "response", "status", "json", "setTimeout", "shouldAbort", "catch", "callbacks", "pendingUpdates", "removeCallback", "sources", "providerCallbacks", "items", "filter", "row", "idCounter", "defaultConfig", "<PERSON><PERSON><PERSON><PERSON>", "payload", "query", "done", "resourcesCount", "startIndex", "list", "nextIndex", "startTime", "lastError", "queriesSent", "timer", "queue", "doneCallbacks", "resetTimer", "clearTimeout", "abort", "subscribe", "overwrite", "fail<PERSON><PERSON><PERSON>", "clearQueue", "execNext", "resource", "status2", "isError", "queued", "moduleResponse", "queriesPending", "initRedundancy", "cfg", "newConfig", "setConfig", "queries", "cleanup", "query<PERSON><PERSON><PERSON>", "doneCallback", "query2", "find", "setIndex", "getIndex", "emptyCallback$1", "redundancyCache", "sendAPIQuery", "target", "redundancy", "api", "cached", "cachedReundancy", "getRedundancyCache", "cache", "emptyCallback", "pendingIcons", "iconsToLoad", "loaderFlags", "queueFlags", "loadedNewIcons", "providerLoaderFlags", "providerPendingUpdates", "hasPending", "<PERSON><PERSON><PERSON><PERSON>", "pending", "loaded", "updateCallbacks", "errorsCache", "loadNewIcons", "providerIconsToLoad", "providerQueueFlags", "providerPendingIcons", "sort", "icons2", "time", "console", "parsed", "store", "err2", "loadIcons", "cleanedIcons", "listToIcons", "sortedIcons", "a", "b", "localeCompare", "lastIcon", "localStorage", "sortIcons", "callCallback", "newIcons", "lastProvider", "lastPrefix", "providerNewIcons", "pendingQueue", "pendingSources", "bind", "storeCallback", "cacheVersion", "cachePrefix", "<PERSON><PERSON><PERSON>", "version<PERSON>ey", "hour", "local", "session", "count", "emptyList", "_window", "getGlobal", "setCount", "setItem", "getCount", "count2", "getItem", "total", "parseInt", "loadCache", "minTime", "load", "func", "valid", "JSON", "parse", "removeItem", "i", "destroyCache", "initCache", "storeCache", "stringify", "separator", "flipFromString", "custom", "flip", "str", "trim", "alignmentFromString", "align", "rotateFromString", "defaultValue", "units", "value2", "svgDefaults", "inlineDefaults", "document", "IconifyPreload", "preload", "added", "addCollection", "e", "IconifyProviders", "providers", "IconComponent", "Component", "constructor", "super", "this", "state", "_abortLoading", "_loading", "_setData", "setState", "_checkIcon", "changed", "_icon", "iconName", "onLoad", "componentDidMount", "componentDidUpdate", "oldProps", "componentWillUnmount", "render", "children", "createElement", "newProps", "defaultProps", "style", "componentProps", "localCounter", "dangerouslySetInnerHTML", "__html", "_inline", "Icon", "forwardRef", "joinChildren", "childrenA<PERSON>y", "toArray", "Boolean", "reduce", "output", "child", "StackRoot", "display", "flexDirection", "handleBreakpoints", "resolveBreakpointValues", "values", "direction", "breakpoints", "propValue", "spacing", "transformer", "createUnarySpacing", "acc", "breakpoint", "directionV<PERSON>ues", "spacingValues", "previousDirectionValue", "styleFromPropValue", "column", "getValue", "deepmerge", "mergeBreakpointsInOrder", "<PERSON><PERSON>", "themeProps", "extendSxProp", "divider", "as"], "mappings": "mNAEO,SAASA,EAAoBC,GAClC,OAAOC,YAAqB,UAAWD,EACzC,CAEeE,MADKC,YAAuB,UAAW,CAAC,OAAQ,gBAAiB,iBAAkB,kBAAmB,SAAU,iB,iBCJxH,MAAMC,EAAuB,CAClCC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfC,MAAO,cAiBMC,MAZWC,IAGpB,IAHqB,MACzBC,EAAK,WACLC,GACDF,EACC,MAAMG,EAP0BC,IACzBX,EAAqBW,IAAUA,EAMbC,CAA0BH,EAAWE,OACxDA,EAAQE,YAAQL,EAAO,WAAFM,OAAaJ,IAAoB,IAAUD,EAAWE,MAC3EI,EAAeF,YAAQL,EAAO,WAAFM,OAAaJ,EAAgB,YAC/D,MAAI,SAAUF,GAASO,EACd,QAAPD,OAAeC,EAAY,WAEtBC,YAAML,EAAO,GAAI,E,OCnB1B,MAAMM,EAAY,CAAC,YAAa,QAAS,YAAa,SAAU,UAAW,oBAAqB,YAAa,UAAW,MA2BlHC,EAAWC,YAAOC,IAAY,CAClCC,KAAM,UACNzB,KAAM,OACN0B,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJf,GACEc,EACJ,MAAO,CAACC,EAAOC,KAAMD,EAAO,YAADV,OAAaY,YAAWjB,EAAWkB,aAAwC,WAAzBlB,EAAWmB,WAA0BJ,EAAOK,OAAO,GAPnHV,EASdZ,IAGG,IAHF,MACFC,EAAK,WACLC,GACDF,EACC,OAAOuB,YAAS,CAAC,EAA4B,SAAzBrB,EAAWkB,WAAwB,CACrDI,eAAgB,QACU,UAAzBtB,EAAWkB,WAAyB,CACrCI,eAAgB,OAChB,UAAW,CACTA,eAAgB,cAEQ,WAAzBtB,EAAWkB,WAA0BG,YAAS,CAC/CC,eAAgB,aACM,YAArBtB,EAAWE,OAAuB,CACnCqB,oBAAqB1B,EAAkB,CACrCE,QACAC,gBAED,CACD,UAAW,CACTuB,oBAAqB,aAEI,WAAzBvB,EAAWmB,WAA0B,CACvCK,SAAU,WACVC,wBAAyB,cACzBC,gBAAiB,cAGjBC,QAAS,EACTC,OAAQ,EACRC,OAAQ,EAERC,aAAc,EACdC,QAAS,EAETC,OAAQ,UACRC,WAAY,OACZC,cAAe,SACfC,cAAe,OAEfC,iBAAkB,OAElB,sBAAuB,CACrBC,YAAa,QAGf,CAAC,KAADhC,OAAMhB,EAAYiD,eAAiB,CACjCX,QAAS,SAEX,IAEEY,EAAoBC,cAAiB,SAAcC,EAASC,GAChE,MAAM5B,EAAQ6B,YAAc,CAC1B7B,MAAO2B,EACP7B,KAAM,aAEF,UACFgC,EAAS,MACT1C,EAAQ,UAAS,UACjBiB,EAAY,IAAG,OACf0B,EAAM,QACNC,EAAO,kBACPC,EAAiB,UACjB7B,EAAY,SAAQ,QACpB8B,EAAU,UAAS,GACnBC,GACEnC,EACJoC,EAAQC,YAA8BrC,EAAON,IACzC,kBACJ4C,EACAP,OAAQQ,EACRP,QAASQ,EACTZ,IAAKa,GACHC,eACGlB,EAAcmB,GAAmBjB,YAAe,GACjDkB,EAAaC,YAAWjB,EAAKa,GAmB7BvD,EAAaqB,YAAS,CAAC,EAAGP,EAAO,CACrCZ,QACAiB,YACAmB,eACApB,YACA8B,YAEIY,EA1HkB5D,KACxB,MAAM,QACJ4D,EAAO,UACPzC,EAAS,aACTmB,EAAY,UACZpB,GACElB,EACE6D,EAAQ,CACZ7C,KAAM,CAAC,OAAQ,YAAFX,OAAcY,YAAWC,IAA4B,WAAdC,GAA0B,SAAUmB,GAAgB,iBAE1G,OAAOwB,YAAeD,EAAO3E,EAAqB0E,EAAQ,EAgH1CG,CAAkB/D,GAClC,OAAoBgE,cAAKvD,EAAUY,YAAS,CAC1CnB,MAAOA,EACP0C,UAAWqB,YAAKL,EAAQ5C,KAAM4B,GAC9BgB,QAASb,EACT5B,UAAWA,EACX0B,OA/BiBqB,IACjBb,EAAkBa,IACgB,IAA9Bd,EAAkBe,SACpBV,GAAgB,GAEdZ,GACFA,EAAOqB,EACT,EAyBApB,QAvBkBoB,IAClBZ,EAAmBY,IACe,IAA9Bd,EAAkBe,SACpBV,GAAgB,GAEdX,GACFA,EAAQoB,EACV,EAiBAxB,IAAKgB,EACL1D,WAAYA,EACZgD,QAASA,EACTC,GAAI,IAAMmB,OAAOC,KAAK9E,GAAsB+E,SAASpE,GAEhD,GAFyD,CAAC,CAC7DA,aACYqE,MAAMC,QAAQvB,GAAMA,EAAK,CAACA,KACvCC,GACL,IAuDeX,K,mCCjNf,8CAEA,MAAMkC,EAAY,2BACZC,EAAeN,OAAOO,OAAO,CACjCC,KAAM,EACNC,IAAK,EACLC,MAAO,GACPC,OAAQ,GACRC,OAAQ,EACRC,OAAO,EACPC,OAAO,IAET,SAASC,EAASC,GAChB,MAAO,IAAKV,KAAiBU,EAC/B,CAEA,MAAMC,EAAe,SAACC,EAAOC,EAAUC,GAAmC,IAAlBC,EAAQC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACjE,MAAMG,EAAiBP,EAAMQ,MAAM,KACnC,GAA0B,MAAtBR,EAAMS,MAAM,EAAG,GAAY,CAC7B,GAAIF,EAAeF,OAAS,GAAKE,EAAeF,OAAS,EACvD,OAAO,KAETF,EAAWI,EAAeG,QAAQD,MAAM,EAC1C,CACA,GAAIF,EAAeF,OAAS,IAAME,EAAeF,OAC/C,OAAO,KAET,GAAIE,EAAeF,OAAS,EAAG,CAC7B,MAAMM,EAAQJ,EAAeK,MACvBC,EAASN,EAAeK,MACxBE,EAAS,CACbX,SAAUI,EAAeF,OAAS,EAAIE,EAAe,GAAKJ,EAC1DU,SACAvF,KAAMqF,GAER,OAAOV,IAAac,EAAaD,GAAU,KAAOA,CACpD,CACA,MAAMxF,EAAOiF,EAAe,GACtBS,EAAgB1F,EAAKkF,MAAM,KACjC,GAAIQ,EAAcX,OAAS,EAAG,CAC5B,MAAMS,EAAS,CACbX,WACAU,OAAQG,EAAcN,QACtBpF,KAAM0F,EAAcC,KAAK,MAE3B,OAAOhB,IAAac,EAAaD,GAAU,KAAOA,CACpD,CACA,GAAIZ,GAAgC,KAAbC,EAAiB,CACtC,MAAMW,EAAS,CACbX,WACAU,OAAQ,GACRvF,QAEF,OAAO2E,IAAac,EAAaD,EAAQZ,GAAmB,KAAOY,CACrE,CACA,OAAO,IACT,EACMC,EAAeA,CAACG,EAAMhB,MACrBgB,KAGwB,KAAlBA,EAAKf,WAAmBe,EAAKf,SAASgB,MAAMhC,MAAgBe,GAAmC,KAAhBgB,EAAKL,QAAiBK,EAAKL,OAAOM,MAAMhC,MAAe+B,EAAK5F,KAAK6F,MAAMhC,IAGnK,SAASiC,EAAcF,EAAMG,GAC3B,MAAMP,EAAS,IAAKI,GACpB,IAAK,MAAMI,KAAOlC,EAAc,CAC9B,MAAMmC,EAAOD,EACb,QAAoB,IAAhBD,EAAME,GAAkB,CAC1B,MAAMvB,EAAQqB,EAAME,GACpB,QAAqB,IAAjBT,EAAOS,GAAkB,CAC3BT,EAAOS,GAAQvB,EACf,QACF,CACA,OAAQuB,GACN,IAAK,SACHT,EAAOS,IAAST,EAAOS,GAAQvB,GAAS,EACxC,MACF,IAAK,QACL,IAAK,QACHc,EAAOS,GAAQvB,IAAUc,EAAOS,GAChC,MACF,QACET,EAAOS,GAAQvB,EAErB,CACF,CACA,OAAOc,CACT,CAEA,SAASU,EAAc1B,EAAMxE,GAAoB,IAAdmG,EAAIrB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACrC,SAASsB,EAAQf,EAAOgB,GACtB,QAA0B,IAAtB7B,EAAK8B,MAAMjB,GACb,OAAO7B,OAAO+C,OAAO,CAAC,EAAG/B,EAAK8B,MAAMjB,IAEtC,GAAIgB,EAAY,EACd,OAAO,KAET,MAAMG,EAAUhC,EAAKgC,QACrB,GAAIA,QAA8B,IAAnBA,EAAQnB,GAAmB,CACxC,MAAMoB,EAAOD,EAAQnB,GACfqB,EAAUN,EAAQK,EAAKE,OAAQN,EAAY,GACjD,OAAIK,EACKZ,EAAcY,EAASD,GAEzBC,CACT,CACA,MAAME,EAAQpC,EAAKoC,MACnB,OAAKP,GAAaO,QAA0B,IAAjBA,EAAMvB,GACxBe,EAAQQ,EAAMvB,GAAQgB,EAAY,GAEpC,IACT,CACA,MAAMb,EAASY,EAAQpG,EAAM,GAC7B,GAAIwF,EACF,IAAK,MAAMQ,KAAOlC,OACI,IAAhB0B,EAAOQ,SAAiC,IAAdxB,EAAKwB,KACjCR,EAAOQ,GAAOxB,EAAKwB,IAIzB,OAAOR,GAAUW,EAAO5B,EAASiB,GAAUA,CAC7C,CAUA,SAASqB,EAAarC,EAAMsC,EAAUC,GACpCA,EAAUA,GAAW,CAAC,EACtB,MAAMC,EAAQ,GACd,GAAoB,kBAATxC,GAA2C,kBAAfA,EAAK8B,MAC1C,OAAOU,EAELxC,EAAKyC,qBAAqBtD,OAC5Ba,EAAKyC,UAAUC,SAASlH,IACtB8G,EAAS9G,EAAM,MACfgH,EAAMG,KAAKnH,EAAK,IAGpB,MAAMsG,EAAQ9B,EAAK8B,MACnB9C,OAAOC,KAAK6C,GAAOY,SAASlH,IAC1B,MAAMoH,EAAWlB,EAAc1B,EAAMxE,GAAM,GACvCoH,IACFN,EAAS9G,EAAMoH,GACfJ,EAAMG,KAAKnH,GACb,IAEF,MAAMqH,EAAeN,EAAQP,SAAW,MACxC,GAAqB,SAAjBa,GAAmD,kBAAjB7C,EAAKgC,QAAsB,CAC/D,MAAMA,EAAUhC,EAAKgC,QACrBhD,OAAOC,KAAK+C,GAASU,SAASlH,IAC5B,GAAqB,eAAjBqH,GAhCV,SAAqBZ,GACnB,IAAK,MAAMT,KAAOlC,EAChB,QAAkB,IAAd2C,EAAKT,GACP,OAAO,EAGX,OAAO,CACT,CAyB2CsB,CAAYd,EAAQxG,IACvD,OAEF,MAAMoH,EAAWlB,EAAc1B,EAAMxE,GAAM,GACvCoH,IACFN,EAAS9G,EAAMoH,GACfJ,EAAMG,KAAKnH,GACb,GAEJ,CACA,OAAOgH,CACT,CAEA,MAAMO,EAAqB,CACzB1C,SAAU,SACV2B,QAAS,SACTS,UAAW,UAEb,IAAK,MAAMhB,MAAQnC,EACjByD,EAAmBtB,WAAenC,EAAamC,IAEjD,SAASuB,EAAuBC,GAC9B,GAAmB,kBAARA,GAA4B,OAARA,EAC7B,OAAO,KAET,MAAMjD,EAAOiD,EACb,GAA2B,kBAAhBjD,EAAKe,SAAwBkC,EAAInB,OAA8B,kBAAdmB,EAAInB,MAC9D,OAAO,KAET,IAAK,MAAML,KAAQsB,EACjB,QAAkB,IAAdE,EAAIxB,WAA2BwB,EAAIxB,KAAUsB,EAAmBtB,GAClE,OAAO,KAGX,MAAMK,EAAQ9B,EAAK8B,MACnB,IAAK,MAAMtG,KAAQsG,EAAO,CACxB,MAAMV,EAAOU,EAAMtG,GACnB,IAAKA,EAAK6F,MAAMhC,IAAmC,kBAAd+B,EAAK8B,KACxC,OAAO,KAET,IAAK,MAAMzB,KAAQnC,EACjB,QAAmB,IAAf8B,EAAKK,WAA2BL,EAAKK,YAAiBnC,EAAamC,GACrE,OAAO,IAGb,CACA,MAAMO,EAAUhC,EAAKgC,QACrB,GAAIA,EACF,IAAK,MAAMxG,KAAQwG,EAAS,CAC1B,MAAMZ,EAAOY,EAAQxG,GACf2G,EAASf,EAAKe,OACpB,IAAK3G,EAAK6F,MAAMhC,IAAgC,kBAAX8C,IAAwBL,EAAMK,KAAYH,EAAQG,GACrF,OAAO,KAET,IAAK,MAAMV,KAAQnC,EACjB,QAAmB,IAAf8B,EAAKK,WAA2BL,EAAKK,YAAiBnC,EAAamC,GACrE,OAAO,IAGb,CAEF,OAAOzB,CACT,CAGA,IAAImD,EAA4BnE,OAAOoE,OAAO,MAC9C,IACE,MAAMC,EAAIC,QAAUC,KAChBF,GAJiB,IAIZA,EAAEG,gBAAgBC,UACzBN,EAAYE,EAAEG,gBAAgBE,QAGlC,CADE,MAAOC,IACT,CAqBA,SAASC,EAAWvD,EAAUU,QACA,IAAxBoC,EAAU9C,KACZ8C,EAAU9C,GAA4BrB,OAAOoE,OAAO,OAEtD,MAAMS,EAAkBV,EAAU9C,GAIlC,YAHgC,IAA5BwD,EAAgB9C,KAClB8C,EAAgB9C,GAdpB,SAAoBV,EAAUU,GAC5B,MAAO,CACLV,WACAU,SACAe,MAAuB9C,OAAOoE,OAAO,MACrCU,QAAyB9E,OAAOoE,OAAO,MAE3C,CAO8BW,CAAW1D,EAAUU,IAE1C8C,EAAgB9C,EACzB,CACA,SAASiD,EAAWC,EAAUjE,GAC5B,IAAKgD,EAAuBhD,GAC1B,MAAO,GAET,MAAMkE,EAAIC,KAAKC,MACf,OAAO/B,EAAarC,GAAM,CAACxE,EAAM4F,KAC3BA,EACF6C,EAASnC,MAAMtG,GAAQ4F,EAEvB6C,EAASH,QAAQtI,GAAQ0I,CAC3B,GAEJ,CAWA,SAASG,EAAmBJ,EAAUzI,GACpC,MAAM0E,EAAQ+D,EAASnC,MAAMtG,GAC7B,YAAiB,IAAV0E,EAAmB,KAAOA,CACnC,CAyBA,IAAIoE,GAAc,EAClB,SAASC,EAAiBC,GAIxB,MAHqB,mBAAVA,IACTF,EAAcE,GAETF,CACT,CACA,SAASG,EAAYjJ,GACnB,MAAM4F,EAAuB,kBAAT5F,EAAoByE,EAAazE,GAAM,EAAM8I,GAAe9I,EAChF,OAAO4F,EAAOiD,EAAmBT,EAAWxC,EAAKf,SAAUe,EAAKL,QAASK,EAAK5F,MAAQ,IACxF,CACA,SAASkJ,EAAQlJ,EAAMwE,GACrB,MAAMoB,EAAOnB,EAAazE,GAAM,EAAM8I,GACtC,IAAKlD,EACH,OAAO,EAGT,OAvDF,SAA0B6C,EAAUzI,EAAM4F,GACxC,IACE,GAAyB,kBAAdA,EAAK8B,KAEd,OADAe,EAASnC,MAAMtG,GAAQwD,OAAOO,OAAOQ,EAASqB,KACvC,CAGX,CADE,MAAOuC,IACT,CACA,OAAO,CACT,CA8CSgB,CADSf,EAAWxC,EAAKf,SAAUe,EAAKL,QACdK,EAAK5F,KAAMwE,EAC9C,CAsCA,MAAM4E,EAAW5F,OAAOO,OAAO,CAC7BsF,QAAQ,EACRnF,MAAO,KACPC,OAAQ,KACRmF,OAAQ,SACRC,OAAQ,SACRpE,OAAO,EACPb,OAAO,EACPD,OAAO,EACPD,OAAQ,IAEV,SAASoF,EAAoBC,EAAWhD,GACtC,MAAMjB,EAAS,CAAC,EAChB,IAAK,MAAMQ,KAAOyD,EAAW,CAC3B,MAAMC,EAAO1D,EAEb,GADAR,EAAOkE,GAAQD,EAAUC,QACN,IAAfjD,EAAKiD,GACP,SAEF,MAAMhF,EAAQ+B,EAAKiD,GACnB,OAAQA,GACN,IAAK,SACL,IAAK,QACkB,mBAAVhF,IACTc,EAAOkE,GAAQhF,GAEjB,MACF,IAAK,QACL,IAAK,SACW,IAAVA,IACFc,EAAOkE,IAASlE,EAAOkE,IAEzB,MACF,IAAK,SACL,IAAK,SACkB,kBAAVhF,GAAgC,KAAVA,IAC/Bc,EAAOkE,GAAQhF,GAEjB,MACF,IAAK,QACL,IAAK,UACkB,kBAAVA,GAAgC,KAAVA,GAAiC,kBAAVA,GAAsBA,GAAmB,OAAVA,KACrFc,EAAOkE,GAAQhF,GAEjB,MACF,IAAK,SACkB,kBAAVA,IACTc,EAAOkE,IAAShF,GAIxB,CACA,OAAOc,CACT,CAEA,MAAMmE,EAAa,4BACbC,EAAY,4BAClB,SAASC,EAAcC,EAAMC,EAAOC,GAClC,GAAc,IAAVD,EACF,OAAOD,EAGT,GADAE,OAA0B,IAAdA,EAAuB,IAAMA,EACrB,kBAATF,EACT,OAAOG,KAAKC,KAAKJ,EAAOC,EAAQC,GAAaA,EAE/C,GAAoB,kBAATF,EACT,OAAOA,EAET,MAAMK,EAAWL,EAAK5E,MAAMyE,GAC5B,GAAiB,OAAbQ,IAAsBA,EAASpF,OACjC,OAAO+E,EAET,MAAMM,EAAW,GACjB,IAAIC,EAAOF,EAAS/E,QAChBkF,EAAWV,EAAUW,KAAKF,GAC9B,OAAa,CACX,GAAIC,EAAU,CACZ,MAAME,EAAMC,WAAWJ,GACnBK,MAAMF,GACRJ,EAASjD,KAAKkD,GAEdD,EAASjD,KAAK8C,KAAKC,KAAKM,EAAMT,EAAQC,GAAaA,EAEvD,MACEI,EAASjD,KAAKkD,GAGhB,GADAA,EAAOF,EAAS/E,aACH,IAATiF,EACF,OAAOD,EAASzE,KAAK,IAEvB2E,GAAYA,CACd,CACF,CAEA,SAASK,EAAoBzK,GAC3B,IAAIsF,EAAS,GACb,OAAQtF,EAAMoJ,QACZ,IAAK,OACH9D,GAAU,OACV,MACF,IAAK,QACHA,GAAU,OACV,MACF,QACEA,GAAU,OAEd,OAAQtF,EAAMqJ,QACZ,IAAK,MACH/D,GAAU,OACV,MACF,IAAK,SACHA,GAAU,OACV,MACF,QACEA,GAAU,OAGd,OADAA,GAAUtF,EAAMiF,MAAQ,SAAW,QAC5BK,CACT,CACA,SAASoF,EAAUhF,EAAMiF,GACvB,MAAMC,EAAM,CACV9G,KAAM4B,EAAK5B,KACXC,IAAK2B,EAAK3B,IACVC,MAAO0B,EAAK1B,MACZC,OAAQyB,EAAKzB,QAEf,IAqDID,EAAOC,EArDPuD,EAAO9B,EAAK8B,KAChB,CAAC9B,EAAMiF,GAAgB3D,SAAShH,IAC9B,MAAM6K,EAAkB,GAClBzG,EAAQpE,EAAMoE,MACdD,EAAQnE,EAAMmE,MACpB,IAcI2G,EAdAC,EAAW/K,EAAMkE,OAmBrB,OAlBIE,EACED,EACF4G,GAAY,GAEZF,EAAgB5D,KAAK,cAAgB2D,EAAI5G,MAAQ4G,EAAI9G,MAAMkH,WAAa,KAAO,EAAIJ,EAAI7G,KAAKiH,WAAa,KACzGH,EAAgB5D,KAAK,eACrB2D,EAAI7G,IAAM6G,EAAI9G,KAAO,GAEdK,IACT0G,EAAgB5D,KAAK,cAAgB,EAAI2D,EAAI9G,MAAMkH,WAAa,KAAOJ,EAAI3G,OAAS2G,EAAI7G,KAAKiH,WAAa,KAC1GH,EAAgB5D,KAAK,eACrB2D,EAAI7G,IAAM6G,EAAI9G,KAAO,GAGnBiH,EAAW,IACbA,GAAuC,EAA3BhB,KAAKkB,MAAMF,EAAW,IAEpCA,GAAsB,EACdA,GACN,KAAK,EACHD,EAAYF,EAAI3G,OAAS,EAAI2G,EAAI7G,IACjC8G,EAAgBK,QAAQ,aAAeJ,EAAUE,WAAa,IAAMF,EAAUE,WAAa,KAC3F,MACF,KAAK,EACHH,EAAgBK,QAAQ,eAAiBN,EAAI5G,MAAQ,EAAI4G,EAAI9G,MAAMkH,WAAa,KAAOJ,EAAI3G,OAAS,EAAI2G,EAAI7G,KAAKiH,WAAa,KAC9H,MACF,KAAK,EACHF,EAAYF,EAAI5G,MAAQ,EAAI4G,EAAI9G,KAChC+G,EAAgBK,QAAQ,cAAgBJ,EAAUE,WAAa,IAAMF,EAAUE,WAAa,KAG5FD,EAAW,IAAM,IACF,IAAbH,EAAI9G,MAA0B,IAAZ8G,EAAI7G,MACxB+G,EAAYF,EAAI9G,KAChB8G,EAAI9G,KAAO8G,EAAI7G,IACf6G,EAAI7G,IAAM+G,GAERF,EAAI5G,QAAU4G,EAAI3G,SACpB6G,EAAYF,EAAI5G,MAChB4G,EAAI5G,MAAQ4G,EAAI3G,OAChB2G,EAAI3G,OAAS6G,IAGbD,EAAgBhG,SAClB2C,EAAO,iBAAmBqD,EAAgBpF,KAAK,KAAO,KAAO+B,EAAO,OACtE,IAG2B,OAAzBmD,EAAe3G,OAA4C,OAA1B2G,EAAe1G,QAClDA,EAAS,MACTD,EAAQ2F,EAAc1F,EAAQ2G,EAAI5G,MAAQ4G,EAAI3G,SACZ,OAAzB0G,EAAe3G,OAA4C,OAA1B2G,EAAe1G,QACzDD,EAAQ2G,EAAe3G,MACvBC,EAAS0G,EAAe1G,QACW,OAA1B0G,EAAe1G,QACxBA,EAAS0G,EAAe1G,OACxBD,EAAQ2F,EAAc1F,EAAQ2G,EAAI5G,MAAQ4G,EAAI3G,UAE9CD,EAAQ2G,EAAe3G,MACvBC,EAAS0F,EAAc3F,EAAO4G,EAAI3G,OAAS2G,EAAI5G,QAEnC,SAAVA,IACFA,EAAQ4G,EAAI5G,OAEC,SAAXC,IACFA,EAAS2G,EAAI3G,QAEfD,EAAyB,kBAAVA,EAAqBA,EAAQA,EAAMgH,WAAa,GAC/D/G,EAA2B,kBAAXA,EAAsBA,EAASA,EAAO+G,WAAa,GACnE,MAAM1F,EAAS,CACb6F,WAAY,CACVnH,QACAC,SACAwG,oBAAqBA,EAAoBE,GACzCS,QAASR,EAAI9G,KAAKkH,WAAa,IAAMJ,EAAI7G,IAAIiH,WAAa,IAAMJ,EAAI5G,MAAMgH,WAAa,IAAMJ,EAAI3G,OAAO+G,YAE1GxD,QAKF,OAHImD,EAAexB,SACjB7D,EAAO6D,QAAS,GAEX7D,CACT,CAMA,MAAM+F,EAAQ,gBACRC,EAAe,YAAc7C,KAAKC,MAAMsC,SAAS,KAAuB,SAAhBjB,KAAKwB,SAAsB,GAAGP,SAAS,IACrG,IAAIQ,EAAU,EACd,SAASC,EAAWjE,GAA6B,IAAvBnC,EAAMT,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG0G,EACjC,MAAMI,EAAM,GACZ,IAAI/F,EACJ,KAAOA,EAAQ0F,EAAMM,KAAKnE,IACxBkE,EAAIzE,KAAKtB,EAAM,IAEjB,OAAK+F,EAAI7G,QAGT6G,EAAI1E,SAAS4E,IACX,MAAMC,EAA0B,oBAAXxG,EAAwBA,EAAOuG,GAAMvG,GAAUmG,KAAWR,WACzEc,EAAYF,EAAGG,QAAQ,sBAAuB,QACpDvE,EAAOA,EAAKuE,QAAQ,IAAIC,OAAO,WAAaF,EAAY,mBAAoB,KAAM,KAAOD,EAAQ,KAAK,IAEjGrE,GAPEA,CAQX,CAEA,MAAMQ,EAA0B1E,OAAOoE,OAAO,MAC9C,SAASuE,EAAatH,EAAU4B,GAC9ByB,EAAQrD,GAAY4B,CACtB,CACA,SAAS2F,EAAavH,GACpB,OAAOqD,EAAQrD,IAAaqD,EAAQ,GACtC,CAEA,SAASmE,EAAgBC,GACvB,IAAIC,EACJ,GAAgC,kBAArBD,EAAOC,UAChBA,EAAY,CAACD,EAAOC,gBAGpB,GADAA,EAAYD,EAAOC,YACbA,aAAqB5I,SAAW4I,EAAUxH,OAC9C,OAAO,KAaX,MAVe,CACbwH,YACAC,UAAsB,IAAhBF,EAAOE,KAAkB,IAAMF,EAAOE,KAC5CC,OAAQH,EAAOG,OAASH,EAAOG,OAAS,IACxCrI,OAAQkI,EAAOlI,OAASkI,EAAOlI,OAAS,IACxCsI,QAASJ,EAAOI,QAAUJ,EAAOI,QAAU,IAC3CjB,QAA0B,IAAlBa,EAAOb,OACfkB,MAAOL,EAAOK,MAAQL,EAAOK,MAAQ,EACrCC,kBAA8C,IAA5BN,EAAOM,iBAG7B,CACA,MAAMC,EAAgCrJ,OAAOoE,OAAO,MAC9CkF,EAAqB,CACzB,4BACA,0BAEIC,EAAc,GACpB,KAAOD,EAAmB/H,OAAS,GACC,IAA9B+H,EAAmB/H,QAGjBkF,KAAKwB,SAAW,GAFpBsB,EAAY5F,KAAK2F,EAAmB1H,SAKlC2H,EAAY5F,KAAK2F,EAAmBxH,OAO1C,SAAS0H,EAAenI,EAAUoI,GAChC,MAAMC,EAASb,EAAgBY,GAC/B,OAAe,OAAXC,IAGJL,EAAchI,GAAYqI,GACnB,EACT,CACA,SAASC,EAAatI,GACpB,OAAOgI,EAAchI,EACvB,CAbAgI,EAAc,IAAMR,EAAgB,CAClCE,UAAW,CAAC,8BAA8B9M,OAAOsN,KAiBnD,MAAMK,EAAcA,CAACC,EAAMC,KACzB,IAAI9H,EAAS6H,EAAME,GAAqC,IAAzB/H,EAAOgI,QAAQ,KAuB9C,OAVAhK,OAAOC,KAAK6J,GAAQpG,SAASlB,IAC3B,IAAItB,EACJ,IACEA,EAfJ,SAAuBA,GACrB,cAAeA,GACb,IAAK,UACH,OAAOA,EAAQ,OAAS,QAC1B,IAAK,SAEL,IAAK,SACH,OAAO+I,mBAAmB/I,GAC5B,QACE,MAAM,IAAIgJ,MAAM,qBAEtB,CAIYC,CAAcL,EAAOtH,GAG/B,CAFE,MAAOmC,IACP,MACF,CACA3C,IAAW+H,EAAY,IAAM,KAAOE,mBAAmBzH,GAAO,IAAMtB,EACpE6I,GAAY,CAAI,IAEX/H,CAAM,EAGToI,EAAiB,CAAC,EAClBC,EAAY,CAAC,EAYnB,IAAIC,EAXgBC,MAClB,IAAIjH,EACJ,IAEE,GADAA,EAAWkH,MACa,oBAAblH,EACT,OAAOA,CAGX,CADE,MAAOqB,IACT,CACA,OAAO,IAAI,EAEK4F,GA8ElB,MAkDME,EAAiB,CACrBC,QA/FcA,CAACrJ,EAAUU,EAAQe,KACjC,MAAM6H,EAAU,GAChB,IAAIC,EAAYR,EAAerI,QACb,IAAd6I,IACFA,EA/BJ,SAA4BvJ,EAAUU,GACpC,MAAM2H,EAASC,EAAatI,GAC5B,IAAKqI,EACH,OAAO,EAET,IAAI1H,EACJ,GAAK0H,EAAOT,OAEL,CACL,IAAI4B,EAAgB,EACpBnB,EAAOX,UAAUrF,SAAST,IACxB,MAAM6H,EAAO7H,EACb4H,EAAgBpE,KAAKsE,IAAIF,EAAeC,EAAKvJ,OAAO,IAEtD,MAAMyJ,EAAMpB,EAAY7H,EAAS,QAAS,CACxCe,MAAO,KAETd,EAAS0H,EAAOT,OAAS4B,EAAgBnB,EAAOV,KAAKzH,OAASyJ,EAAIzJ,MACpE,MAXES,EAAS,EAYX,MAAMiJ,EAAW5J,EAAW,IAAMU,EAGlC,OAFAsI,EAAUhJ,GAAYqI,EAAOV,KAC7BoB,EAAea,GAAYjJ,EACpBA,CACT,CAQgBkJ,CAAmB7J,EAAUU,IAE3C,MAAMoJ,EAAO,QACb,IAAIlI,EAAO,CACTkI,OACA9J,WACAU,SACAe,MAAO,IAELvB,EAAS,EAgBb,OAfAuB,EAAMY,SAAQ,CAAClH,EAAM2M,KACnB5H,GAAU/E,EAAK+E,OAAS,EACpBA,GAAUqJ,GAAazB,EAAQ,IACjCwB,EAAQhH,KAAKV,GACbA,EAAO,CACLkI,OACA9J,WACAU,SACAe,MAAO,IAETvB,EAAS/E,EAAK+E,QAEhB0B,EAAKH,MAAMa,KAAKnH,EAAK,IAEvBmO,EAAQhH,KAAKV,GACN0H,CAAO,EAmEdS,KApDWA,CAACN,EAAMhB,EAAQxG,KAC1B,IAAKgH,EAEH,YADAhH,EAAS,QAAS,KAGpB,IAAI0F,EAlBN,SAAiB3H,GACf,GAAwB,kBAAbA,EAAuB,CAChC,QAA4B,IAAxBgJ,EAAUhJ,GAAsB,CAClC,MAAMqI,EAASC,EAAatI,GAC5B,IAAKqI,EACH,MAAO,IAETW,EAAUhJ,GAAYqI,EAAOV,IAC/B,CACA,OAAOqB,EAAUhJ,EACnB,CACA,MAAO,GACT,CAMarF,CAAQ8N,EAAOzI,UAC1B,OAAQyI,EAAOqB,MACb,IAAK,QAAS,CACZ,MAAMpJ,EAAS+H,EAAO/H,OAEhBsJ,EADQvB,EAAOhH,MACGX,KAAK,KAC7B6G,GAAQY,EAAY7H,EAAS,QAAS,CACpCe,MAAOuI,IAET,KACF,CACA,IAAK,SAAU,CACb,MAAMC,EAAMxB,EAAOwB,IACnBtC,GAA4B,MAApBsC,EAAI3J,MAAM,EAAG,GAAa2J,EAAI3J,MAAM,GAAK2J,EACjD,KACF,CACA,QAEE,YADAhI,EAAS,QAAS,KAGtB,IAAIiI,EAAe,IACnBjB,EAAYQ,EAAO9B,GAAMwC,MAAMC,IAC7B,MAAMC,EAASD,EAASC,OACxB,GAAe,MAAXA,EAOJ,OADAH,EAAe,IACRE,EAASE,OANdC,YAAW,KACTtI,EA7ER,SAAqBoI,GACnB,OAAkB,MAAXA,CACT,CA2EiBG,CAAYH,GAAU,QAAU,OAAQA,EAAO,GAKtC,IACrBF,MAAMxK,IACa,kBAATA,GAA8B,OAATA,EAMhC4K,YAAW,KACTtI,EAAS,UAAWtC,EAAK,IANzB4K,YAAW,KACTtI,EAAS,OAAQiI,EAAa,GAMhC,IACDO,OAAM,KACPxI,EAAS,OAAQiI,EAAa,GAC9B,GA8DJ,MAAMQ,EAA4B/L,OAAOoE,OAAO,MAC1C4H,EAAiChM,OAAOoE,OAAO,MACrD,SAAS6H,EAAeC,EAAS5D,GAC/B4D,EAAQxI,SAASoF,IACf,MAAMzH,EAAWyH,EAAOzH,SACxB,QAA4B,IAAxB0K,EAAU1K,GACZ,OAEF,MAAM8K,EAAoBJ,EAAU1K,GAC9BU,EAAS+G,EAAO/G,OAChBqK,EAAQD,EAAkBpK,GAC5BqK,IACFD,EAAkBpK,GAAUqK,EAAMC,QAAQC,GAAQA,EAAIhE,KAAOA,IAC/D,GAEJ,CA4DA,IAAIiE,EAAY,EA4ChB,IAAIC,EAAgB,CAClBzD,UAAW,GACXI,MAAO,EACPD,QAAS,IACTtI,OAAQ,IACRqH,QAAQ,EACRmB,kBAAkB,GAIpB,SAASqD,EAAU/C,EAAQgD,EAASC,EAAOC,GACzC,MAAMC,EAAiBnD,EAAOX,UAAUxH,OAClCuL,EAAapD,EAAOzB,OAASxB,KAAKkB,MAAMlB,KAAKwB,SAAW4E,GAAkBnD,EAAOP,MACvF,IAAIJ,EACJ,GAAIW,EAAOzB,OAAQ,CACjB,IAAI8E,EAAOrD,EAAOX,UAAUpH,MAAM,GAElC,IADAoH,EAAY,GACLgE,EAAKxL,OAAS,GAAG,CACtB,MAAMyL,EAAYvG,KAAKkB,MAAMlB,KAAKwB,SAAW8E,EAAKxL,QAClDwH,EAAUpF,KAAKoJ,EAAKC,IACpBD,EAAOA,EAAKpL,MAAM,EAAGqL,GAAW/Q,OAAO8Q,EAAKpL,MAAMqL,EAAY,GAChE,CACAjE,EAAYA,EAAU9M,OAAO8Q,EAC/B,MACEhE,EAAYW,EAAOX,UAAUpH,MAAMmL,GAAY7Q,OAAOyN,EAAOX,UAAUpH,MAAM,EAAGmL,IAElF,MAAMG,EAAY9H,KAAKC,MACvB,IAEI8H,EAFAxB,EAAS,UACTyB,EAAc,EAEdC,EAAQ,KACRC,EAAQ,GACRC,EAAgB,GAIpB,SAASC,IACHH,IACFI,aAAaJ,GACbA,EAAQ,KAEZ,CACA,SAASK,IACQ,YAAX/B,IACFA,EAAS,WAEX6B,IACAF,EAAM3J,SAAST,IACO,YAAhBA,EAAKyI,SACPzI,EAAKyI,OAAS,UAChB,IAEF2B,EAAQ,EACV,CACA,SAASK,EAAUpK,EAAUqK,GACvBA,IACFL,EAAgB,IAEM,oBAAbhK,GACTgK,EAAc3J,KAAKL,EAEvB,CAYA,SAASsK,IACPlC,EAAS,SACT4B,EAAc5J,SAASJ,IACrBA,OAAS,EAAQ4J,EAAU,GAE/B,CACA,SAASW,IACPR,EAAM3J,SAAST,IACO,YAAhBA,EAAKyI,SACPzI,EAAKyI,OAAS,UAChB,IAEF2B,EAAQ,EACV,CA4CA,SAASS,IACP,GAAe,YAAXpC,EACF,OAEF6B,IACA,MAAMQ,EAAWhF,EAAUnH,QAC3B,QAAiB,IAAbmM,EACF,OAAIV,EAAM9L,YACR6L,EAAQxB,YAAW,KACjB2B,IACe,YAAX7B,IACFmC,IACAD,IACF,GACClE,EAAOR,eAGZ0E,IAGF,MAAM3K,EAAO,CACXyI,OAAQ,UACRqC,WACAzK,SAAUA,CAAC0K,EAAShN,MAlExB,SAAwBiC,EAAMwI,EAAUzK,GACtC,MAAMiN,EAAuB,YAAbxC,EAEhB,OADA4B,EAAQA,EAAMhB,QAAQ6B,GAAWA,IAAWjL,IACpCyI,GACN,IAAK,UACH,MACF,IAAK,SACH,GAAIuC,IAAYvE,EAAON,iBACrB,OAEF,MACF,QACE,OAEJ,GAAiB,UAAbqC,EAGF,OAFAyB,EAAYlM,OACZ4M,IAGF,GAAIK,EASF,OARAf,EAAYlM,OACPqM,EAAM9L,SACJwH,EAAUxH,OAGbuM,IAFAF,MASN,GAFAL,IACAM,KACKnE,EAAOzB,OAAQ,CAClB,MAAMkB,EAAQO,EAAOX,UAAUiB,QAAQ/G,EAAK8K,WAC7B,IAAX5E,GAAgBA,IAAUO,EAAOP,QACnCO,EAAOP,MAAQA,EAEnB,CACAuC,EAAS,YACT4B,EAAc5J,SAASJ,IACrBA,EAAStC,EAAK,GAElB,CAyBMmN,CAAelL,EAAM+K,EAAShN,EAAK,GAGvCqM,EAAM1J,KAAKV,GACXkK,IACAC,EAAQxB,WAAWkC,EAAUpE,EAAO9I,QACpC+L,EAAMoB,EAAUrB,EAASzJ,EAAKK,SAChC,CAEA,MAlIoB,oBAATsJ,GACTU,EAAc3J,KAAKiJ,GAgIrBhB,WAAWkC,GApGX,WACE,MAAO,CACLb,YACAP,UACAhB,SACAyB,cACAiB,eAAgBf,EAAM9L,OACtBmM,YACAD,QAEJ,CA4FF,CAkBA,SAASY,EAAeC,GACtB,MAAM5E,EAhBR,SAAmBA,GACjB,GAAsB,kBAAXA,GAAmD,kBAArBA,EAAOX,aAA4BW,EAAOX,qBAAqB5I,SAAWuJ,EAAOX,UAAUxH,OAClI,MAAM,IAAI2I,MAAM,oCAElB,MAAMqE,EAA4BvO,OAAOoE,OAAO,MAChD,IAAI5B,EACJ,IAAKA,KAAOgK,OACU,IAAhB9C,EAAOlH,GACT+L,EAAU/L,GAAOkH,EAAOlH,GAExB+L,EAAU/L,GAAOgK,EAAchK,GAGnC,OAAO+L,CACT,CAEiBC,CAAUF,GACzB,IAAIG,EAAU,GACd,SAASC,IACPD,EAAUA,EAAQpC,QAAQpJ,GAA2B,YAAlBA,IAAOyI,QAC5C,CA0BA,MATiB,CACfiB,MAjBF,SAAeD,EAASiC,EAAeC,GACrC,MAAMC,EAASpC,EAAU/C,EAAQgD,EAASiC,GAAe,CAAC3N,EAAMxF,KAC9DkT,IACIE,GACFA,EAAa5N,EAAMxF,EACrB,IAGF,OADAiT,EAAQ9K,KAAKkL,GACNA,CACT,EASEC,KARF,SAAcxL,GACZ,MAAMtB,EAASyM,EAAQK,MAAM5N,GACpBoC,EAASpC,KAElB,YAAkB,IAAXc,EAAoBA,EAAS,IACtC,EAIE+M,SAAW5F,IACTO,EAAOP,MAAQA,CAAK,EAEtB6F,SAAUA,IAAMtF,EAAOP,MACvBuF,UAGJ,CAEA,SAASO,KACT,CACA,MAAMC,GAAkClP,OAAOoE,OAAO,MAgBtD,SAAS+K,GAAaC,EAAQzC,EAAOrJ,GACnC,IAAI+L,EACAjE,EACJ,GAAsB,kBAAXgE,EAAqB,CAC9B,MAAME,EAAM1G,EAAawG,GACzB,IAAKE,EAEH,OADAhM,OAAS,EAAQ,KACV2L,GAET7D,EAAOkE,EAAIlE,KACX,MAAMmE,EAzBV,SAA4BlO,GAC1B,QAAkC,IAA9B6N,GAAgB7N,GAAsB,CACxC,MAAMqI,EAASC,EAAatI,GAC5B,IAAKqI,EACH,OAEF,MACM8F,EAAkB,CACtB9F,SACA2F,WAHiBhB,EAAe3E,IAKlCwF,GAAgB7N,GAAYmO,CAC9B,CACA,OAAON,GAAgB7N,EACzB,CAWmBoO,CAAmBL,GAC9BG,IACFF,EAAaE,EAAOF,WAExB,KAAO,CACL,MAAM3F,EAASb,EAAgBuG,GAC/B,GAAI1F,EAAQ,CACV2F,EAAahB,EAAe3E,GAC5B,MACM4F,EAAM1G,EADMwG,EAAOrG,UAAYqG,EAAOrG,UAAU,GAAK,IAEvDuG,IACFlE,EAAOkE,EAAIlE,KAEf,CACF,CACA,OAAKiE,GAAejE,EAIbiE,EAAW1C,MAAMA,EAAOvB,EAAM9H,EAA9B+L,GAA0C5B,OAH/CnK,OAAS,EAAQ,KACV2L,GAGX,CAEA,MAAMS,GAAQ,CAAC,EAEf,SAASC,KACT,CACA,MAAMC,GAA+B5P,OAAOoE,OAAO,MAC7CyL,GAA8B7P,OAAOoE,OAAO,MAC5C0L,GAA8B9P,OAAOoE,OAAO,MAC5C2L,GAA6B/P,OAAOoE,OAAO,MACjD,SAAS4L,GAAe3O,EAAUU,QACF,IAA1B+N,GAAYzO,KACdyO,GAAYzO,GAA4BrB,OAAOoE,OAAO,OAExD,MAAM6L,EAAsBH,GAAYzO,GACnC4O,EAAoBlO,KACvBkO,EAAoBlO,IAAU,EAC9B6J,YAAW,KACTqE,EAAoBlO,IAAU,EAjYpC,SAAyBV,EAAUU,QACA,IAA7BiK,EAAe3K,KACjB2K,EAAe3K,GAA4BrB,OAAOoE,OAAO,OAE3D,MAAM8L,EAAyBlE,EAAe3K,GACzC6O,EAAuBnO,KAC1BmO,EAAuBnO,IAAU,EACjC6J,YAAW,KAET,GADAsE,EAAuBnO,IAAU,OACL,IAAxBgK,EAAU1K,SAAwD,IAAhC0K,EAAU1K,GAAUU,GACxD,OAEF,MAAMqK,EAAQL,EAAU1K,GAAUU,GAAQJ,MAAM,GAChD,IAAKyK,EAAM7K,OACT,OAEF,MAAMmD,EAAUE,EAAWvD,EAAUU,GACrC,IAAIoO,GAAa,EACjB/D,EAAM1I,SAAST,IACb,MAAMH,EAAQG,EAAKH,MACbsN,EAAYtN,EAAMuN,QAAQ9O,OAChCuB,EAAMuN,QAAUvN,EAAMuN,QAAQhE,QAAQjK,IACpC,GAAIA,EAAKL,SAAWA,EAClB,OAAO,EAET,MAAMvF,EAAO4F,EAAK5F,KAClB,QAA4B,IAAxBkI,EAAQ5B,MAAMtG,GAChBsG,EAAMwN,OAAO3M,KAAK,CAChBtC,WACAU,SACAvF,aAEG,SAA8B,IAA1BkI,EAAQI,QAAQtI,GAQzB,OADA2T,GAAa,GACN,EAPPrN,EAAMgC,QAAQnB,KAAK,CACjBtC,WACAU,SACAvF,QAKJ,CACA,OAAO,CAAK,IAEVsG,EAAMuN,QAAQ9O,SAAW6O,IACtBD,GACHlE,EAAe,CACb,CACE5K,WACAU,WAEDkB,EAAKqF,IAEVrF,EAAKK,SAASR,EAAMwN,OAAO3O,MAAM,GAAImB,EAAMgC,QAAQnD,MAAM,GAAImB,EAAMuN,QAAQ1O,MAAM,GAAIsB,EAAKwK,OAC5F,GACA,IAGR,CAwUM8C,CAAgBlP,EAAUU,EAAO,IAGvC,CACA,MAAMyO,GAA8BxQ,OAAOoE,OAAO,MAClD,SAASqM,GAAapP,EAAUU,EAAQe,QASR,IAA1B+M,GAAYxO,KACdwO,GAAYxO,GAA4BrB,OAAOoE,OAAO,OAExD,MAAMsM,EAAsBb,GAAYxO,QACX,IAAzB0O,GAAW1O,KACb0O,GAAW1O,GAA4BrB,OAAOoE,OAAO,OAEvD,MAAMuM,EAAqBZ,GAAW1O,QACP,IAA3BuO,GAAavO,KACfuO,GAAavO,GAA4BrB,OAAOoE,OAAO,OAEzD,MAAMwM,EAAuBhB,GAAavO,QACN,IAAhCqP,EAAoB3O,GACtB2O,EAAoB3O,GAAUe,EAE9B4N,EAAoB3O,GAAU2O,EAAoB3O,GAAQ9F,OAAO6G,GAAO+N,OAErEF,EAAmB5O,KACtB4O,EAAmB5O,IAAU,EAC7B6J,YAAW,KACT+E,EAAmB5O,IAAU,EAC7B,MAAM+O,EAASJ,EAAoB3O,UAC5B2O,EAAoB3O,GAC3B,MAAMuN,EAAM1G,EAAavH,GACzB,IAAKiO,EAEH,YAlCN,WACE,MAAM9M,GAAoB,KAAbnB,EAAkB,GAAK,IAAMA,EAAW,KAAOU,EACtDgP,EAAOtK,KAAKkB,MAAMxC,KAAKC,MAAQ,KACjCoL,GAAYhO,GAAOuO,IACrBP,GAAYhO,GAAOuO,EACnBC,QAAQxV,MAAM,iCAAmCgH,EAAM,6CAE3D,CA0BMmC,GAGa2K,EAAI5E,QAAQrJ,EAAUU,EAAQ+O,GACtCpN,SAAST,IACdkM,GAAa9N,EAAU4B,GAAM,CAACjC,EAAMxF,KAClC,MAAMkJ,EAAUE,EAAWvD,EAAUU,GACrC,GAAoB,kBAATf,EAAmB,CAC5B,GAAc,MAAVxF,EACF,OAEF,MAAM0J,EAAIC,KAAKC,MACfnC,EAAKH,MAAMY,SAASlH,IAClBkI,EAAQI,QAAQtI,GAAQ0I,CAAC,GAE7B,MACE,IACE,MAAM+L,EAASjM,EAAWN,EAAS1D,GACnC,IAAKiQ,EAAO1P,OACV,OAEF,MAAM8O,EAAUO,EAAqB7O,GACrCkP,EAAOvN,SAASlH,WACP6T,EAAQ7T,EAAK,IAElBkT,GAAMwB,OACRxB,GAAMwB,MAAM7P,EAAUL,EAI1B,CAFE,MAAOmQ,GACPH,QAAQxV,MAAM2V,EAChB,CAEFnB,GAAe3O,EAAUU,EAAO,GAChC,GACF,IAGR,CACA,MAAMqP,GAAYA,CAACtO,EAAOQ,KACxB,MAAM+N,EAzXR,SAAqBtE,GAA4C,IAAtC5L,IAAQG,UAAAC,OAAA,QAAAC,IAAAF,UAAA,KAAAA,UAAA,GAASgE,EAAWhE,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACrD,MAAMU,EAAS,GAWf,OAVA+K,EAAKrJ,SAAST,IACZ,MAAMb,EAAuB,kBAATa,EAAoBhC,EAAagC,GAAM,EAAOqC,GAAerC,EAC5E9B,IAAYc,EAAaG,EAAMkD,IAClCtD,EAAO2B,KAAK,CACVtC,SAAUe,EAAKf,SACfU,OAAQK,EAAKL,OACbvF,KAAM4F,EAAK5F,MAEf,IAEKwF,CACT,CA4WuBsP,CAAYxO,GAAO,EAAMyC,KACxCgM,EAxhBR,SAAmBzO,GACjB,MAAMd,EAAS,CACbsO,OAAQ,GACRxL,QAAS,GACTuL,QAAS,IAEL3L,EAA0B1E,OAAOoE,OAAO,MAC9CtB,EAAM+N,MAAK,CAACW,EAAGC,IACTD,EAAEnQ,WAAaoQ,EAAEpQ,SACZmQ,EAAEnQ,SAASqQ,cAAcD,EAAEpQ,UAEhCmQ,EAAEzP,SAAW0P,EAAE1P,OACVyP,EAAEzP,OAAO2P,cAAcD,EAAE1P,QAE3ByP,EAAEhV,KAAKkV,cAAcD,EAAEjV,QAEhC,IAAImV,EAAW,CACbtQ,SAAU,GACVU,OAAQ,GACRvF,KAAM,IAiCR,OA/BAsG,EAAMY,SAAStB,IACb,GAAIuP,EAASnV,OAAS4F,EAAK5F,MAAQmV,EAAS5P,SAAWK,EAAKL,QAAU4P,EAAStQ,WAAae,EAAKf,SAC/F,OAEFsQ,EAAWvP,EACX,MAAMf,EAAWe,EAAKf,SAChBU,EAASK,EAAKL,OACdvF,EAAO4F,EAAK5F,UACQ,IAAtBkI,EAAQrD,KACVqD,EAAQrD,GAA4BrB,OAAOoE,OAAO,OAEpD,MAAMS,EAAkBH,EAAQrD,QACA,IAA5BwD,EAAgB9C,KAClB8C,EAAgB9C,GAAU6C,EAAWvD,EAAUU,IAEjD,MAAM6P,EAAe/M,EAAgB9C,GACrC,IAAIgL,EAEFA,OAD+B,IAA7B6E,EAAa9O,MAAMtG,GACdwF,EAAOsO,OACM,KAAXvO,QAAgD,IAA/B6P,EAAa9M,QAAQtI,GACxCwF,EAAO8C,QAEP9C,EAAOqO,QAEhB,MAAMpN,EAAO,CACX5B,WACAU,SACAvF,QAEFuQ,EAAKpJ,KAAKV,EAAK,IAEVjB,CACT,CAmesB6P,CAAUR,GAC9B,IAAKE,EAAYlB,QAAQ9O,OAAQ,CAC/B,IAAIuQ,GAAe,EAQnB,OAPIxO,GACFsI,YAAW,KACLkG,GACFxO,EAASiO,EAAYjB,OAAQiB,EAAYzM,QAASyM,EAAYlB,QAASV,GACzE,IAGG,KACLmC,GAAe,CAAK,CAExB,CACA,MAAMC,EAA2B/R,OAAOoE,OAAO,MACzC8H,EAAU,GAChB,IAAI8F,EAAcC,EAClBV,EAAYlB,QAAQ3M,SAAStB,IAC3B,MAAMf,EAAWe,EAAKf,SAChBU,EAASK,EAAKL,OACpB,GAAIA,IAAWkQ,GAAc5Q,IAAa2Q,EACxC,OAEFA,EAAe3Q,EACf4Q,EAAalQ,EACbmK,EAAQvI,KAAK,CACXtC,WACAU,gBAE6B,IAA3B6N,GAAavO,KACfuO,GAAavO,GAA4BrB,OAAOoE,OAAO,OAEzD,MAAMwM,EAAuBhB,GAAavO,QACL,IAAjCuP,EAAqB7O,KACvB6O,EAAqB7O,GAA0B/B,OAAOoE,OAAO,YAEpC,IAAvB2N,EAAS1Q,KACX0Q,EAAS1Q,GAA4BrB,OAAOoE,OAAO,OAErD,MAAM8N,EAAmBH,EAAS1Q,QACD,IAA7B6Q,EAAiBnQ,KACnBmQ,EAAiBnQ,GAAU,GAC7B,IAEF,MAAMgP,EAAO5L,KAAKC,MAkBlB,OAjBAmM,EAAYlB,QAAQ3M,SAAStB,IAC3B,MAAMf,EAAWe,EAAKf,SAChBU,EAASK,EAAKL,OACdvF,EAAO4F,EAAK5F,KACZ2V,EAAevC,GAAavO,GAAUU,QACjB,IAAvBoQ,EAAa3V,KACf2V,EAAa3V,GAAQuU,EACrBgB,EAAS1Q,GAAUU,GAAQ4B,KAAKnH,GAClC,IAEF0P,EAAQxI,SAASoF,IACf,MAAMzH,EAAWyH,EAAOzH,SAClBU,EAAS+G,EAAO/G,OAClBgQ,EAAS1Q,GAAUU,GAAQR,QAC7BkP,GAAapP,EAAUU,EAAQgQ,EAAS1Q,GAAUU,GACpD,IAEKuB,EAndT,SAAuBA,EAAUR,EAAOsP,GACtC,MAAM9J,EAAKiE,IACLkB,EAAQxB,EAAeoG,KAAK,KAAMD,EAAgB9J,GACxD,IAAKxF,EAAMuN,QAAQ9O,OACjB,OAAOkM,EAET,MAAMxK,EAAO,CACXqF,KACAxF,QACAQ,WACAmK,SAcF,OAZA2E,EAAe1O,SAASoF,IACtB,MAAMzH,EAAWyH,EAAOzH,SAClBU,EAAS+G,EAAO/G,YACM,IAAxBgK,EAAU1K,KACZ0K,EAAU1K,GAA4BrB,OAAOoE,OAAO,OAEtD,MAAM+H,EAAoBJ,EAAU1K,QACF,IAA9B8K,EAAkBpK,KACpBoK,EAAkBpK,GAAU,IAE9BoK,EAAkBpK,GAAQ4B,KAAKV,EAAK,IAE/BwK,CACT,CA0boB6E,CAAchP,EAAUiO,EAAarF,GAAWyD,EAAa,EAmB3E4C,GAAe,WACfC,GAAc,UACdC,GAAWD,GAAc,SACzBE,GAAaF,GAAc,WAC3BG,GAAO,KAEPjJ,GAAS,CACbkJ,OAAO,EACPC,SAAS,GAEX,IAAIvC,IAAS,EACb,MAAMwC,GAAQ,CACZF,MAAO,EACPC,QAAS,GAELE,GAAY,CAChBH,MAAO,GACPC,QAAS,IAEX,IAAIG,GAA4B,qBAAX1O,OAAyB,CAAC,EAAIA,OACnD,SAAS2O,GAAUzQ,GACjB,MAAM0D,EAAO1D,EAAM,UACnB,IACE,GAAIwQ,IAAWA,GAAQ9M,IAAyC,kBAAzB8M,GAAQ9M,GAAM3E,OACnD,OAAOyR,GAAQ9M,EAGnB,CADE,MAAOvB,IACT,CAEA,OADA+E,GAAOlH,IAAO,EACP,IACT,CACA,SAAS0Q,GAASxO,EAASlC,EAAKtB,GAC9B,IAGE,OAFAwD,EAAQyO,QAAQV,GAAUvR,EAAMwG,YAChCoL,GAAMtQ,GAAOtB,GACN,CAGT,CAFE,MAAOyD,IACP,OAAO,CACT,CACF,CACA,SAASyO,GAAS1O,GAChB,MAAM2O,EAAS3O,EAAQ4O,QAAQb,IAC/B,GAAIY,EAAQ,CACV,MAAME,EAAQC,SAASH,GACvB,OAAOE,GAAgB,CACzB,CACA,OAAO,CACT,CAiBA,MAAME,GAAYA,KAChB,GAAInD,GACF,OAEFA,IAAS,EACT,MAAMoD,EAAUjN,KAAKkB,MAAMxC,KAAKC,MAAQuN,IAhElB,IAiEtB,SAASgB,EAAKnR,GACZ,MAAMoR,EAAOX,GAAUzQ,GACvB,IAAKoR,EACH,OAEF,MAAMN,EAAWnK,IACf,MAAM3M,EAAOgW,GAAcrJ,EAAMzB,WAC3BzE,EAAO2Q,EAAKN,QAAQ9W,GAC1B,GAAoB,kBAATyG,EACT,OAAO,EAET,IAAI4Q,GAAQ,EACZ,IACE,MAAM7S,EAAO8S,KAAKC,MAAM9Q,GACxB,GAAoB,kBAATjC,GAA4C,kBAAhBA,EAAKuO,QAAuBvO,EAAKuO,OAASmE,GAAoC,kBAAlB1S,EAAKK,UAA8C,kBAAdL,EAAKA,MAAiD,kBAArBA,EAAKA,KAAKe,OACjL8R,GAAQ,MACH,CACL,MAAMxS,EAAWL,EAAKK,SAChBU,EAASf,EAAKA,KAAKe,OAEzB8R,EAAQ7O,EADQJ,EAAWvD,EAAUU,GACTf,EAAKA,MAAMO,OAAS,CAClD,CAGF,CAFE,MAAOoD,IACPkP,GAAQ,CACV,CAIA,OAHKA,GACHD,EAAKI,WAAWxX,GAEXqX,CAAK,EAEd,IACE,MAAMpP,EAAUmP,EAAKN,QAAQZ,IAC7B,GAAIjO,IAAY8N,GAKd,OAJI9N,GAhDZ,SAAsBC,GACpB,IACE,MAAM6O,EAAQH,GAAS1O,GACvB,IAAK,IAAIuP,EAAI,EAAGA,EAAIV,EAAOU,IACzBvP,EAAQsP,WAAWxB,GAAcyB,EAAEvM,WAGvC,CADE,MAAO/C,IACT,CACF,CAyCUuP,CAAaN,QAxDvB,SAAmBlP,EAASlC,GAC1B,IACEkC,EAAQyO,QAAQT,GAAYH,GAE9B,CADE,MAAO5N,IACT,CACAuO,GAASxO,EAASlC,EAAK,EACzB,CAoDQ2R,CAAUP,EAAMpR,GAGlB,IAAI+Q,EAAQH,GAASQ,GACrB,IAAK,IAAIK,EAAIV,EAAQ,EAAGU,GAAK,EAAGA,IACzBX,EAAQW,KACPA,IAAMV,EAAQ,EAChBA,IAEAR,GAAUvQ,GAAKmB,KAAKsQ,IAI1Bf,GAASU,EAAMpR,EAAK+Q,EAEtB,CADE,MAAO5O,IACT,CACF,CACA,IAAK,MAAMnC,KAAOkH,GAChBiK,EAAKnR,EACP,EAEI4R,GAAaA,CAAC/S,EAAUL,KAI5B,SAASkQ,EAAM1O,GACb,IAAKkH,GAAOlH,GACV,OAAO,EAET,MAAMoR,EAAOX,GAAUzQ,GACvB,IAAKoR,EACH,OAAO,EAET,IAAIzK,EAAQ4J,GAAUvQ,GAAKZ,QAC3B,QAAc,IAAVuH,IACFA,EAAQ2J,GAAMtQ,IACT0Q,GAASU,EAAMpR,EAAK2G,EAAQ,IAC/B,OAAO,EAGX,IACE,MAAMlG,EAAO,CACXsM,OAAQ9I,KAAKkB,MAAMxC,KAAKC,MAAQuN,IAChCtR,WACAL,QAEF4S,EAAKT,QAAQX,GAAcrJ,EAAMzB,WAAYoM,KAAKO,UAAUpR,GAG9D,CAFE,MAAO0B,IACP,OAAO,CACT,CACA,OAAO,CACT,CA7BK2L,IACHmD,KA6BGzT,OAAOC,KAAKe,EAAK8B,OAAOvB,SAGzBP,EAAKyC,kBACPzC,EAAOhB,OAAO+C,OAAO,CAAC,EAAG/B,IACbyC,UAETyN,EAAM,UACTA,EAAM,WACR,EAiBF,MAAMoD,GAAY,SAClB,SAASC,GAAeC,EAAQC,GAC9BA,EAAK/S,MAAM4S,IAAW5Q,SAASgR,IAE7B,OADcA,EAAIC,QAEhB,IAAK,aACHH,EAAO1T,OAAQ,EACf,MACF,IAAK,WACH0T,EAAO3T,OAAQ,EAEnB,GAEJ,CACA,SAAS+T,GAAoBJ,EAAQK,GACnCA,EAAMnT,MAAM4S,IAAW5Q,SAASgR,IAC9B,MAAMxT,EAAQwT,EAAIC,OAClB,OAAQzT,GACN,IAAK,OACL,IAAK,SACL,IAAK,QACHsT,EAAO1O,OAAS5E,EAChB,MACF,IAAK,MACL,IAAK,SACL,IAAK,SACHsT,EAAOzO,OAAS7E,EAChB,MACF,IAAK,QACL,IAAK,OACHsT,EAAO7S,OAAQ,EACf,MACF,IAAK,OACH6S,EAAO7S,OAAQ,EACnB,GAEJ,CAEA,SAASmT,GAAiB5T,GAAyB,IAAlB6T,EAAYzT,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAC9C,MAAM0T,EAAQ9T,EAAMuH,QAAQ,aAAc,IAC1C,SAASiG,EAAQuG,GACf,KAAOA,EAAS,GACdA,GAAU,EAEZ,OAAOA,EAAS,CAClB,CACA,GAAc,KAAVD,EAAc,CAChB,MAAMhO,EAAMwM,SAAStS,GACrB,OAAOgG,MAAMF,GAAO,EAAI0H,EAAQ1H,EAClC,CAAO,GAAIgO,IAAU9T,EAAO,CAC1B,IAAIQ,EAAQ,EACZ,OAAQsT,GACN,IAAK,IACHtT,EAAQ,GACR,MACF,IAAK,MACHA,EAAQ,GAEZ,GAAIA,EAAO,CACT,IAAIsF,EAAMC,WAAW/F,EAAMS,MAAM,EAAGT,EAAMK,OAASyT,EAAMzT,SACzD,OAAI2F,MAAMF,GACD,GAETA,GAAYtF,EACLsF,EAAM,IAAM,EAAI0H,EAAQ1H,GAAO,EACxC,CACF,CACA,OAAO+N,CACT,CAKA,MAAMG,GAAc,CAChB,MAAS,6BACT,WAAc,+BACd,eAAe,EACf,KAAQ,MACR,MAAS,CAAC,GAKRC,GAAiB,IAAKvP,EAAUC,QAAQ,GAgI9C,GANAN,GAAiB,GAEjBoD,EAAa,GAAI8B,GAIO,qBAAb2K,UAA8C,qBAAX9Q,OAAwB,CAElEoL,GAAMwB,MAAQkD,GACdX,KACA,MAAMT,EAAU1O,OAEhB,QAA+B,IAA3B0O,EAAQqC,eAA2B,CACnC,MAAMC,EAAUtC,EAAQqC,eAClB1Q,EAAM,iCACW,kBAAZ2Q,GAAoC,OAAZA,IAC9BA,aAAmBnV,MAAQmV,EAAU,CAACA,IAAU5R,SAAST,IACtD,KAGoB,kBAATA,GACM,OAATA,GACAA,aAAgB9C,OAEM,kBAAf8C,EAAKH,OACW,kBAAhBG,EAAKlB,SA1gDpC,SAAuBf,EAAMK,GAC3B,GAAoB,kBAATL,EACT,OAAO,EAKT,GAHwB,kBAAbK,IACTA,EAAoC,kBAAlBL,EAAKK,SAAwBL,EAAKK,SAAW,IAE7DiE,GAA4B,KAAbjE,IAA2C,kBAAhBL,EAAKe,QAAuC,KAAhBf,EAAKe,QAAgB,CAC7F,IAAIwT,GAAQ,EASZ,OARIvR,EAAuBhD,KACzBA,EAAKe,OAAS,GACdsB,EAAarC,GAAM,CAACxE,EAAM4F,KACpBA,GAAQsD,EAAQlJ,EAAM4F,KACxBmT,GAAQ,EACV,KAGGA,CACT,CACA,QAA2B,kBAAhBvU,EAAKe,SAAwBE,EAAa,CACnDZ,WACAU,OAAQf,EAAKe,OACbvF,KAAM,UAKCwI,EADOJ,EAAWvD,EAAUL,EAAKe,QACbf,EAC/B,CAg/CyBwU,CAAcvS,KACf+N,QAAQxV,MAAMmJ,EAKtB,CAFA,MAAO8Q,GACHzE,QAAQxV,MAAMmJ,EAClB,IAGZ,CAEA,QAAiC,IAA7BqO,EAAQ0C,iBAA6B,CACrC,MAAMC,EAAY3C,EAAQ0C,iBAC1B,GAAyB,kBAAdC,GAAwC,OAAdA,EACjC,IAAK,IAAInT,KAAOmT,EAAW,CACvB,MAAMhR,EAAM,oBAAsBnC,EAAM,gBACxC,IACI,MAAMtB,EAAQyU,EAAUnT,GACxB,GAAqB,kBAAVtB,IACNA,QACmB,IAApBA,EAAM6H,UACN,SAECS,EAAehH,EAAKtB,IACrB8P,QAAQxV,MAAMmJ,EAKtB,CAFA,MAAO8Q,IACHzE,QAAQxV,MAAMmJ,EAClB,CACJ,CAER,CACJ,CACA,MAAMiR,WAAsBxX,EAAMyX,UAC9BC,YAAYpZ,GACRqZ,MAAMrZ,GACNsZ,KAAKC,MAAQ,CAET7T,KAAM,KAEd,CAIA8T,gBACQF,KAAKG,WACLH,KAAKG,SAAS1I,QACduI,KAAKG,SAAW,KAExB,CAIAC,SAAShU,GACD4T,KAAKC,MAAM7T,OAASA,GACpB4T,KAAKK,SAAS,CACVjU,QAGZ,CAIAkU,WAAWC,GACP,MAAMN,EAAQD,KAAKC,MACb7T,EAAO4T,KAAKtZ,MAAM0F,KAExB,GAAoB,kBAATA,GACE,OAATA,GACqB,kBAAdA,EAAK8B,KAUZ,OARA8R,KAAKQ,MAAQ,GACbR,KAAKE,sBACDK,GAA0B,OAAfN,EAAM7T,OAEjB4T,KAAKI,SAAS,CACVpV,KAAMD,EAASqB,MAM3B,IAAIqU,EACJ,GAAoB,kBAATrU,GAC0C,QAAhDqU,EAAWxV,EAAamB,GAAM,GAAO,IAGtC,OAFA4T,KAAKE,qBACLF,KAAKI,SAAS,MAIlB,MAAMpV,EAAOyE,EAAYgR,GACzB,GAAa,OAATzV,GAeJ,GAAIgV,KAAKQ,QAAUpU,GAAuB,OAAf6T,EAAM7T,KAAe,CAE5C4T,KAAKE,gBACLF,KAAKQ,MAAQpU,EAEb,MAAM5C,EAAU,CAAC,WACO,KAApBiX,EAAS1U,QACTvC,EAAQmE,KAAK,YAAc8S,EAAS1U,QAEd,KAAtB0U,EAASpV,UACT7B,EAAQmE,KAAK,YAAc8S,EAASpV,UAGxC2U,KAAKI,SAAS,CACVpV,OACAxB,YAEAwW,KAAKtZ,MAAMga,QACXV,KAAKtZ,MAAMga,OAAOtU,EAE1B,OAjCS4T,KAAKG,UAAYH,KAAKG,SAAS3Z,OAAS4F,IAEzC4T,KAAKE,gBACLF,KAAKQ,MAAQ,GACbR,KAAKI,SAAS,MACdJ,KAAKG,SAAW,CACZ3Z,KAAM4F,EACNqL,MAAO2D,GAAU,CAACqF,GAAWT,KAAKM,WAAWjE,KAAK2D,MAAM,KA2BxE,CAIAW,oBACIX,KAAKM,YAAW,EACpB,CAIAM,mBAAmBC,GACXA,EAASzU,OAAS4T,KAAKtZ,MAAM0F,MAC7B4T,KAAKM,YAAW,EAExB,CAIAQ,uBACId,KAAKE,eACT,CAIAa,SACI,MAAMra,EAAQsZ,KAAKtZ,MACb0F,EAAO4T,KAAKC,MAAM7T,KACxB,GAAa,OAATA,EAEA,OAAO1F,EAAMsa,SACPta,EAAMsa,SACN5Y,EAAM6Y,cAAc,OAAQ,CAAC,GAGvC,IAAIC,EAAWxa,EAUf,OATI0F,EAAK5C,UACL0X,EAAW,IACJxa,EACH8B,WAAuC,kBAApB9B,EAAM8B,UACnB9B,EAAM8B,UAAY,IAClB,IAAM4D,EAAK5C,QAAQ2C,KAAK,OAzT/B4U,EAEf3U,EAEA1F,EAEAmJ,EAEAvH,KAEI,MAAM6Y,EAAetR,EAASsP,GAAiBvP,EAEzCyB,EAAiBrB,EAAoBmR,EAAcza,GAEnD0a,EAA+B,kBAAhB1a,EAAM0a,OAAsC,OAAhB1a,EAAM0a,MACjD1a,EAAM0a,MACN,CAAC,EAEDC,EAAiB,IAAKnC,GAAa5W,MAAK8Y,SAE9C,IAAK,IAAI5U,KAAO9F,EAAO,CACnB,MAAMwE,EAAQxE,EAAM8F,GACpB,QAAc,IAAVtB,EAGJ,OAAQsB,GAEJ,IAAK,OACL,IAAK,QACL,IAAK,WACL,IAAK,SACL,IAAK,OACL,IAAK,UACD,MAEJ,IAAK,SACL,IAAK,QACL,IAAK,QACD6E,EAAe7E,IACD,IAAVtB,GAA4B,SAAVA,GAA8B,IAAVA,EAC1C,MAEJ,IAAK,OACoB,kBAAVA,GACPqT,GAAelN,EAAgBnG,GAEnC,MAEJ,IAAK,QACoB,kBAAVA,GACP0T,GAAoBvN,EAAgBnG,GAExC,MAEJ,IAAK,QACDkW,EAAMtb,MAAQoF,EACd,MAEJ,IAAK,SACoB,kBAAVA,EACPmG,EAAe7E,GAAOsS,GAAiB5T,GAEjB,kBAAVA,IACZmG,EAAe7E,GAAOtB,GAE1B,MAEJ,IAAK,aACL,IAAK,eACa,IAAVA,GAA4B,SAAVA,UACXmW,EAAe,eAE1B,MAEJ,aAC8B,IAAtBF,EAAa3U,KACb6U,EAAe7U,GAAOtB,GAGtC,CAEA,MAAM+B,EAAOmE,EAAUhF,EAAMiF,GAE7B,IAAIiQ,EAAe,EACfhP,EAAK5L,EAAM4L,GACG,kBAAPA,IAEPA,EAAKA,EAAGG,QAAQ,KAAM,MAG1B4O,EAAeE,wBAA0B,CACrCC,OAAQrP,EAAWlF,EAAKiB,KAAMoE,EAAK,IAAMA,EAAK,KAAOgP,IAAiB,iBAE1E,IAAK,IAAI9U,KAAOS,EAAK4E,WACjBwP,EAAe7U,GAAOS,EAAK4E,WAAWrF,GAK1C,OAHIS,EAAK4C,aAAkC,IAAxBuR,EAAMtZ,gBACrBsZ,EAAMtZ,cAAgB,YAEnBM,EAAM6Y,cAAc,MAAOI,EAAe,EA0NtCN,CAAO3U,EAAKpB,KAAMkW,EAAUxa,EAAM+a,QAAS/a,EAAMhB,KAC5D,EAOJ,MAAMgc,GAAOtZ,EAAMuZ,YAAW,SAAcjb,EAAO4B,GAC/C,MAAM4Y,EAAW,IACVxa,EACHhB,KAAM4C,EACNmZ,SAAS,GAEb,OAAOrZ,EAAM6Y,cAAcrB,GAAesB,EAC9C,IAMmB9Y,EAAMuZ,YAAW,SAAoBjb,EAAO4B,GAC3D,MAAM4Y,EAAW,IAAKxa,EAAOhB,KAAM4C,EAAKmZ,SAAS,GACjD,OAAOrZ,EAAM6Y,cAAcrB,GAAesB,EAC9C,G,mCCzhEA,kFAEA,MAAM9a,EAAY,CAAC,YAAa,YAAa,UAAW,UAAW,YAgBnE,SAASwb,EAAaZ,EAAU1C,GAC9B,MAAMuD,EAAgBzZ,WAAe0Z,QAAQd,GAAU3K,OAAO0L,SAC9D,OAAOF,EAAcG,QAAO,CAACC,EAAQC,EAAO/O,KAC1C8O,EAAOtU,KAAKuU,GACR/O,EAAQ0O,EAActW,OAAS,GACjC0W,EAAOtU,KAAmBvF,eAAmBkW,EAAW,CACtD9R,IAAK,aAAFvG,OAAekN,MAGf8O,IACN,GACL,CACA,MA+DME,EAAY7b,YAAO,MAAO,CAC9BE,KAAM,WACNzB,KAAM,OACN0B,kBAAmBA,CAACC,EAAOC,IAClB,CAACA,EAAOC,OAJDN,EAvDGZ,IAGf,IAHgB,WACpBE,EAAU,MACVD,GACDD,EACKiB,EAASM,YAAS,CACpBmb,QAAS,OACTC,cAAe,UACdC,YAAkB,CACnB3c,SACC4c,YAAwB,CACzBC,OAAQ5c,EAAW6c,UACnBC,YAAa/c,EAAM+c,YAAYF,UAC7BG,IAAa,CACfN,cAAeM,OAEjB,GAAI/c,EAAWgd,QAAS,CACtB,MAAMC,EAAcC,YAAmBnd,GACjCkO,EAAO7J,OAAOC,KAAKtE,EAAM+c,YAAYF,QAAQR,QAAO,CAACe,EAAKC,MAC5B,kBAAvBpd,EAAWgd,SAA0D,MAAlChd,EAAWgd,QAAQI,IAAuD,kBAAzBpd,EAAW6c,WAA8D,MAApC7c,EAAW6c,UAAUO,MACvJD,EAAIC,IAAc,GAEbD,IACN,CAAC,GACEE,EAAkBV,YAAwB,CAC9CC,OAAQ5c,EAAW6c,UACnB5O,SAEIqP,EAAgBX,YAAwB,CAC5CC,OAAQ5c,EAAWgd,QACnB/O,SAE6B,kBAApBoP,GACTjZ,OAAOC,KAAKgZ,GAAiBvV,SAAQ,CAACsV,EAAY7P,EAAOuP,KAEvD,IADuBO,EAAgBD,GAClB,CACnB,MAAMG,EAAyBhQ,EAAQ,EAAI8P,EAAgBP,EAAYvP,EAAQ,IAAM,SACrF8P,EAAgBD,GAAcG,CAChC,KAGJ,MAAMC,EAAqBA,CAACT,EAAWK,KACrC,MAAO,CACL,gCAAiC,CAC/Bvb,OAAQ,EACR,CAAC,SAADxB,QApDmBwc,EAoDYO,EAAaC,EAAgBD,GAAcpd,EAAW6c,UAnDtF,CACLnM,IAAK,OACL,cAAe,QACf+M,OAAQ,MACR,iBAAkB,UAClBZ,MA8C0Ga,YAAST,EAAaF,KApDvGF,KAsDtB,EAEH9b,EAAS4c,YAAU5c,EAAQ2b,YAAkB,CAC3C3c,SACCud,EAAeE,GACpB,CAEA,OADAzc,EAAS6c,YAAwB7d,EAAM+c,YAAa/b,GAC7CA,CAAM,IAST8c,EAAqBrb,cAAiB,SAAeC,EAASC,GAClE,MAAMob,EAAanb,YAAc,CAC/B7B,MAAO2B,EACP7B,KAAM,aAEFE,EAAQid,YAAaD,IACrB,UACF3c,EAAY,MAAK,UACjB0b,EAAY,SAAQ,QACpBG,EAAU,EAAC,QACXgB,EAAO,SACP5C,GACEta,EACJoC,EAAQC,YAA8BrC,EAAON,GACzCR,EAAa,CACjB6c,YACAG,WAEF,OAAoBhZ,cAAKuY,EAAWlb,YAAS,CAC3C4c,GAAI9c,EACJnB,WAAYA,EACZ0C,IAAKA,GACJQ,EAAO,CACRkY,SAAU4C,EAAUhC,EAAaZ,EAAU4C,GAAW5C,IAE1D,IAmCeyC,K", "file": "static/js/5.7dfff338.chunk.js", "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { alpha, getPath } from '@mui/system';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { elementTypeAcceptingRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "import React from 'react';\n\nconst matchName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst iconDefaults = Object.freeze({\n  left: 0,\n  top: 0,\n  width: 16,\n  height: 16,\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nfunction fullIcon(data) {\n  return { ...iconDefaults, ...data };\n}\n\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIcon(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIcon(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIcon(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIcon = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!((icon.provider === \"\" || icon.provider.match(matchName)) && (allowSimpleName && icon.prefix === \"\" || icon.prefix.match(matchName)) && icon.name.match(matchName));\n};\n\nfunction mergeIconData(icon, alias) {\n  const result = { ...icon };\n  for (const key in iconDefaults) {\n    const prop = key;\n    if (alias[prop] !== void 0) {\n      const value = alias[prop];\n      if (result[prop] === void 0) {\n        result[prop] = value;\n        continue;\n      }\n      switch (prop) {\n        case \"rotate\":\n          result[prop] = (result[prop] + value) % 4;\n          break;\n        case \"hFlip\":\n        case \"vFlip\":\n          result[prop] = value !== result[prop];\n          break;\n        default:\n          result[prop] = value;\n      }\n    }\n  }\n  return result;\n}\n\nfunction getIconData$1(data, name, full = false) {\n  function getIcon(name2, iteration) {\n    if (data.icons[name2] !== void 0) {\n      return Object.assign({}, data.icons[name2]);\n    }\n    if (iteration > 5) {\n      return null;\n    }\n    const aliases = data.aliases;\n    if (aliases && aliases[name2] !== void 0) {\n      const item = aliases[name2];\n      const result2 = getIcon(item.parent, iteration + 1);\n      if (result2) {\n        return mergeIconData(result2, item);\n      }\n      return result2;\n    }\n    const chars = data.chars;\n    if (!iteration && chars && chars[name2] !== void 0) {\n      return getIcon(chars[name2], iteration + 1);\n    }\n    return null;\n  }\n  const result = getIcon(name, 0);\n  if (result) {\n    for (const key in iconDefaults) {\n      if (result[key] === void 0 && data[key] !== void 0) {\n        result[key] = data[key];\n      }\n    }\n  }\n  return result && full ? fullIcon(result) : result;\n}\n\nfunction isVariation(item) {\n  for (const key in iconDefaults) {\n    if (item[key] !== void 0) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction parseIconSet(data, callback, options) {\n  options = options || {};\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const icons = data.icons;\n  Object.keys(icons).forEach((name) => {\n    const iconData = getIconData$1(data, name, true);\n    if (iconData) {\n      callback(name, iconData);\n      names.push(name);\n    }\n  });\n  const parseAliases = options.aliases || \"all\";\n  if (parseAliases !== \"none\" && typeof data.aliases === \"object\") {\n    const aliases = data.aliases;\n    Object.keys(aliases).forEach((name) => {\n      if (parseAliases === \"variations\" && isVariation(aliases[name])) {\n        return;\n      }\n      const iconData = getIconData$1(data, name, true);\n      if (iconData) {\n        callback(name, iconData);\n        names.push(name);\n      }\n    });\n  }\n  return names;\n}\n\nconst optionalProperties = {\n  provider: \"string\",\n  aliases: \"object\",\n  not_found: \"object\"\n};\nfor (const prop in iconDefaults) {\n  optionalProperties[prop] = typeof iconDefaults[prop];\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  for (const prop in optionalProperties) {\n    if (obj[prop] !== void 0 && typeof obj[prop] !== optionalProperties[prop]) {\n      return null;\n    }\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (!name.match(matchName) || typeof icon.body !== \"string\") {\n      return null;\n    }\n    for (const prop in iconDefaults) {\n      if (icon[prop] !== void 0 && typeof icon[prop] !== typeof iconDefaults[prop]) {\n        return null;\n      }\n    }\n  }\n  const aliases = data.aliases;\n  if (aliases) {\n    for (const name in aliases) {\n      const icon = aliases[name];\n      const parent = icon.parent;\n      if (!name.match(matchName) || typeof parent !== \"string\" || !icons[parent] && !aliases[parent]) {\n        return null;\n      }\n      for (const prop in iconDefaults) {\n        if (icon[prop] !== void 0 && typeof icon[prop] !== typeof iconDefaults[prop]) {\n          return null;\n        }\n      }\n    }\n  }\n  return data;\n}\n\nconst storageVersion = 1;\nlet storage$1 = /* @__PURE__ */ Object.create(null);\ntry {\n  const w = window || self;\n  if (w && w._iconifyStorage.version === storageVersion) {\n    storage$1 = w._iconifyStorage.storage;\n  }\n} catch (err) {\n}\nfunction shareStorage() {\n  try {\n    const w = window || self;\n    if (w && !w._iconifyStorage) {\n      w._iconifyStorage = {\n        version: storageVersion,\n        storage: storage$1\n      };\n    }\n  } catch (err) {\n  }\n}\nfunction newStorage(provider, prefix) {\n  return {\n    provider,\n    prefix,\n    icons: /* @__PURE__ */ Object.create(null),\n    missing: /* @__PURE__ */ Object.create(null)\n  };\n}\nfunction getStorage(provider, prefix) {\n  if (storage$1[provider] === void 0) {\n    storage$1[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerStorage = storage$1[provider];\n  if (providerStorage[prefix] === void 0) {\n    providerStorage[prefix] = newStorage(provider, prefix);\n  }\n  return providerStorage[prefix];\n}\nfunction addIconSet(storage2, data) {\n  if (!quicklyValidateIconSet(data)) {\n    return [];\n  }\n  const t = Date.now();\n  return parseIconSet(data, (name, icon) => {\n    if (icon) {\n      storage2.icons[name] = icon;\n    } else {\n      storage2.missing[name] = t;\n    }\n  });\n}\nfunction addIconToStorage(storage2, name, icon) {\n  try {\n    if (typeof icon.body === \"string\") {\n      storage2.icons[name] = Object.freeze(fullIcon(icon));\n      return true;\n    }\n  } catch (err) {\n  }\n  return false;\n}\nfunction getIconFromStorage(storage2, name) {\n  const value = storage2.icons[name];\n  return value === void 0 ? null : value;\n}\nfunction listIcons(provider, prefix) {\n  let allIcons = [];\n  let providers;\n  if (typeof provider === \"string\") {\n    providers = [provider];\n  } else {\n    providers = Object.keys(storage$1);\n  }\n  providers.forEach((provider2) => {\n    let prefixes;\n    if (typeof provider2 === \"string\" && typeof prefix === \"string\") {\n      prefixes = [prefix];\n    } else {\n      prefixes = storage$1[provider2] === void 0 ? [] : Object.keys(storage$1[provider2]);\n    }\n    prefixes.forEach((prefix2) => {\n      const storage2 = getStorage(provider2, prefix2);\n      const icons = Object.keys(storage2.icons).map((name) => (provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name);\n      allIcons = allIcons.concat(icons);\n    });\n  });\n  return allIcons;\n}\n\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n  if (typeof allow === \"boolean\") {\n    simpleNames = allow;\n  }\n  return simpleNames;\n}\nfunction getIconData(name) {\n  const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n  return icon ? getIconFromStorage(getStorage(icon.provider, icon.prefix), icon.name) : null;\n}\nfunction addIcon(name, data) {\n  const icon = stringToIcon(name, true, simpleNames);\n  if (!icon) {\n    return false;\n  }\n  const storage = getStorage(icon.provider, icon.prefix);\n  return addIconToStorage(storage, icon.name, data);\n}\nfunction addCollection(data, provider) {\n  if (typeof data !== \"object\") {\n    return false;\n  }\n  if (typeof provider !== \"string\") {\n    provider = typeof data.provider === \"string\" ? data.provider : \"\";\n  }\n  if (simpleNames && provider === \"\" && (typeof data.prefix !== \"string\" || data.prefix === \"\")) {\n    let added = false;\n    if (quicklyValidateIconSet(data)) {\n      data.prefix = \"\";\n      parseIconSet(data, (name, icon) => {\n        if (icon && addIcon(name, icon)) {\n          added = true;\n        }\n      });\n    }\n    return added;\n  }\n  if (typeof data.prefix !== \"string\" || !validateIcon({\n    provider,\n    prefix: data.prefix,\n    name: \"a\"\n  })) {\n    return false;\n  }\n  const storage = getStorage(provider, data.prefix);\n  return !!addIconSet(storage, data);\n}\nfunction iconExists(name) {\n  return getIconData(name) !== null;\n}\nfunction getIcon(name) {\n  const result = getIconData(name);\n  return result ? { ...result } : null;\n}\n\nconst defaults = Object.freeze({\n  inline: false,\n  width: null,\n  height: null,\n  hAlign: \"center\",\n  vAlign: \"middle\",\n  slice: false,\n  hFlip: false,\n  vFlip: false,\n  rotate: 0\n});\nfunction mergeCustomisations(defaults2, item) {\n  const result = {};\n  for (const key in defaults2) {\n    const attr = key;\n    result[attr] = defaults2[attr];\n    if (item[attr] === void 0) {\n      continue;\n    }\n    const value = item[attr];\n    switch (attr) {\n      case \"inline\":\n      case \"slice\":\n        if (typeof value === \"boolean\") {\n          result[attr] = value;\n        }\n        break;\n      case \"hFlip\":\n      case \"vFlip\":\n        if (value === true) {\n          result[attr] = !result[attr];\n        }\n        break;\n      case \"hAlign\":\n      case \"vAlign\":\n        if (typeof value === \"string\" && value !== \"\") {\n          result[attr] = value;\n        }\n        break;\n      case \"width\":\n      case \"height\":\n        if (typeof value === \"string\" && value !== \"\" || typeof value === \"number\" && value || value === null) {\n          result[attr] = value;\n        }\n        break;\n      case \"rotate\":\n        if (typeof value === \"number\") {\n          result[attr] += value;\n        }\n        break;\n    }\n  }\n  return result;\n}\n\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision === void 0 ? 100 : precision;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nfunction preserveAspectRatio(props) {\n  let result = \"\";\n  switch (props.hAlign) {\n    case \"left\":\n      result += \"xMin\";\n      break;\n    case \"right\":\n      result += \"xMax\";\n      break;\n    default:\n      result += \"xMid\";\n  }\n  switch (props.vAlign) {\n    case \"top\":\n      result += \"YMin\";\n      break;\n    case \"bottom\":\n      result += \"YMax\";\n      break;\n    default:\n      result += \"YMid\";\n  }\n  result += props.slice ? \" slice\" : \" meet\";\n  return result;\n}\nfunction iconToSVG(icon, customisations) {\n  const box = {\n    left: icon.left,\n    top: icon.top,\n    width: icon.width,\n    height: icon.height\n  };\n  let body = icon.body;\n  [icon, customisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\");\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\");\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n        break;\n      case 2:\n        transformations.unshift(\"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\");\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== 0 || box.top !== 0) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = '<g transform=\"' + transformations.join(\" \") + '\">' + body + \"</g>\";\n    }\n  });\n  let width, height;\n  if (customisations.width === null && customisations.height === null) {\n    height = \"1em\";\n    width = calculateSize(height, box.width / box.height);\n  } else if (customisations.width !== null && customisations.height !== null) {\n    width = customisations.width;\n    height = customisations.height;\n  } else if (customisations.height !== null) {\n    height = customisations.height;\n    width = calculateSize(height, box.width / box.height);\n  } else {\n    width = customisations.width;\n    height = calculateSize(width, box.height / box.width);\n  }\n  if (width === \"auto\") {\n    width = box.width;\n  }\n  if (height === \"auto\") {\n    height = box.height;\n  }\n  width = typeof width === \"string\" ? width : width.toString() + \"\";\n  height = typeof height === \"string\" ? height : height.toString() + \"\";\n  const result = {\n    attributes: {\n      width,\n      height,\n      preserveAspectRatio: preserveAspectRatio(customisations),\n      viewBox: box.left.toString() + \" \" + box.top.toString() + \" \" + box.width.toString() + \" \" + box.height.toString()\n    },\n    body\n  };\n  if (customisations.inline) {\n    result.inline = true;\n  }\n  return result;\n}\n\nfunction buildIcon(icon, customisations) {\n  return iconToSVG(fullIcon(icon), customisations ? mergeCustomisations(defaults, customisations) : defaults);\n}\n\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"), \"$1\" + newID + \"$3\");\n  });\n  return body;\n}\n\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n  storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n  return storage[provider] || storage[\"\"];\n}\n\nfunction createAPIConfig(source) {\n  let resources;\n  if (typeof source.resources === \"string\") {\n    resources = [source.resources];\n  } else {\n    resources = source.resources;\n    if (!(resources instanceof Array) || !resources.length) {\n      return null;\n    }\n  }\n  const result = {\n    resources,\n    path: source.path === void 0 ? \"/\" : source.path,\n    maxURL: source.maxURL ? source.maxURL : 500,\n    rotate: source.rotate ? source.rotate : 750,\n    timeout: source.timeout ? source.timeout : 5e3,\n    random: source.random === true,\n    index: source.index ? source.index : 0,\n    dataAfterTimeout: source.dataAfterTimeout !== false\n  };\n  return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n  \"https://api.simplesvg.com\",\n  \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile (fallBackAPISources.length > 0) {\n  if (fallBackAPISources.length === 1) {\n    fallBackAPI.push(fallBackAPISources.shift());\n  } else {\n    if (Math.random() > 0.5) {\n      fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n      fallBackAPI.push(fallBackAPISources.pop());\n    }\n  }\n}\nconfigStorage[\"\"] = createAPIConfig({\n  resources: [\"https://api.iconify.design\"].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n  const config = createAPIConfig(customConfig);\n  if (config === null) {\n    return false;\n  }\n  configStorage[provider] = config;\n  return true;\n}\nfunction getAPIConfig(provider) {\n  return configStorage[provider];\n}\nfunction listAPIProviders() {\n  return Object.keys(configStorage);\n}\n\nconst mergeParams = (base, params) => {\n  let result = base, hasParams = result.indexOf(\"?\") !== -1;\n  function paramToString(value) {\n    switch (typeof value) {\n      case \"boolean\":\n        return value ? \"true\" : \"false\";\n      case \"number\":\n        return encodeURIComponent(value);\n      case \"string\":\n        return encodeURIComponent(value);\n      default:\n        throw new Error(\"Invalid parameter\");\n    }\n  }\n  Object.keys(params).forEach((key) => {\n    let value;\n    try {\n      value = paramToString(params[key]);\n    } catch (err) {\n      return;\n    }\n    result += (hasParams ? \"&\" : \"?\") + encodeURIComponent(key) + \"=\" + value;\n    hasParams = true;\n  });\n  return result;\n};\n\nconst maxLengthCache = {};\nconst pathCache = {};\nconst detectFetch = () => {\n  let callback;\n  try {\n    callback = fetch;\n    if (typeof callback === \"function\") {\n      return callback;\n    }\n  } catch (err) {\n  }\n  return null;\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n  fetchModule = fetch2;\n}\nfunction getFetch() {\n  return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n  const config = getAPIConfig(provider);\n  if (!config) {\n    return 0;\n  }\n  let result;\n  if (!config.maxURL) {\n    result = 0;\n  } else {\n    let maxHostLength = 0;\n    config.resources.forEach((item) => {\n      const host = item;\n      maxHostLength = Math.max(maxHostLength, host.length);\n    });\n    const url = mergeParams(prefix + \".json\", {\n      icons: \"\"\n    });\n    result = config.maxURL - maxHostLength - config.path.length - url.length;\n  }\n  const cacheKey = provider + \":\" + prefix;\n  pathCache[provider] = config.path;\n  maxLengthCache[cacheKey] = result;\n  return result;\n}\nfunction shouldAbort(status) {\n  return status === 404;\n}\nconst prepare = (provider, prefix, icons) => {\n  const results = [];\n  let maxLength = maxLengthCache[prefix];\n  if (maxLength === void 0) {\n    maxLength = calculateMaxLength(provider, prefix);\n  }\n  const type = \"icons\";\n  let item = {\n    type,\n    provider,\n    prefix,\n    icons: []\n  };\n  let length = 0;\n  icons.forEach((name, index) => {\n    length += name.length + 1;\n    if (length >= maxLength && index > 0) {\n      results.push(item);\n      item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n      };\n      length = name.length;\n    }\n    item.icons.push(name);\n  });\n  results.push(item);\n  return results;\n};\nfunction getPath(provider) {\n  if (typeof provider === \"string\") {\n    if (pathCache[provider] === void 0) {\n      const config = getAPIConfig(provider);\n      if (!config) {\n        return \"/\";\n      }\n      pathCache[provider] = config.path;\n    }\n    return pathCache[provider];\n  }\n  return \"/\";\n}\nconst send = (host, params, callback) => {\n  if (!fetchModule) {\n    callback(\"abort\", 424);\n    return;\n  }\n  let path = getPath(params.provider);\n  switch (params.type) {\n    case \"icons\": {\n      const prefix = params.prefix;\n      const icons = params.icons;\n      const iconsList = icons.join(\",\");\n      path += mergeParams(prefix + \".json\", {\n        icons: iconsList\n      });\n      break;\n    }\n    case \"custom\": {\n      const uri = params.uri;\n      path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n      break;\n    }\n    default:\n      callback(\"abort\", 400);\n      return;\n  }\n  let defaultError = 503;\n  fetchModule(host + path).then((response) => {\n    const status = response.status;\n    if (status !== 200) {\n      setTimeout(() => {\n        callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n      });\n      return;\n    }\n    defaultError = 501;\n    return response.json();\n  }).then((data) => {\n    if (typeof data !== \"object\" || data === null) {\n      setTimeout(() => {\n        callback(\"next\", defaultError);\n      });\n      return;\n    }\n    setTimeout(() => {\n      callback(\"success\", data);\n    });\n  }).catch(() => {\n    callback(\"next\", defaultError);\n  });\n};\nconst fetchAPIModule = {\n  prepare,\n  send\n};\n\nfunction sortIcons(icons) {\n  const result = {\n    loaded: [],\n    missing: [],\n    pending: []\n  };\n  const storage = /* @__PURE__ */ Object.create(null);\n  icons.sort((a, b) => {\n    if (a.provider !== b.provider) {\n      return a.provider.localeCompare(b.provider);\n    }\n    if (a.prefix !== b.prefix) {\n      return a.prefix.localeCompare(b.prefix);\n    }\n    return a.name.localeCompare(b.name);\n  });\n  let lastIcon = {\n    provider: \"\",\n    prefix: \"\",\n    name: \"\"\n  };\n  icons.forEach((icon) => {\n    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n      return;\n    }\n    lastIcon = icon;\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    if (storage[provider] === void 0) {\n      storage[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerStorage = storage[provider];\n    if (providerStorage[prefix] === void 0) {\n      providerStorage[prefix] = getStorage(provider, prefix);\n    }\n    const localStorage = providerStorage[prefix];\n    let list;\n    if (localStorage.icons[name] !== void 0) {\n      list = result.loaded;\n    } else if (prefix === \"\" || localStorage.missing[name] !== void 0) {\n      list = result.missing;\n    } else {\n      list = result.pending;\n    }\n    const item = {\n      provider,\n      prefix,\n      name\n    };\n    list.push(item);\n  });\n  return result;\n}\n\nconst callbacks = /* @__PURE__ */ Object.create(null);\nconst pendingUpdates = /* @__PURE__ */ Object.create(null);\nfunction removeCallback(sources, id) {\n  sources.forEach((source) => {\n    const provider = source.provider;\n    if (callbacks[provider] === void 0) {\n      return;\n    }\n    const providerCallbacks = callbacks[provider];\n    const prefix = source.prefix;\n    const items = providerCallbacks[prefix];\n    if (items) {\n      providerCallbacks[prefix] = items.filter((row) => row.id !== id);\n    }\n  });\n}\nfunction updateCallbacks(provider, prefix) {\n  if (pendingUpdates[provider] === void 0) {\n    pendingUpdates[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerPendingUpdates = pendingUpdates[provider];\n  if (!providerPendingUpdates[prefix]) {\n    providerPendingUpdates[prefix] = true;\n    setTimeout(() => {\n      providerPendingUpdates[prefix] = false;\n      if (callbacks[provider] === void 0 || callbacks[provider][prefix] === void 0) {\n        return;\n      }\n      const items = callbacks[provider][prefix].slice(0);\n      if (!items.length) {\n        return;\n      }\n      const storage = getStorage(provider, prefix);\n      let hasPending = false;\n      items.forEach((item) => {\n        const icons = item.icons;\n        const oldLength = icons.pending.length;\n        icons.pending = icons.pending.filter((icon) => {\n          if (icon.prefix !== prefix) {\n            return true;\n          }\n          const name = icon.name;\n          if (storage.icons[name] !== void 0) {\n            icons.loaded.push({\n              provider,\n              prefix,\n              name\n            });\n          } else if (storage.missing[name] !== void 0) {\n            icons.missing.push({\n              provider,\n              prefix,\n              name\n            });\n          } else {\n            hasPending = true;\n            return true;\n          }\n          return false;\n        });\n        if (icons.pending.length !== oldLength) {\n          if (!hasPending) {\n            removeCallback([\n              {\n                provider,\n                prefix\n              }\n            ], item.id);\n          }\n          item.callback(icons.loaded.slice(0), icons.missing.slice(0), icons.pending.slice(0), item.abort);\n        }\n      });\n    });\n  }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n  const id = idCounter++;\n  const abort = removeCallback.bind(null, pendingSources, id);\n  if (!icons.pending.length) {\n    return abort;\n  }\n  const item = {\n    id,\n    icons,\n    callback,\n    abort\n  };\n  pendingSources.forEach((source) => {\n    const provider = source.provider;\n    const prefix = source.prefix;\n    if (callbacks[provider] === void 0) {\n      callbacks[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerCallbacks = callbacks[provider];\n    if (providerCallbacks[prefix] === void 0) {\n      providerCallbacks[prefix] = [];\n    }\n    providerCallbacks[prefix].push(item);\n  });\n  return abort;\n}\n\nfunction listToIcons(list, validate = true, simpleNames = false) {\n  const result = [];\n  list.forEach((item) => {\n    const icon = typeof item === \"string\" ? stringToIcon(item, false, simpleNames) : item;\n    if (!validate || validateIcon(icon, simpleNames)) {\n      result.push({\n        provider: icon.provider,\n        prefix: icon.prefix,\n        name: icon.name\n      });\n    }\n  });\n  return result;\n}\n\n// src/config.ts\nvar defaultConfig = {\n  resources: [],\n  index: 0,\n  timeout: 2e3,\n  rotate: 750,\n  random: false,\n  dataAfterTimeout: false\n};\n\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n  const resourcesCount = config.resources.length;\n  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n  let resources;\n  if (config.random) {\n    let list = config.resources.slice(0);\n    resources = [];\n    while (list.length > 1) {\n      const nextIndex = Math.floor(Math.random() * list.length);\n      resources.push(list[nextIndex]);\n      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n    }\n    resources = resources.concat(list);\n  } else {\n    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n  }\n  const startTime = Date.now();\n  let status = \"pending\";\n  let queriesSent = 0;\n  let lastError;\n  let timer = null;\n  let queue = [];\n  let doneCallbacks = [];\n  if (typeof done === \"function\") {\n    doneCallbacks.push(done);\n  }\n  function resetTimer() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function abort() {\n    if (status === \"pending\") {\n      status = \"aborted\";\n    }\n    resetTimer();\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function subscribe(callback, overwrite) {\n    if (overwrite) {\n      doneCallbacks = [];\n    }\n    if (typeof callback === \"function\") {\n      doneCallbacks.push(callback);\n    }\n  }\n  function getQueryStatus() {\n    return {\n      startTime,\n      payload,\n      status,\n      queriesSent,\n      queriesPending: queue.length,\n      subscribe,\n      abort\n    };\n  }\n  function failQuery() {\n    status = \"failed\";\n    doneCallbacks.forEach((callback) => {\n      callback(void 0, lastError);\n    });\n  }\n  function clearQueue() {\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function moduleResponse(item, response, data) {\n    const isError = response !== \"success\";\n    queue = queue.filter((queued) => queued !== item);\n    switch (status) {\n      case \"pending\":\n        break;\n      case \"failed\":\n        if (isError || !config.dataAfterTimeout) {\n          return;\n        }\n        break;\n      default:\n        return;\n    }\n    if (response === \"abort\") {\n      lastError = data;\n      failQuery();\n      return;\n    }\n    if (isError) {\n      lastError = data;\n      if (!queue.length) {\n        if (!resources.length) {\n          failQuery();\n        } else {\n          execNext();\n        }\n      }\n      return;\n    }\n    resetTimer();\n    clearQueue();\n    if (!config.random) {\n      const index = config.resources.indexOf(item.resource);\n      if (index !== -1 && index !== config.index) {\n        config.index = index;\n      }\n    }\n    status = \"completed\";\n    doneCallbacks.forEach((callback) => {\n      callback(data);\n    });\n  }\n  function execNext() {\n    if (status !== \"pending\") {\n      return;\n    }\n    resetTimer();\n    const resource = resources.shift();\n    if (resource === void 0) {\n      if (queue.length) {\n        timer = setTimeout(() => {\n          resetTimer();\n          if (status === \"pending\") {\n            clearQueue();\n            failQuery();\n          }\n        }, config.timeout);\n        return;\n      }\n      failQuery();\n      return;\n    }\n    const item = {\n      status: \"pending\",\n      resource,\n      callback: (status2, data) => {\n        moduleResponse(item, status2, data);\n      }\n    };\n    queue.push(item);\n    queriesSent++;\n    timer = setTimeout(execNext, config.rotate);\n    query(resource, payload, item.callback);\n  }\n  setTimeout(execNext);\n  return getQueryStatus;\n}\n\n// src/index.ts\nfunction setConfig(config) {\n  if (typeof config !== \"object\" || typeof config.resources !== \"object\" || !(config.resources instanceof Array) || !config.resources.length) {\n    throw new Error(\"Invalid Reduncancy configuration\");\n  }\n  const newConfig = /* @__PURE__ */ Object.create(null);\n  let key;\n  for (key in defaultConfig) {\n    if (config[key] !== void 0) {\n      newConfig[key] = config[key];\n    } else {\n      newConfig[key] = defaultConfig[key];\n    }\n  }\n  return newConfig;\n}\nfunction initRedundancy(cfg) {\n  const config = setConfig(cfg);\n  let queries = [];\n  function cleanup() {\n    queries = queries.filter((item) => item().status === \"pending\");\n  }\n  function query(payload, queryCallback, doneCallback) {\n    const query2 = sendQuery(config, payload, queryCallback, (data, error) => {\n      cleanup();\n      if (doneCallback) {\n        doneCallback(data, error);\n      }\n    });\n    queries.push(query2);\n    return query2;\n  }\n  function find(callback) {\n    const result = queries.find((value) => {\n      return callback(value);\n    });\n    return result !== void 0 ? result : null;\n  }\n  const instance = {\n    query,\n    find,\n    setIndex: (index) => {\n      config.index = index;\n    },\n    getIndex: () => config.index,\n    cleanup\n  };\n  return instance;\n}\n\nfunction emptyCallback$1() {\n}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n  if (redundancyCache[provider] === void 0) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n      return;\n    }\n    const redundancy = initRedundancy(config);\n    const cachedReundancy = {\n      config,\n      redundancy\n    };\n    redundancyCache[provider] = cachedReundancy;\n  }\n  return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n  let redundancy;\n  let send;\n  if (typeof target === \"string\") {\n    const api = getAPIModule(target);\n    if (!api) {\n      callback(void 0, 424);\n      return emptyCallback$1;\n    }\n    send = api.send;\n    const cached = getRedundancyCache(target);\n    if (cached) {\n      redundancy = cached.redundancy;\n    }\n  } else {\n    const config = createAPIConfig(target);\n    if (config) {\n      redundancy = initRedundancy(config);\n      const moduleKey = target.resources ? target.resources[0] : \"\";\n      const api = getAPIModule(moduleKey);\n      if (api) {\n        send = api.send;\n      }\n    }\n  }\n  if (!redundancy || !send) {\n    callback(void 0, 424);\n    return emptyCallback$1;\n  }\n  return redundancy.query(query, send, callback)().abort;\n}\n\nconst cache = {};\n\nfunction emptyCallback() {\n}\nconst pendingIcons = /* @__PURE__ */ Object.create(null);\nconst iconsToLoad = /* @__PURE__ */ Object.create(null);\nconst loaderFlags = /* @__PURE__ */ Object.create(null);\nconst queueFlags = /* @__PURE__ */ Object.create(null);\nfunction loadedNewIcons(provider, prefix) {\n  if (loaderFlags[provider] === void 0) {\n    loaderFlags[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerLoaderFlags = loaderFlags[provider];\n  if (!providerLoaderFlags[prefix]) {\n    providerLoaderFlags[prefix] = true;\n    setTimeout(() => {\n      providerLoaderFlags[prefix] = false;\n      updateCallbacks(provider, prefix);\n    });\n  }\n}\nconst errorsCache = /* @__PURE__ */ Object.create(null);\nfunction loadNewIcons(provider, prefix, icons) {\n  function err() {\n    const key = (provider === \"\" ? \"\" : \"@\" + provider + \":\") + prefix;\n    const time = Math.floor(Date.now() / 6e4);\n    if (errorsCache[key] < time) {\n      errorsCache[key] = time;\n      console.error('Unable to retrieve icons for \"' + key + '\" because API is not configured properly.');\n    }\n  }\n  if (iconsToLoad[provider] === void 0) {\n    iconsToLoad[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerIconsToLoad = iconsToLoad[provider];\n  if (queueFlags[provider] === void 0) {\n    queueFlags[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerQueueFlags = queueFlags[provider];\n  if (pendingIcons[provider] === void 0) {\n    pendingIcons[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerPendingIcons = pendingIcons[provider];\n  if (providerIconsToLoad[prefix] === void 0) {\n    providerIconsToLoad[prefix] = icons;\n  } else {\n    providerIconsToLoad[prefix] = providerIconsToLoad[prefix].concat(icons).sort();\n  }\n  if (!providerQueueFlags[prefix]) {\n    providerQueueFlags[prefix] = true;\n    setTimeout(() => {\n      providerQueueFlags[prefix] = false;\n      const icons2 = providerIconsToLoad[prefix];\n      delete providerIconsToLoad[prefix];\n      const api = getAPIModule(provider);\n      if (!api) {\n        err();\n        return;\n      }\n      const params = api.prepare(provider, prefix, icons2);\n      params.forEach((item) => {\n        sendAPIQuery(provider, item, (data, error) => {\n          const storage = getStorage(provider, prefix);\n          if (typeof data !== \"object\") {\n            if (error !== 404) {\n              return;\n            }\n            const t = Date.now();\n            item.icons.forEach((name) => {\n              storage.missing[name] = t;\n            });\n          } else {\n            try {\n              const parsed = addIconSet(storage, data);\n              if (!parsed.length) {\n                return;\n              }\n              const pending = providerPendingIcons[prefix];\n              parsed.forEach((name) => {\n                delete pending[name];\n              });\n              if (cache.store) {\n                cache.store(provider, data);\n              }\n            } catch (err2) {\n              console.error(err2);\n            }\n          }\n          loadedNewIcons(provider, prefix);\n        });\n      });\n    });\n  }\n}\nconst loadIcons = (icons, callback) => {\n  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n  const sortedIcons = sortIcons(cleanedIcons);\n  if (!sortedIcons.pending.length) {\n    let callCallback = true;\n    if (callback) {\n      setTimeout(() => {\n        if (callCallback) {\n          callback(sortedIcons.loaded, sortedIcons.missing, sortedIcons.pending, emptyCallback);\n        }\n      });\n    }\n    return () => {\n      callCallback = false;\n    };\n  }\n  const newIcons = /* @__PURE__ */ Object.create(null);\n  const sources = [];\n  let lastProvider, lastPrefix;\n  sortedIcons.pending.forEach((icon) => {\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    if (prefix === lastPrefix && provider === lastProvider) {\n      return;\n    }\n    lastProvider = provider;\n    lastPrefix = prefix;\n    sources.push({\n      provider,\n      prefix\n    });\n    if (pendingIcons[provider] === void 0) {\n      pendingIcons[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerPendingIcons = pendingIcons[provider];\n    if (providerPendingIcons[prefix] === void 0) {\n      providerPendingIcons[prefix] = /* @__PURE__ */ Object.create(null);\n    }\n    if (newIcons[provider] === void 0) {\n      newIcons[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerNewIcons = newIcons[provider];\n    if (providerNewIcons[prefix] === void 0) {\n      providerNewIcons[prefix] = [];\n    }\n  });\n  const time = Date.now();\n  sortedIcons.pending.forEach((icon) => {\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    const pendingQueue = pendingIcons[provider][prefix];\n    if (pendingQueue[name] === void 0) {\n      pendingQueue[name] = time;\n      newIcons[provider][prefix].push(name);\n    }\n  });\n  sources.forEach((source) => {\n    const provider = source.provider;\n    const prefix = source.prefix;\n    if (newIcons[provider][prefix].length) {\n      loadNewIcons(provider, prefix, newIcons[provider][prefix]);\n    }\n  });\n  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon) => {\n  return new Promise((fulfill, reject) => {\n    const iconObj = typeof icon === \"string\" ? stringToIcon(icon) : icon;\n    loadIcons([iconObj || icon], (loaded) => {\n      if (loaded.length && iconObj) {\n        const storage = getStorage(iconObj.provider, iconObj.prefix);\n        const data = getIconFromStorage(storage, iconObj.name);\n        if (data) {\n          fulfill(data);\n          return;\n        }\n      }\n      reject(icon);\n    });\n  });\n};\n\nconst cacheVersion = \"iconify2\";\nconst cachePrefix = \"iconify\";\nconst countKey = cachePrefix + \"-count\";\nconst versionKey = cachePrefix + \"-version\";\nconst hour = 36e5;\nconst cacheExpiration = 168;\nconst config = {\n  local: true,\n  session: true\n};\nlet loaded = false;\nconst count = {\n  local: 0,\n  session: 0\n};\nconst emptyList = {\n  local: [],\n  session: []\n};\nlet _window = typeof window === \"undefined\" ? {} : window;\nfunction getGlobal(key) {\n  const attr = key + \"Storage\";\n  try {\n    if (_window && _window[attr] && typeof _window[attr].length === \"number\") {\n      return _window[attr];\n    }\n  } catch (err) {\n  }\n  config[key] = false;\n  return null;\n}\nfunction setCount(storage, key, value) {\n  try {\n    storage.setItem(countKey, value.toString());\n    count[key] = value;\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nfunction getCount(storage) {\n  const count2 = storage.getItem(countKey);\n  if (count2) {\n    const total = parseInt(count2);\n    return total ? total : 0;\n  }\n  return 0;\n}\nfunction initCache(storage, key) {\n  try {\n    storage.setItem(versionKey, cacheVersion);\n  } catch (err) {\n  }\n  setCount(storage, key, 0);\n}\nfunction destroyCache(storage) {\n  try {\n    const total = getCount(storage);\n    for (let i = 0; i < total; i++) {\n      storage.removeItem(cachePrefix + i.toString());\n    }\n  } catch (err) {\n  }\n}\nconst loadCache = () => {\n  if (loaded) {\n    return;\n  }\n  loaded = true;\n  const minTime = Math.floor(Date.now() / hour) - cacheExpiration;\n  function load(key) {\n    const func = getGlobal(key);\n    if (!func) {\n      return;\n    }\n    const getItem = (index) => {\n      const name = cachePrefix + index.toString();\n      const item = func.getItem(name);\n      if (typeof item !== \"string\") {\n        return false;\n      }\n      let valid = true;\n      try {\n        const data = JSON.parse(item);\n        if (typeof data !== \"object\" || typeof data.cached !== \"number\" || data.cached < minTime || typeof data.provider !== \"string\" || typeof data.data !== \"object\" || typeof data.data.prefix !== \"string\") {\n          valid = false;\n        } else {\n          const provider = data.provider;\n          const prefix = data.data.prefix;\n          const storage = getStorage(provider, prefix);\n          valid = addIconSet(storage, data.data).length > 0;\n        }\n      } catch (err) {\n        valid = false;\n      }\n      if (!valid) {\n        func.removeItem(name);\n      }\n      return valid;\n    };\n    try {\n      const version = func.getItem(versionKey);\n      if (version !== cacheVersion) {\n        if (version) {\n          destroyCache(func);\n        }\n        initCache(func, key);\n        return;\n      }\n      let total = getCount(func);\n      for (let i = total - 1; i >= 0; i--) {\n        if (!getItem(i)) {\n          if (i === total - 1) {\n            total--;\n          } else {\n            emptyList[key].push(i);\n          }\n        }\n      }\n      setCount(func, key, total);\n    } catch (err) {\n    }\n  }\n  for (const key in config) {\n    load(key);\n  }\n};\nconst storeCache = (provider, data) => {\n  if (!loaded) {\n    loadCache();\n  }\n  function store(key) {\n    if (!config[key]) {\n      return false;\n    }\n    const func = getGlobal(key);\n    if (!func) {\n      return false;\n    }\n    let index = emptyList[key].shift();\n    if (index === void 0) {\n      index = count[key];\n      if (!setCount(func, key, index + 1)) {\n        return false;\n      }\n    }\n    try {\n      const item = {\n        cached: Math.floor(Date.now() / hour),\n        provider,\n        data\n      };\n      func.setItem(cachePrefix + index.toString(), JSON.stringify(item));\n    } catch (err) {\n      return false;\n    }\n    return true;\n  }\n  if (!Object.keys(data.icons).length) {\n    return;\n  }\n  if (data.not_found) {\n    data = Object.assign({}, data);\n    delete data.not_found;\n  }\n  if (!store(\"local\")) {\n    store(\"session\");\n  }\n};\n\nfunction toggleBrowserCache(storage, value) {\n  switch (storage) {\n    case \"local\":\n    case \"session\":\n      config[storage] = value;\n      break;\n    case \"all\":\n      for (const key in config) {\n        config[key] = value;\n      }\n      break;\n  }\n}\n\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\nfunction alignmentFromString(custom, align) {\n  align.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"left\":\n      case \"center\":\n      case \"right\":\n        custom.hAlign = value;\n        break;\n      case \"top\":\n      case \"middle\":\n      case \"bottom\":\n        custom.vAlign = value;\n        break;\n      case \"slice\":\n      case \"crop\":\n        custom.slice = true;\n        break;\n      case \"meet\":\n        custom.slice = false;\n    }\n  });\n}\n\nfunction rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\n/**\n * Default SVG attributes\n */\nconst svgDefaults = {\n    'xmlns': 'http://www.w3.org/2000/svg',\n    'xmlnsXlink': 'http://www.w3.org/1999/xlink',\n    'aria-hidden': true,\n    'role': 'img',\n    'style': {}, // Include style if it isn't set to add verticalAlign later\n};\n/**\n * Default values for customisations for inline icon\n */\nconst inlineDefaults = { ...defaults, inline: true };\n/**\n * Render icon\n */\nconst render = (\n// Icon must be validated before calling this function\nicon, \n// Partial properties\nprops, \n// True if icon should have vertical-align added\ninline, \n// Optional reference for SVG, extracted by React.forwardRef()\nref) => {\n    // Get default properties\n    const defaultProps = inline ? inlineDefaults : defaults;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Create style\n    const style = typeof props.style === 'object' && props.style !== null\n        ? props.style\n        : {};\n    // Create SVG component properties\n    const componentProps = { ...svgDefaults, ref, style };\n    // Get element properties\n    for (let key in props) {\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch (key) {\n            // Properties to ignore\n            case 'icon':\n            case 'style':\n            case 'children':\n            case 'onLoad':\n            case '_ref':\n            case '_inline':\n                break;\n            // Boolean attributes\n            case 'inline':\n            case 'hFlip':\n            case 'vFlip':\n                customisations[key] =\n                    value === true || value === 'true' || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case 'flip':\n                if (typeof value === 'string') {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Alignment as string\n            case 'align':\n                if (typeof value === 'string') {\n                    alignmentFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case 'color':\n                style.color = value;\n                break;\n            // Rotation as string\n            case 'rotate':\n                if (typeof value === 'string') {\n                    customisations[key] = rotateFromString(value);\n                }\n                else if (typeof value === 'number') {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case 'ariaHidden':\n            case 'aria-hidden':\n                if (value !== true && value !== 'true') {\n                    delete componentProps['aria-hidden'];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    // Counter for ids based on \"id\" property to render icons consistently on server and client\n    let localCounter = 0;\n    let id = props.id;\n    if (typeof id === 'string') {\n        // Convert '-' to '_' to avoid errors in animations\n        id = id.replace(/-/g, '_');\n    }\n    // Add icon stuff\n    componentProps.dangerouslySetInnerHTML = {\n        __html: replaceIDs(item.body, id ? () => id + 'ID' + localCounter++ : 'iconifyReact'),\n    };\n    for (let key in item.attributes) {\n        componentProps[key] = item.attributes[key];\n    }\n    if (item.inline && style.verticalAlign === void 0) {\n        style.verticalAlign = '-0.125em';\n    }\n    return React.createElement('svg', componentProps);\n};\n\n/**\n * Enable cache\n */\nfunction enableCache(storage) {\n    toggleBrowserCache(storage, true);\n}\n/**\n * Disable cache\n */\nfunction disableCache(storage) {\n    toggleBrowserCache(storage, false);\n}\n/**\n * Initialise stuff\n */\n// Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    // Set cache and load existing cache\n    cache.store = storeCache;\n    loadCache();\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = 'Invalid IconifyPreload syntax.';\n        if (typeof preload === 'object' && preload !== null) {\n            (preload instanceof Array ? preload : [preload]).forEach((item) => {\n                try {\n                    if (\n                    // Check if item is an object and not null/array\n                    typeof item !== 'object' ||\n                        item === null ||\n                        item instanceof Array ||\n                        // Check for 'icons' and 'prefix'\n                        typeof item.icons !== 'object' ||\n                        typeof item.prefix !== 'string' ||\n                        // Add icon set\n                        !addCollection(item)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === 'object' && providers !== null) {\n            for (let key in providers) {\n                const err = 'IconifyProviders[' + key + '] is invalid.';\n                try {\n                    const value = providers[key];\n                    if (typeof value !== 'object' ||\n                        !value ||\n                        value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\nclass IconComponent extends React.Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            // Render placeholder before component is mounted\n            icon: null,\n        };\n    }\n    /**\n     * Abort loading icon\n     */\n    _abortLoading() {\n        if (this._loading) {\n            this._loading.abort();\n            this._loading = null;\n        }\n    }\n    /**\n     * Update state\n     */\n    _setData(icon) {\n        if (this.state.icon !== icon) {\n            this.setState({\n                icon,\n            });\n        }\n    }\n    /**\n     * Check if icon should be loaded\n     */\n    _checkIcon(changed) {\n        const state = this.state;\n        const icon = this.props.icon;\n        // Icon is an object\n        if (typeof icon === 'object' &&\n            icon !== null &&\n            typeof icon.body === 'string') {\n            // Stop loading\n            this._icon = '';\n            this._abortLoading();\n            if (changed || state.icon === null) {\n                // Set data if it was changed\n                this._setData({\n                    data: fullIcon(icon),\n                });\n            }\n            return;\n        }\n        // Invalid icon?\n        let iconName;\n        if (typeof icon !== 'string' ||\n            (iconName = stringToIcon(icon, false, true)) === null) {\n            this._abortLoading();\n            this._setData(null);\n            return;\n        }\n        // Load icon\n        const data = getIconData(iconName);\n        if (data === null) {\n            // Icon needs to be loaded\n            if (!this._loading || this._loading.name !== icon) {\n                // New icon to load\n                this._abortLoading();\n                this._icon = '';\n                this._setData(null);\n                this._loading = {\n                    name: icon,\n                    abort: loadIcons([iconName], this._checkIcon.bind(this, false)),\n                };\n            }\n            return;\n        }\n        // Icon data is available\n        if (this._icon !== icon || state.icon === null) {\n            // New icon or icon has been loaded\n            this._abortLoading();\n            this._icon = icon;\n            // Add classes\n            const classes = ['iconify'];\n            if (iconName.prefix !== '') {\n                classes.push('iconify--' + iconName.prefix);\n            }\n            if (iconName.provider !== '') {\n                classes.push('iconify--' + iconName.provider);\n            }\n            // Set data\n            this._setData({\n                data,\n                classes,\n            });\n            if (this.props.onLoad) {\n                this.props.onLoad(icon);\n            }\n        }\n    }\n    /**\n     * Component mounted\n     */\n    componentDidMount() {\n        this._checkIcon(false);\n    }\n    /**\n     * Component updated\n     */\n    componentDidUpdate(oldProps) {\n        if (oldProps.icon !== this.props.icon) {\n            this._checkIcon(true);\n        }\n    }\n    /**\n     * Abort loading\n     */\n    componentWillUnmount() {\n        this._abortLoading();\n    }\n    /**\n     * Render\n     */\n    render() {\n        const props = this.props;\n        const icon = this.state.icon;\n        if (icon === null) {\n            // Render placeholder\n            return props.children\n                ? props.children\n                : React.createElement('span', {});\n        }\n        // Add classes\n        let newProps = props;\n        if (icon.classes) {\n            newProps = {\n                ...props,\n                className: (typeof props.className === 'string'\n                    ? props.className + ' '\n                    : '') + icon.classes.join(' '),\n            };\n        }\n        // Render icon\n        return render(icon.data, newProps, props._inline, props._ref);\n    }\n}\n/**\n * Block icon\n *\n * @param props - Component properties\n */\nconst Icon = React.forwardRef(function Icon(props, ref) {\n    const newProps = {\n        ...props,\n        _ref: ref,\n        _inline: false,\n    };\n    return React.createElement(IconComponent, newProps);\n});\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */\nconst InlineIcon = React.forwardRef(function InlineIcon(props, ref) {\n    const newProps = { ...props, _ref: ref, _inline: true };\n    return React.createElement(IconComponent, newProps);\n});\n/**\n * Internal API\n */\nconst _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders,\n    mergeParams,\n};\n\nexport { Icon, InlineIcon, _api, addAPIProvider, addCollection, addIcon, buildIcon, calculateSize, disableCache, enableCache, getIcon, iconExists, listIcons, loadIcon, loadIcons, replaceIDs, shareStorage };\n", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"component\", \"direction\", \"spacing\", \"divider\", \"children\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { createUnarySpacing, getValue, handleBreakpoints, mergeBreakpointsInOrder, unstable_extendSxProp as extendSxProp, unstable_resolveBreakpointValues as resolveBreakpointValues } from '@mui/system';\nimport { deepmerge } from '@mui/utils';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\n\n/**\n * Return an array with the separator React element interspersed between\n * each React node of the input children.\n *\n * > joinChildren([1,2,3], 0)\n * [1,0,2,0,3]\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction joinChildren(children, separator) {\n  const childrenArray = React.Children.toArray(children).filter(Boolean);\n  return childrenArray.reduce((output, child, index) => {\n    output.push(child);\n    if (index < childrenArray.length - 1) {\n      output.push( /*#__PURE__*/React.cloneElement(separator, {\n        key: `separator-${index}`\n      }));\n    }\n    return output;\n  }, []);\n}\nconst getSideFromDirection = direction => {\n  return {\n    row: 'Left',\n    'row-reverse': 'Right',\n    column: 'Top',\n    'column-reverse': 'Bottom'\n  }[direction];\n};\nexport const style = ({\n  ownerState,\n  theme\n}) => {\n  let styles = _extends({\n    display: 'flex',\n    flexDirection: 'column'\n  }, handleBreakpoints({\n    theme\n  }, resolveBreakpointValues({\n    values: ownerState.direction,\n    breakpoints: theme.breakpoints.values\n  }), propValue => ({\n    flexDirection: propValue\n  })));\n  if (ownerState.spacing) {\n    const transformer = createUnarySpacing(theme);\n    const base = Object.keys(theme.breakpoints.values).reduce((acc, breakpoint) => {\n      if (typeof ownerState.spacing === 'object' && ownerState.spacing[breakpoint] != null || typeof ownerState.direction === 'object' && ownerState.direction[breakpoint] != null) {\n        acc[breakpoint] = true;\n      }\n      return acc;\n    }, {});\n    const directionValues = resolveBreakpointValues({\n      values: ownerState.direction,\n      base\n    });\n    const spacingValues = resolveBreakpointValues({\n      values: ownerState.spacing,\n      base\n    });\n    if (typeof directionValues === 'object') {\n      Object.keys(directionValues).forEach((breakpoint, index, breakpoints) => {\n        const directionValue = directionValues[breakpoint];\n        if (!directionValue) {\n          const previousDirectionValue = index > 0 ? directionValues[breakpoints[index - 1]] : 'column';\n          directionValues[breakpoint] = previousDirectionValue;\n        }\n      });\n    }\n    const styleFromPropValue = (propValue, breakpoint) => {\n      return {\n        '& > :not(style) + :not(style)': {\n          margin: 0,\n          [`margin${getSideFromDirection(breakpoint ? directionValues[breakpoint] : ownerState.direction)}`]: getValue(transformer, propValue)\n        }\n      };\n    };\n    styles = deepmerge(styles, handleBreakpoints({\n      theme\n    }, spacingValues, styleFromPropValue));\n  }\n  styles = mergeBreakpointsInOrder(theme.breakpoints, styles);\n  return styles;\n};\nconst StackRoot = styled('div', {\n  name: 'MuiStack',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [styles.root];\n  }\n})(style);\nconst Stack = /*#__PURE__*/React.forwardRef(function Stack(inProps, ref) {\n  const themeProps = useThemeProps({\n    props: inProps,\n    name: 'MuiStack'\n  });\n  const props = extendSxProp(themeProps);\n  const {\n      component = 'div',\n      direction = 'column',\n      spacing = 0,\n      divider,\n      children\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = {\n    direction,\n    spacing\n  };\n  return /*#__PURE__*/_jsx(StackRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: divider ? joinChildren(children, divider) : children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stack;"], "sourceRoot": ""}