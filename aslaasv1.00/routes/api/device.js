const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const { imageUpload } = require('../../utils/FileUploader');
const admin = require('../../middleware/admin');
const {
    driverConfirm,
    getConnectedMqttClients,
    getDevices,
    getAll,
    editDevice,
    myDevice,
    setByAdmin,
    configDevice,
    orderInfo,
    driverInfo,
    orderConfirm,
    configDriver,
    configDelete,
    register,
    deleteDevice,
    updateDevice,
    controlAnalog,
    controlDevice,
    changeActive,
    checkLine,
    setGpsTime,
    switchSubscribe,
    addBleDevice,
    removeBleDevice,
    getLocations,
    searchRentCars,
    searchRentMopeds,
    extendsBalance,
    finishRent,
    requestRent,
    setDriverProfile,
    listAll,
    sendSimCheckCommand,
    requestRentMoped,
    finishRentMoped,
    deleteMultipleDevices,
    updateDeviceRenter,

} = require('../../controller/deviceController');
const { sendMqtt,publish } = require('../../utils/channel');


/******************** */


router.post('/driver-confirm', auth, driverConfirm)

router.get('/get-connections', auth, getConnectedMqttClients)
router.post('/my-device', auth, myDevice)
router.post('/gets', auth, getAll)
router.post('/list/all', auth, listAll)
router.post('/edit/:id', auth, editDevice)

router.post('/set-by-admin/:id', admin, setByAdmin);
router.post('/configure', auth, configDevice);

router.post('/order-info', auth, orderInfo)
router.post('/driver-info', auth, driverInfo)
router.post('/order-confirm', auth, orderConfirm);

router.post('/configured-drivers', auth, configDriver);

router.post('/configured-delete', auth, configDelete);

router.post('/register', auth, register);
router.post('/delete', auth, deleteDevice)
router.post('/set/:id', auth, updateDevice);
router.post('/delete-multiple', auth, deleteMultipleDevices);

router.post("/control/analog", auth, controlAnalog);
router.post('/control/:cmd', auth, controlDevice);
router.post('/change-active', auth, changeActive)

router.post('/checkline', auth, checkLine)

router.post('/set-gps-time', auth, setGpsTime)

router.post('/switch-subscribe', auth, switchSubscribe)

router.post('/add-ble-device', auth, addBleDevice)
router.post('/remove-ble-device', auth, removeBleDevice)

// for admin part get all locations from devices 
router.post('/get-locations', auth, getLocations);

router.get('/search-rent-cars', auth, searchRentCars)
router.get('/search-rent-mopeds', auth, searchRentMopeds)

router.post("/extend-balance", auth, extendsBalance);

router.post('/finish-rent', auth, finishRent)
router.post('/finish-rent-moped', auth, finishRentMoped)

router.post('/request-rent', auth, requestRent)
router.post('/request-rent-moped', auth, requestRentMoped)

router.post('/set-driver-profile', auth, [
        imageUpload.single('driverLicenseFile')
    ],
    setDriverProfile
);
router.post("/check-sim", auth, sendSimCheckCommand);
router.post('/publish',auth, publish)

router.post('/update-renter', auth, updateDeviceRenter);

module.exports = router;
