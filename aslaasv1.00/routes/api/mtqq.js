const express = require('express');
const router = express.Router();
const { sendMessageToChannel } = require('../../utils/socket');
// from sms iot
router.post('/hook', async(req, res) => {
    try {
        sendMessageToChannel(req.body.from_client_id, req.body, "4g");
        return res.status(200).json({ data: req.body });

    } catch (err) {
        console.log('err', err);
        return res.status(400).json(err);
    }

});
module.exports = router;