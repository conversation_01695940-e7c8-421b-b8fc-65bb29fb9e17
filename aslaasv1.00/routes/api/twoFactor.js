const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const { 
    twoFactorRateLimit, 
    twoFactorSetupRateLimit, 
    backupCodeRateLimit 
} = require('../../middleware/rateLimiter');

const {
    setup2FA,
    enable2FA,
    verify2FA,
    disable2FA,
    get2FAStatus,
    generateNewBackupCodes
} = require('../../controller/twoFactorController');

// Get 2FA status for current user
router.get('/status', auth, get2FAStatus);

// Setup 2FA - Generate secret and QR code (rate limiting temporarily disabled for testing)
router.post('/setup', auth, setup2FA);

// Enable 2FA after verifying TOTP token
router.post('/enable', auth, twoFactorRateLimit, enable2FA);

// Verify 2FA token during login (public endpoint with rate limiting)
router.post('/verify', twoFactorRateLimit, verify2FA);

// Disable 2FA
router.post('/disable', auth, twoFactorRateLimit, disable2FA);

// Generate new backup codes
router.post('/backup-codes', auth, backupCodeRateLimit, generateNewBackupCodes);

module.exports = router;
