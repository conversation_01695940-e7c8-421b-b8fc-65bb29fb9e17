const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const { imageUpload } = require('../../utils/FileUploader');
const admin = require('../../middleware/admin');
const {
    createBike,
    getAllBikes,
    getBikeById,
    updateBikeById,
    deleteBikeById,
    createBikeMessageLog,
    searchRentBikes,
    requestRentBike,
    finishRent
} = require('../../controller/bikeController');
// Create a new bike
router.post('/create-bike', auth, createBike);

// Get all bikes
router.get('/get-allbike', auth, getAllBikes);

// Get a bike by ID
router.get('/get-bike-byid', auth, getBikeById);

// Update a bike by ID
router.patch('/update-bike-byid', auth, updateBikeById);

// Delete a bike by ID
router.delete('/delete-bike-byid', auth, deleteBikeById);

// Create a new bike message log
router.post('/create-bike-messagelog', auth, createBikeMessageLog);

router.get('/search-rent-bikes', auth, searchRentBikes);
router.post('/request-rent-bike', auth, requestRentBike);
router.post('/finish-rent-bike', auth, finishRent);
module.exports = router;