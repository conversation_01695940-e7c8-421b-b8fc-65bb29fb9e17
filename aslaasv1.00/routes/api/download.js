const express = require("express");
const router = express.Router();
const path = require('path');
const fs = require('fs');

router.get('/android-app', (req, res) => {
    try {
        const filePath = path.resolve(__dirname, '../../uploads/app/android-app.apk');
        if (fs.existsSync(filePath)) {
            // Set appropriate headers
            res.setHeader('Content-Type', 'application/vnd.android.package-archive');
            res.setHeader('Content-Disposition', 'attachment; filename=android-app.apk');
            
            // Stream the file instead of using res.download
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        } else {
            res.status(404).send(`File not found at: ${filePath}`);
        }
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

router.post('/android', (req, res) => {
    try {
        const filePath = path.resolve(__dirname, '../../uploads/app/android-app.apk');
        if (fs.existsSync(filePath)) {
            res.setHeader('Content-Type', 'application/vnd.android.package-archive');
            res.setHeader('Content-Disposition', 'attachment; filename=android-app.apk');
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        } else {
            res.status(404).send(`File not found at: ${filePath}`);
        }
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

router.post('/ios', (req, res) => {
    console.log("ios");
    res.json({success:true});
});

module.exports = router;
