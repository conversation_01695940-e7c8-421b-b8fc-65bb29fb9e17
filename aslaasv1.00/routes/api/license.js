const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth')
const { extendLicense, deleteLicense, getLicenses } = require('../../controller/licenseController');


/****************** */

router.post("/extend-license", auth, extendLicense);
router.post("/delete",
    auth,
    deleteLicense
)
router.post("/list",
    auth,
    getLicenses
)
module.exports = router;