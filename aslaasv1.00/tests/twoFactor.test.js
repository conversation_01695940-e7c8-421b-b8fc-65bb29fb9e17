const request = require('supertest');
const mongoose = require('mongoose');
const speakeasy = require('speakeasy');
const app = require('../server'); // Assuming your main app file exports the Express app
const User = require('../models/user');
const { encrypt, decrypt, generateSecureRandom } = require('../utils/security');

// Test database connection
const MONGODB_URI = process.env.TEST_DATABASE_URL || 'mongodb://localhost:27017/aslaa_test';

describe('Two-Factor Authentication', () => {
  let testUser;
  let authToken;
  let twoFactorSecret;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  });

  afterAll(async () => {
    // Clean up and close database connection
    await User.deleteMany({});
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Create a test user
    testUser = new User({
      phoneNumber: '99887766',
      username: 'testuser',
      pinCode: '1234',
      status: 'active',
      role: 'user'
    });
    await testUser.save();

    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ phoneNumber: '99887766' });

    const pinResponse = await request(app)
      .post('/api/auth/pincode')
      .send({ 
        phoneNumber: '99887766',
        pinCode: '1234'
      });

    authToken = pinResponse.body.token;
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({});
  });

  describe('2FA Setup', () => {
    test('should initiate 2FA setup successfully', async () => {
      const response = await request(app)
        .post('/api/2fa/setup')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('secret');
      expect(response.body.data).toHaveProperty('qrCode');
      expect(response.body.data).toHaveProperty('manualEntryKey');

      // Verify secret is stored encrypted in database
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.twoFactorSecret).toBeDefined();
      expect(updatedUser.twoFactorSecret).not.toBe(response.body.data.secret);
      expect(updatedUser.twoFactorEnabled).toBe(false);
    });

    test('should not allow setup if 2FA is already enabled', async () => {
      // Enable 2FA first
      testUser.twoFactorEnabled = true;
      await testUser.save();

      const response = await request(app)
        .post('/api/2fa/setup')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already enabled');
    });

    test('should require authentication for setup', async () => {
      const response = await request(app)
        .post('/api/2fa/setup')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('2FA Enable', () => {
    beforeEach(async () => {
      // Setup 2FA secret
      const secret = speakeasy.generateSecret({
        name: `ASLAA (${testUser.phoneNumber})`,
        issuer: 'ASLAA Car Control System',
        length: 32
      });
      
      twoFactorSecret = secret.base32;
      testUser.twoFactorSecret = encrypt(secret.base32);
      await testUser.save();
    });

    test('should enable 2FA with valid TOTP token', async () => {
      const token = speakeasy.totp({
        secret: twoFactorSecret,
        encoding: 'base32'
      });

      const response = await request(app)
        .post('/api/2fa/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ token })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('backupCodes');
      expect(response.body.data.backupCodes).toHaveLength(10);

      // Verify 2FA is enabled in database
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.twoFactorEnabled).toBe(true);
      expect(updatedUser.twoFactorEnabledAt).toBeDefined();
      expect(updatedUser.backupCodes).toHaveLength(10);
    });

    test('should reject invalid TOTP token', async () => {
      const response = await request(app)
        .post('/api/2fa/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ token: '123456' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid');
    });

    test('should reject malformed token', async () => {
      const response = await request(app)
        .post('/api/2fa/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ token: 'abc123' })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('should require setup before enabling', async () => {
      // Remove the secret
      testUser.twoFactorSecret = null;
      await testUser.save();

      const response = await request(app)
        .post('/api/2fa/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ token: '123456' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('setup not initiated');
    });
  });

  describe('2FA Verification', () => {
    beforeEach(async () => {
      // Setup and enable 2FA
      const secret = speakeasy.generateSecret({
        name: `ASLAA (${testUser.phoneNumber})`,
        issuer: 'ASLAA Car Control System',
        length: 32
      });
      
      twoFactorSecret = secret.base32;
      testUser.twoFactorSecret = encrypt(secret.base32);
      testUser.twoFactorEnabled = true;
      testUser.twoFactorEnabledAt = new Date();
      testUser.backupCodes = [
        { code: 'ABCD1234', used: false, usedAt: null },
        { code: 'EFGH5678', used: false, usedAt: null }
      ];
      await testUser.save();
    });

    test('should verify valid TOTP token', async () => {
      const token = speakeasy.totp({
        secret: twoFactorSecret,
        encoding: 'base32'
      });

      const response = await request(app)
        .post('/api/2fa/verify')
        .send({ 
          phoneNumber: testUser.phoneNumber,
          token 
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.verified).toBe(true);
    });

    test('should verify valid backup code', async () => {
      const response = await request(app)
        .post('/api/2fa/verify')
        .send({ 
          phoneNumber: testUser.phoneNumber,
          token: 'ABCD1234',
          isBackupCode: true
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.verified).toBe(true);

      // Verify backup code is marked as used
      const updatedUser = await User.findById(testUser._id);
      const usedCode = updatedUser.backupCodes.find(bc => bc.code === 'ABCD1234');
      expect(usedCode.used).toBe(true);
      expect(usedCode.usedAt).toBeDefined();
    });

    test('should reject used backup code', async () => {
      // Mark backup code as used
      testUser.backupCodes[0].used = true;
      testUser.backupCodes[0].usedAt = new Date();
      await testUser.save();

      const response = await request(app)
        .post('/api/2fa/verify')
        .send({ 
          phoneNumber: testUser.phoneNumber,
          token: 'ABCD1234',
          isBackupCode: true
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('should reject invalid TOTP token', async () => {
      const response = await request(app)
        .post('/api/2fa/verify')
        .send({ 
          phoneNumber: testUser.phoneNumber,
          token: '123456'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('should prevent TOTP token reuse', async () => {
      const token = speakeasy.totp({
        secret: twoFactorSecret,
        encoding: 'base32'
      });

      // First verification should succeed
      await request(app)
        .post('/api/2fa/verify')
        .send({ 
          phoneNumber: testUser.phoneNumber,
          token 
        })
        .expect(200);

      // Second verification with same token should fail
      const response = await request(app)
        .post('/api/2fa/verify')
        .send({ 
          phoneNumber: testUser.phoneNumber,
          token 
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already been used');
    });
  });

  describe('2FA Status', () => {
    test('should return correct status when 2FA is disabled', async () => {
      const response = await request(app)
        .get('/api/2fa/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.twoFactorEnabled).toBe(false);
      expect(response.body.data.unusedBackupCodes).toBe(0);
    });

    test('should return correct status when 2FA is enabled', async () => {
      // Enable 2FA
      testUser.twoFactorEnabled = true;
      testUser.twoFactorEnabledAt = new Date();
      testUser.backupCodes = [
        { code: 'ABCD1234', used: false, usedAt: null },
        { code: 'EFGH5678', used: true, usedAt: new Date() }
      ];
      await testUser.save();

      const response = await request(app)
        .get('/api/2fa/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.twoFactorEnabled).toBe(true);
      expect(response.body.data.unusedBackupCodes).toBe(1);
      expect(response.body.data.twoFactorEnabledAt).toBeDefined();
    });
  });

  describe('Security Utils', () => {
    test('should encrypt and decrypt data correctly', () => {
      const originalText = 'test-secret-key';
      const encrypted = encrypt(originalText);
      const decrypted = decrypt(encrypted);

      expect(encrypted).not.toBe(originalText);
      expect(decrypted).toBe(originalText);
    });

    test('should generate secure random strings', () => {
      const random1 = generateSecureRandom(8);
      const random2 = generateSecureRandom(8);

      expect(random1).toHaveLength(8);
      expect(random2).toHaveLength(8);
      expect(random1).not.toBe(random2);
      expect(/^[A-Z0-9]+$/.test(random1)).toBe(true);
    });
  });

  describe('Rate Limiting', () => {
    test('should apply rate limiting to 2FA verification', async () => {
      // Make multiple failed attempts
      const promises = [];
      for (let i = 0; i < 6; i++) {
        promises.push(
          request(app)
            .post('/api/2fa/verify')
            .send({ 
              phoneNumber: testUser.phoneNumber,
              token: '123456'
            })
        );
      }

      const responses = await Promise.all(promises);
      
      // First 5 should be processed (even if they fail)
      // 6th should be rate limited
      const rateLimitedResponse = responses[5];
      expect(rateLimitedResponse.status).toBe(429);
      expect(rateLimitedResponse.body.message).toContain('Too many');
    });
  });
});

module.exports = {
  // Export test utilities if needed
};
